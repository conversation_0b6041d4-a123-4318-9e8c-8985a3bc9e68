<?php
/**
 * فایل تست سیستم RBAC
 * این فایل عملکرد سیستم کنترل دسترسی مبتنی بر نقش را تست می‌کند
 */

require_once __DIR__ . '/../db_connection.php';
$pdo = db_connect();
require_once __DIR__ . '/../includes/PermissionSystem.php';

// تنظیم منطقه زمانی
date_default_timezone_set('Asia/Tehran');

// ایجاد نمونه سیستم مجوزها
$permission_system = new PermissionSystem($pdo);

echo "<h1>تست سیستم RBAC</h1>\n";

// تابع کمکی برای نمایش نتایج تست
function test_result($test_name, $result, $expected = true) {
    $status = ($result === $expected) ? '✅ موفق' : '❌ ناموفق';
    $color = ($result === $expected) ? 'green' : 'red';
    echo "<div style='color: $color; margin: 5px 0;'>$status - $test_name</div>\n";
    return ($result === $expected);
}

$passed_tests = 0;
$total_tests = 0;

try {
    echo "<h2>تست 1: بررسی وجود جداول</h2>\n";
    
    // تست وجود جداول
    $tables = ['roles', 'system_pages', 'permissions', 'role_permissions', 'users'];
    foreach ($tables as $table) {
        $total_tests++;
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = (bool)$stmt->fetch();
        if (test_result("جدول $table موجود است", $exists)) {
            $passed_tests++;
        }
    }
    
    echo "<h2>تست 2: بررسی داده‌های اولیه</h2>\n";
    
    // تست وجود نقش‌های پایه
    $base_roles = ['admin', 'user', 'outsource'];
    foreach ($base_roles as $role) {
        $total_tests++;
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM roles WHERE name = ? AND is_active = 1");
        $stmt->execute([$role]);
        $exists = $stmt->fetchColumn() > 0;
        if (test_result("نقش $role موجود است", $exists)) {
            $passed_tests++;
        }
    }
    
    // تست وجود صفحات سیستم
    $system_pages = ['dashboard', 'devices', 'activities', 'work_order', 'reports_list', 'my_tasks', 'user_management'];
    foreach ($system_pages as $page) {
        $total_tests++;
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_pages WHERE name = ? AND is_active = 1");
        $stmt->execute([$page]);
        $exists = $stmt->fetchColumn() > 0;
        if (test_result("صفحه $page موجود است", $exists)) {
            $passed_tests++;
        }
    }
    
    echo "<h2>تست 3: بررسی مجوزها</h2>\n";
    
    // تست وجود مجوزها برای هر صفحه
    $permission_types = ['view', 'create', 'edit', 'delete'];
    foreach ($system_pages as $page) {
        $stmt = $pdo->prepare("SELECT id FROM system_pages WHERE name = ?");
        $stmt->execute([$page]);
        $page_id = $stmt->fetchColumn();
        
        if ($page_id) {
            foreach ($permission_types as $perm_type) {
                $total_tests++;
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM permissions WHERE page_id = ? AND name = ?");
                $stmt->execute([$page_id, $perm_type]);
                $exists = $stmt->fetchColumn() > 0;
                if (test_result("مجوز $perm_type برای صفحه $page موجود است", $exists)) {
                    $passed_tests++;
                }
            }
        }
    }
    
    echo "<h2>تست 4: بررسی عملکرد کلاس PermissionSystem</h2>\n";
    
    // تست متدهای کلاس PermissionSystem
    $total_tests++;
    $active_roles = $permission_system->get_active_roles();
    if (test_result("دریافت نقش‌های فعال", is_array($active_roles) && count($active_roles) > 0)) {
        $passed_tests++;
    }
    
    $total_tests++;
    $active_pages = $permission_system->get_active_pages();
    if (test_result("دریافت صفحات فعال", is_array($active_pages) && count($active_pages) > 0)) {
        $passed_tests++;
    }
    
    // تست مجوزهای نقش admin
    $admin_role = $pdo->query("SELECT id FROM roles WHERE name = 'admin'")->fetch(PDO::FETCH_ASSOC);
    if ($admin_role) {
        $total_tests++;
        $admin_permissions = $permission_system->get_role_permissions($admin_role['id']);
        if (test_result("دریافت مجوزهای نقش admin", is_array($admin_permissions) && count($admin_permissions) > 0)) {
            $passed_tests++;
        }
    }
    
    echo "<h2>تست 5: بررسی کاربران</h2>\n";
    
    // تست انتقال کاربران
    $total_tests++;
    $users_with_role = $pdo->query("SELECT COUNT(*) FROM users WHERE role_id IS NOT NULL")->fetchColumn();
    $total_users = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    if (test_result("انتقال کاربران به سیستم جدید", $users_with_role == $total_users && $total_users > 0)) {
        $passed_tests++;
    }
    
    // تست فعال بودن کاربران
    $total_tests++;
    $active_users = $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn();
    if (test_result("وجود کاربران فعال", $active_users > 0)) {
        $passed_tests++;
    }
    
    echo "<h2>تست 6: بررسی روابط جداول</h2>\n";
    
    // تست روابط foreign key
    $total_tests++;
    $stmt = $pdo->query("
        SELECT COUNT(*) 
        FROM users u 
        JOIN roles r ON u.role_id = r.id 
        WHERE u.role_id IS NOT NULL
    ");
    $valid_relations = $stmt->fetchColumn();
    if (test_result("روابط معتبر users-roles", $valid_relations == $users_with_role)) {
        $passed_tests++;
    }
    
    $total_tests++;
    $stmt = $pdo->query("
        SELECT COUNT(*) 
        FROM role_permissions rp 
        JOIN roles r ON rp.role_id = r.id 
        JOIN permissions p ON rp.permission_id = p.id
    ");
    $valid_role_permissions = $stmt->fetchColumn();
    if (test_result("روابط معتبر role_permissions", $valid_role_permissions > 0)) {
        $passed_tests++;
    }
    
    echo "<h2>تست 7: بررسی امنیت</h2>\n";
    
    // تست عدم وجود کاربران بدون نقش
    $total_tests++;
    $users_without_role = $pdo->query("SELECT COUNT(*) FROM users WHERE role_id IS NULL")->fetchColumn();
    if (test_result("عدم وجود کاربران بدون نقش", $users_without_role == 0)) {
        $passed_tests++;
    }
    
    // تست وجود حداقل یک مدیر
    $total_tests++;
    $admin_users = $pdo->query("
        SELECT COUNT(*) 
        FROM users u 
        JOIN roles r ON u.role_id = r.id 
        WHERE r.name = 'admin' AND u.is_active = 1
    ")->fetchColumn();
    if (test_result("وجود حداقل یک مدیر فعال", $admin_users > 0)) {
        $passed_tests++;
    }
    
    echo "<h2>نتایج نهایی</h2>\n";
    
    $success_rate = round(($passed_tests / $total_tests) * 100, 2);
    $color = ($success_rate >= 90) ? 'green' : (($success_rate >= 70) ? 'orange' : 'red');
    
    echo "<div style='background: #f0f0f0; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<h3>خلاصه تست‌ها:</h3>\n";
    echo "<div style='font-size: 18px; margin: 10px 0;'>تعداد کل تست‌ها: <strong>$total_tests</strong></div>\n";
    echo "<div style='font-size: 18px; margin: 10px 0; color: green;'>تست‌های موفق: <strong>$passed_tests</strong></div>\n";
    echo "<div style='font-size: 18px; margin: 10px 0; color: red;'>تست‌های ناموفق: <strong>" . ($total_tests - $passed_tests) . "</strong></div>\n";
    echo "<div style='font-size: 20px; margin: 15px 0; color: $color;'>درصد موفقیت: <strong>$success_rate%</strong></div>\n";
    echo "</div>\n";
    
    if ($success_rate >= 90) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 20px 0;'>\n";
        echo "<h3>✅ سیستم RBAC با موفقیت نصب و پیکربندی شده است!</h3>\n";
        echo "<p>تمام تست‌های اصلی با موفقیت انجام شدند. سیستم آماده استفاده است.</p>\n";
        echo "</div>\n";
    } elseif ($success_rate >= 70) {
        echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 20px 0;'>\n";
        echo "<h3>⚠️ سیستم RBAC نصب شده اما نیاز به بررسی دارد</h3>\n";
        echo "<p>برخی تست‌ها ناموفق بودند. لطفاً مشکلات را بررسی و برطرف کنید.</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;'>\n";
        echo "<h3>❌ مشکلات جدی در سیستم RBAC</h3>\n";
        echo "<p>تعداد زیادی از تست‌ها ناموفق بودند. لطفاً نصب را دوباره بررسی کنید.</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<h3>❌ خطا در اجرای تست‌ها</h3>\n";
    echo "<p>خطا: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست سیستم RBAC</title>
    <style>
        body {
            font-family: 'Tahoma', sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        h1, h2, h3 {
            color: #333;
        }
        
        .test-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <strong>نکته:</strong> این فایل تست عملکرد سیستم RBAC را بررسی می‌کند. پس از اطمینان از صحت عملکرد، آن را از دسترسی عمومی خارج کنید.
    </div>
    
    <div class="warning">
        <strong>هشدار امنیتی:</strong> این فایل اطلاعات حساس سیستم را نمایش می‌دهد. فقط برای تست استفاده کنید.
    </div>
</body>
</html>
