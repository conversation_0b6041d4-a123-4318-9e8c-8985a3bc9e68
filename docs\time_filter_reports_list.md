# فیلتر زمانی در صفحه گزارش‌های خرابی

## خلاصه تغییرات

در صفحه `reports_list.php` سیستم فیلتر زمانی اضافه شد که به کاربران امکان مشاهده گزارش‌های خرابی در بازه‌های زمانی مختلف را می‌دهد.

## ویژگی‌های اضافه شده

### 1. دکمه‌های فیلتر زمانی
- **جدید**: فقط گزارش‌های با وضعیت "گزارش شده" (تبدیل به دستورکار نشده)
- **7 روز اخیر**: همه گزارش‌های 7 روز گذشته با هر وضعیتی
- **30 روز اخیر**: همه گزارش‌های 30 روز گذشته با هر وضعیتی  
- **یک سال اخیر**: همه گزارش‌های یک سال گذشته با هر وضعیتی

### 2. نمایش تعداد گزارش‌ها
- هر دکمه فیلتر تعداد گزارش‌های موجود در آن بازه را نمایش می‌دهد
- تعداد بر اساس مجوزهای کاربر محاسبه می‌شود

### 3. پیام اطلاعاتی
- نمایش اطلاعات فیلتر فعال و تعداد گزارش‌های نمایش داده شده
- رنگ‌بندی مناسب برای جلب توجه کاربر

## تغییرات فنی

### Backend (PHP)
```php
// تعیین فیلتر زمانی
$time_period = $_GET['period'] ?? 'new';

if ($time_period === 'new') {
    $main_query_condition = " WHERE br.status = 'گزارش شده'";
} else {
    $days = (int)$time_period;
    $date_condition = " WHERE br.report_datetime >= DATE_SUB(NOW(), INTERVAL $days DAY)";
    $main_query_condition = $date_condition;
}
```

### Frontend (JavaScript)
- مدیریت کلیک روی دکمه‌های فیلتر
- تغییر URL parameter و بارگذاری مجدد صفحه
- تنظیم دکمه active بر اساس URL parameter

### CSS
- استایل‌دهی دکمه‌های فیلتر زمانی
- نمایش responsive در موبایل
- استایل پیام اطلاعاتی

## نحوه استفاده

1. کاربر روی یکی از دکمه‌های فیلتر زمانی کلیک می‌کند
2. صفحه با parameter جدید بارگذاری می‌شود
3. گزارش‌های مربوط به بازه زمانی انتخابی نمایش داده می‌شوند
4. پیام اطلاعاتی نوع فیلتر فعال را نشان می‌دهد

## مجوزهای کاربر

سیستم فیلتر زمانی مجوزهای موجود کاربر را رعایت می‌کند:
- کاربران با مجوز `view_all`: همه گزارش‌ها
- کاربران با مجوز `view_own`: فقط گزارش‌های خودشان
- سایر کاربران: هیچ گزارشی

## URL Parameters

- `period=new`: گزارش‌های جدید (پیش‌فرض)
- `period=7`: 7 روز اخیر
- `period=30`: 30 روز اخیر  
- `period=365`: یک سال اخیر

مثال: `reports_list.php?period=30`
