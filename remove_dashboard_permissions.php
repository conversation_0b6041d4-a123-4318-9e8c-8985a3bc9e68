<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "حذف مجوزهای غیرضروری dashboard...\n\n";
    
    // دریافت ID صفحه dashboard
    $page_stmt = $pdo->prepare("SELECT id FROM system_pages WHERE name = ?");
    $page_stmt->execute(['dashboard']);
    $page_id = $page_stmt->fetchColumn();
    
    if (!$page_id) {
        echo "صفحه dashboard یافت نشد!\n";
        exit;
    }
    
    // مجوزهای غیرضروری dashboard
    $unnecessary_permissions = ['create', 'edit', 'delete'];
    
    $total_removed = 0;
    
    foreach ($unnecessary_permissions as $permission_name) {
        // دریافت ID مجوز
        $perm_stmt = $pdo->prepare("SELECT id FROM permissions WHERE page_id = ? AND name = ?");
        $perm_stmt->execute([$page_id, $permission_name]);
        $permission_id = $perm_stmt->fetchColumn();
        
        if ($permission_id) {
            // حذف از role_permissions
            $role_perm_stmt = $pdo->prepare("DELETE FROM role_permissions WHERE permission_id = ?");
            $role_perm_stmt->execute([$permission_id]);
            $role_perm_count = $role_perm_stmt->rowCount();
            
            // حذف از permissions
            $perm_delete_stmt = $pdo->prepare("DELETE FROM permissions WHERE id = ?");
            $perm_delete_stmt->execute([$permission_id]);
            
            echo "✓ حذف مجوز '$permission_name' (تعداد تخصیص‌های حذف شده: $role_perm_count)\n";
            $total_removed++;
        } else {
            echo "- مجوز '$permission_name' یافت نشد\n";
        }
    }
    
    echo "\nتعداد کل مجوزهای حذف شده: $total_removed\n";
    echo "عملیات با موفقیت انجام شد!\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
