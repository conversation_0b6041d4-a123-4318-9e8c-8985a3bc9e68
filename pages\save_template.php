<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'report_builder', 'create')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'عدم دسترسی']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'متد نامعتبر']);
    exit;
}

$name = trim($_POST['name'] ?? '');
$description = trim($_POST['description'] ?? '');
$config = $_POST['config'] ?? '';

if (empty($name)) {
    echo json_encode(['success' => false, 'message' => 'نام قالب الزامی است']);
    exit;
}

if (empty($config)) {
    echo json_encode(['success' => false, 'message' => 'تنظیمات قالب الزامی است']);
    exit;
}

// اعتبارسنجی JSON
$configArray = json_decode($config, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode(['success' => false, 'message' => 'تنظیمات نامعتبر']);
    exit;
}

try {
    // بررسی تکراری نبودن نام قالب
    $stmt = $pdo->prepare("SELECT id FROM report_templates WHERE name = ? AND created_by = ?");
    $stmt->execute([$name, $_SESSION['user_id']]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'قالبی با این نام قبلاً ایجاد شده است']);
        exit;
    }
    
    // ذخیره قالب جدید
    $stmt = $pdo->prepare("
        INSERT INTO report_templates (name, description, config, created_by) 
        VALUES (?, ?, ?, ?)
    ");
    $stmt->execute([
        $name,
        $description ?: 'قالب کاربری',
        $config,
        $_SESSION['user_id']
    ]);
    
    $templateId = $pdo->lastInsertId();
    
    echo json_encode([
        'success' => true, 
        'message' => 'قالب با موفقیت ذخیره شد',
        'template_id' => $templateId
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'خطا در ذخیره قالب: ' . $e->getMessage()
    ]);
}
?>
