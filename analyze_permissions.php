<?php
// تحلیل کامل مجوزهای سیستم
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "<h1>تحلیل کامل مجوزهای سیستم</h1>\n";
    echo "<style>
        body { font-family: Tahoma, sans-serif; direction: rtl; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .page-section { margin: 30px 0; padding: 20px; border: 2px solid #007bff; border-radius: 10px; }
        .permission-list { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .warning { color: red; font-weight: bold; }
        .success { color: green; font-weight: bold; }
    </style>\n";
    
    // دریافت تمام صفحات سیستم
    $pages_query = "SELECT id, name, display_name, file_path FROM system_pages ORDER BY name";
    $pages = $pdo->query($pages_query)->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>صفحات سیستم و مجوزهای آن‌ها:</h2>\n";
    
    foreach ($pages as $page) {
        echo "<div class='page-section'>\n";
        echo "<h3>صفحه: {$page['display_name']} ({$page['name']})</h3>\n";
        echo "<p><strong>مسیر فایل:</strong> {$page['file_path']}</p>\n";
        
        // دریافت مجوزهای این صفحه
        $permissions_query = "
            SELECT p.name, p.display_name, p.description 
            FROM permissions p 
            WHERE p.page_id = ? 
            ORDER BY p.name
        ";
        $stmt = $pdo->prepare($permissions_query);
        $stmt->execute([$page['id']]);
        $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($permissions)) {
            echo "<p class='warning'>⚠️ هیچ مجوزی برای این صفحه تعریف نشده است!</p>\n";
        } else {
            echo "<div class='permission-list'>\n";
            echo "<h4>مجوزهای تعریف شده:</h4>\n";
            echo "<ul>\n";
            foreach ($permissions as $perm) {
                echo "<li><strong>{$perm['name']}</strong> - {$perm['display_name']}";
                if ($perm['description']) {
                    echo " <em>({$perm['description']})</em>";
                }
                echo "</li>\n";
            }
            echo "</ul>\n";
            echo "</div>\n";
        }
        
        // بررسی وجود فایل
        $file_path = "pages/" . $page['file_path'];
        if (file_exists($file_path)) {
            echo "<p class='success'>✅ فایل موجود است</p>\n";
        } else {
            echo "<p class='warning'>❌ فایل موجود نیست: $file_path</p>\n";
        }
        
        echo "</div>\n";
    }
    
    // آمار کلی
    echo "<h2>آمار کلی:</h2>\n";
    $total_pages = count($pages);
    $total_permissions = $pdo->query("SELECT COUNT(*) FROM permissions")->fetchColumn();
    
    echo "<table>\n";
    echo "<tr><th>آیتم</th><th>تعداد</th></tr>\n";
    echo "<tr><td>تعداد کل صفحات</td><td>$total_pages</td></tr>\n";
    echo "<tr><td>تعداد کل مجوزها</td><td>$total_permissions</td></tr>\n";
    echo "</table>\n";
    
    // صفحاتی که مجوز ندارند
    $pages_without_permissions = $pdo->query("
        SELECT sp.name, sp.display_name 
        FROM system_pages sp 
        LEFT JOIN permissions p ON sp.id = p.page_id 
        WHERE p.id IS NULL
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($pages_without_permissions)) {
        echo "<h3 class='warning'>صفحات بدون مجوز:</h3>\n";
        echo "<ul>\n";
        foreach ($pages_without_permissions as $page) {
            echo "<li>{$page['display_name']} ({$page['name']})</li>\n";
        }
        echo "</ul>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='warning'>خطا: " . $e->getMessage() . "</p>\n";
}
?>
