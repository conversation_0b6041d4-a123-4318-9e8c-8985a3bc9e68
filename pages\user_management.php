<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';
$pdo = db_connect();

// بررسی دسترسی به صفحه مدیریت کاربران
require_page_access('user_management', 'view');

// پردازش درخواست‌های AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'create_user':
                require_page_access('user_management', 'create');
                
                $stmt = $pdo->prepare("INSERT INTO users (username, password, name, role_id, is_active) VALUES (?, ?, ?, ?, ?)");
                $result = $stmt->execute([
                    $_POST['username'],
                    password_hash($_POST['password'], PASSWORD_DEFAULT),
                    $_POST['name'],
                    $_POST['role_id'],
                    isset($_POST['is_active']) ? 1 : 0
                ]);
                
                echo json_encode(['success' => $result, 'message' => $result ? 'کاربر با موفقیت ایجاد شد.' : 'خطا در ایجاد کاربر']);
                break;
                
            case 'update_user':
                require_page_access('user_management', 'edit');
                
                $sql = "UPDATE users SET name = ?, role_id = ?, is_active = ?";
                $params = [$_POST['name'], $_POST['role_id'], isset($_POST['is_active']) ? 1 : 0];
                
                if (!empty($_POST['password'])) {
                    $sql .= ", password = ?";
                    $params[] = password_hash($_POST['password'], PASSWORD_DEFAULT);
                }
                
                $sql .= " WHERE id = ?";
                $params[] = $_POST['user_id'];
                
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute($params);
                
                echo json_encode(['success' => $result, 'message' => $result ? 'کاربر با موفقیت به‌روزرسانی شد.' : 'خطا در به‌روزرسانی کاربر']);
                break;
                
            case 'delete_user':
                require_page_access('user_management', 'delete');
                
                $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                $result = $stmt->execute([$_POST['user_id']]);
                
                echo json_encode(['success' => $result, 'message' => $result ? 'کاربر با موفقیت حذف شد.' : 'خطا در حذف کاربر']);
                break;
                
            case 'create_role':
                require_page_access('user_management', 'manage_roles');
                
                $stmt = $pdo->prepare("INSERT INTO roles (name, display_name, description, is_active) VALUES (?, ?, ?, ?)");
                $result = $stmt->execute([
                    $_POST['name'],
                    $_POST['display_name'],
                    $_POST['description'],
                    isset($_POST['is_active']) ? 1 : 0
                ]);
                
                echo json_encode(['success' => $result, 'message' => $result ? 'نقش با موفقیت ایجاد شد.' : 'خطا در ایجاد نقش']);
                break;
                
            case 'update_role':
                require_page_access('user_management', 'manage_roles');
                
                $stmt = $pdo->prepare("UPDATE roles SET display_name = ?, description = ?, is_active = ? WHERE id = ?");
                $result = $stmt->execute([
                    $_POST['display_name'],
                    $_POST['description'],
                    isset($_POST['is_active']) ? 1 : 0,
                    $_POST['role_id']
                ]);
                
                echo json_encode(['success' => $result, 'message' => $result ? 'نقش با موفقیت به‌روزرسانی شد.' : 'خطا در به‌روزرسانی نقش']);
                break;
                
            case 'update_role_permissions':
                require_page_access('user_management', 'manage_roles');
                
                $role_id = $_POST['role_id'];
                $permission_ids = isset($_POST['permissions']) ? $_POST['permissions'] : [];
                
                // حذف مجوزهای قبلی
                $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?")->execute([$role_id]);
                
                // اضافه کردن مجوزهای جدید
                foreach ($permission_ids as $permission_id) {
                    $stmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                    $stmt->execute([$role_id, $permission_id]);
                }

                echo json_encode(['success' => true, 'message' => 'مجوزهای نقش با موفقیت به‌روزرسانی شد.']);
                break;

            case 'delete_role':
                require_page_access('user_management', 'manage_roles');

                $role_id = $_POST['role_id'];

                // بررسی اینکه نقش‌های اصلی حذف نشوند
                $role_name = $pdo->prepare("SELECT name FROM roles WHERE id = ?");
                $role_name->execute([$role_id]);
                $role = $role_name->fetchColumn();

                if (in_array($role, ['admin', 'user', 'outsource'])) {
                    echo json_encode(['success' => false, 'message' => 'نقش‌های اصلی سیستم قابل حذف نیستند.']);
                    break;
                }

                // شمارش کاربران با این نقش
                $user_count_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role_id = ?");
                $user_count_stmt->execute([$role_id]);
                $user_count = $user_count_stmt->fetchColumn();

                // یافتن ID نقش "user" (کاربر عادی)
                $user_role_stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'user'");
                $user_role_stmt->execute();
                $user_role_id = $user_role_stmt->fetchColumn();

                if (!$user_role_id) {
                    echo json_encode(['success' => false, 'message' => 'نقش "کاربر عادی" در سیستم یافت نشد.']);
                    break;
                }

                try {
                    // شروع تراکنش
                    $pdo->beginTransaction();

                    // انتقال کاربران به نقش "user" در صورت وجود
                    if ($user_count > 0) {
                        $transfer_stmt = $pdo->prepare("UPDATE users SET role_id = ? WHERE role_id = ?");
                        $transfer_stmt->execute([$user_role_id, $role_id]);
                    }

                    // حذف مجوزهای نقش
                    $delete_permissions_stmt = $pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?");
                    $delete_permissions_stmt->execute([$role_id]);

                    // حذف نقش
                    $delete_role_stmt = $pdo->prepare("DELETE FROM roles WHERE id = ?");
                    $delete_role_stmt->execute([$role_id]);

                    // تایید تراکنش
                    $pdo->commit();

                    $message = $user_count > 0 
                        ? "نقش با موفقیت حذف شد. $user_count کاربر به نقش 'کاربر عادی' منتقل شدند."
                        : 'نقش با موفقیت حذف شد.';

                    echo json_encode(['success' => true, 'message' => $message]);

                } catch (Exception $e) {
                    // لغو تراکنش در صورت خطا
                    $pdo->rollBack();
                    echo json_encode(['success' => false, 'message' => 'خطا در حذف نقش: ' . $e->getMessage()]);
                }
                break;
                
            case 'get_user':
                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$_POST['user_id']]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode(['success' => true, 'user' => $user]);
                break;
                
            case 'get_role':
                $stmt = $pdo->prepare("SELECT * FROM roles WHERE id = ?");
                $stmt->execute([$_POST['role_id']]);
                $role = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo json_encode(['success' => true, 'role' => $role]);
                break;
                
            case 'get_role_permissions':
                $role_permissions = $permission_system->get_role_permissions($_POST['role_id']);
                $permission_ids = array_column($role_permissions, 'id');
                
                echo json_encode(['success' => true, 'permission_ids' => $permission_ids]);
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'عملیات نامعتبر']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'خطا: ' . $e->getMessage()]);
    }
    exit;
}

// دریافت داده‌ها برای نمایش بر اساس مجوزهای کاربر
$current_user_id = current_user_id();

if (has_permission('user_management', 'view_all_profiles')) {
    // کاربر می‌تواند همه پروفایل‌ها را ببیند
    $users = $pdo->query("
        SELECT u.*, r.display_name as role_display_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        ORDER BY u.name
    ")->fetchAll(PDO::FETCH_ASSOC);
} else {
    // کاربر فقط می‌تواند پروفایل خود را ببیند
    $stmt = $pdo->prepare("
        SELECT u.*, r.display_name as role_display_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        WHERE u.id = ?
        ORDER BY u.name
    ");
    $stmt->execute([$current_user_id]);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

$roles = $permission_system->get_active_roles();
$pages = $permission_system->get_active_pages();

// دریافت تمام مجوزها برای هر صفحه
$all_permissions = [];
foreach ($pages as $page) {
    $all_permissions[$page['id']] = $permission_system->get_page_permissions($page['id']);
}

// محاسبه آمار
$total_users = count($users);
$active_users = count(array_filter($users, function($u) { return $u['is_active']; }));
$inactive_users = $total_users - $active_users;
$total_roles = count($roles);

include '../includes/header.php';
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدیریت کاربران</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --border-radius: 0.5rem;
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
           
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .page-header {
            background: white;
            border-radius: var(--border-radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .page-title i {
            color: var(--primary-color);
            font-size: 2.5rem;
        }

        .page-subtitle {
            margin: 0.5rem 0 0 0;
            color: var(--gray-600);
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 3rem;
            height: 3rem;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.users { background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); }
        .stat-icon.roles { background: linear-gradient(135deg, var(--success-color), #34d399); }
        .stat-icon.active { background: linear-gradient(135deg, var(--info-color), #22d3ee); }
        .stat-icon.inactive { background: linear-gradient(135deg, var(--warning-color), #fbbf24); }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--gray-800);
            line-height: 1;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.9rem;
            font-weight: 500;
        }
        .main-content {
            background: white;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }

        .tabs-container {
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .tabs {
            display: flex;
            overflow-x: auto;
        }

        .tab {
            flex: 1;
            min-width: 200px;
            padding: 1.25rem 1.5rem;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-600);
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab:hover {
            background: var(--gray-100);
            color: var(--primary-color);
        }

        .tab.active {
            color: var(--primary-color);
            background: white;
        }

        .tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .tab-content {
            display: none;
            padding: 2rem;
        }

        .tab-content.active {
            display: block;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .content-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--gray-50);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1.1rem;
        }


        .data-table-container {
            background: white;
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: var(--gray-50);
            padding: 1rem;
            text-align: right;
            font-weight: 700;
            color: var(--gray-700);
            border-bottom: 2px solid var(--gray-200);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .data-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--gray-200);
            color: var(--gray-700);
        }

        .data-table tbody tr {
            transition: all 0.2s ease;
        }

        .data-table tbody tr:hover {
            background: var(--gray-50);
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fef2f2;
            color: #991b1b;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
    </style>
</head>
<body>

<div class="page-container">
    <!-- هدر صفحه -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-users-cog"></i>
            مدیریت کاربران و نقش‌ها
        </h1>
        <p class="page-subtitle">مدیریت کاربران، نقش‌ها و مجوزهای سیستم</p>
    </div>

    <!-- آمار -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon users">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="stat-number"><?= $total_users ?></div>
            <div class="stat-label">کل کاربران</div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon active">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
            <div class="stat-number"><?= $active_users ?></div>
            <div class="stat-label">کاربران فعال</div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon inactive">
                    <i class="fas fa-user-times"></i>
                </div>
            </div>
            <div class="stat-number"><?= $inactive_users ?></div>
            <div class="stat-label">کاربران غیرفعال</div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon roles">
                    <i class="fas fa-user-tag"></i>
                </div>
            </div>
            <div class="stat-number"><?= $total_roles ?></div>
            <div class="stat-label">نقش‌های تعریف شده</div>
        </div>
    </div>

    <!-- محتوای اصلی -->
    <div class="main-content">
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab active" onclick="showTab('users')">
                    <i class="fas fa-users"></i>
                    مدیریت کاربران
                </button>
                <?php if (has_permission('user_management', 'manage_roles')): ?>
                <button class="tab" onclick="showTab('roles')">
                    <i class="fas fa-user-tag"></i>
                    مدیریت نقش‌ها
                </button>
                <?php endif; ?>
            </div>
        </div>

        <!-- تب مدیریت کاربران -->
        <div id="users-tab" class="tab-content active">
            <div class="content-header">
                <h2 class="content-title">لیست کاربران</h2>
                <div class="search-container">
                    <input type="text" class="search-input" id="userSearch" placeholder="جستجو در کاربران...">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <?php if (has_permission('user_management', 'create')): ?>
                <button class="btn btn-primary" onclick="openUserModal()">
                    <i class="fas fa-plus"></i>
                    افزودن کاربر جدید
                </button>
                <?php endif; ?>
            </div>

            <div class="data-table-container">
                <table class="data-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>نام کاربری</th>
                            <th>نام و نام خانوادگی</th>
                            <th>نقش</th>
                            <th>وضعیت</th>
                            <th>تاریخ عضویت</th>
                            <th>عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                    <div style="width: 2rem; height: 2rem; border-radius: 50%; background: var(--primary-color); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">
                                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                    </div>
                                    <strong><?= htmlspecialchars($user['username']) ?></strong>
                                </div>
                            </td>
                            <td><?= htmlspecialchars($user['name']) ?></td>
                            <td>
                                <span style="background: var(--gray-100); color: var(--gray-700); padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.8rem; font-weight: 600;">
                                    <?= htmlspecialchars($user['role_display_name']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($user['is_active']): ?>
                                    <span class="status-badge status-active">
                                        <i class="fas fa-check-circle"></i>
                                        فعال
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge status-inactive">
                                        <i class="fas fa-times-circle"></i>
                                        غیرفعال
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td><?= to_shamsi($user['created_at']) ?></td>
                            <td>
                                <div class="action-buttons">
                                    <?php if (has_permission('user_management', 'edit_all_profiles') || (has_permission('user_management', 'edit_own_profile') && $user['id'] == $current_user_id)): ?>
                                    <button class="btn btn-warning btn-sm" onclick="editUser(<?= $user['id'] ?>)">
                                        <i class="fas fa-edit"></i>
                                        ویرایش
                                    </button>
                                    <?php endif; ?>

                                    <?php if (has_permission('user_management', 'delete') && $user['id'] != $current_user_id): ?>
                                    <button class="btn btn-danger btn-sm" onclick="deleteUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['name']) ?>')">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <?php if (empty($users)): ?>
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>هیچ کاربری یافت نشد</h3>
                    <p>در حال حاضر هیچ کاربری در سیستم وجود ندارد.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- تب مدیریت نقش‌ها -->
        <?php if (has_permission('user_management', 'manage_roles')): ?>
        <div id="roles-tab" class="tab-content">
            <div class="content-header">
                <h2 class="content-title">لیست نقش‌ها</h2>
                <div class="search-container">
                    <input type="text" class="search-input" id="roleSearch" placeholder="جستجو در نقش‌ها...">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <button class="btn btn-success" onclick="openRoleModal()">
                    <i class="fas fa-plus"></i>
                    افزودن نقش جدید
                </button>
            </div>

            <div class="data-table-container">
                <table class="data-table" id="rolesTable">
                    <thead>
                        <tr>
                            <th>نام نقش</th>
                            <th>نام نمایشی</th>
                            <th>توضیحات</th>
                            <th>وضعیت</th>
                            <th>تعداد کاربران</th>
                            <th>عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($roles as $role): ?>
                        <?php
                        $user_count = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role_id = ?");
                        $user_count->execute([$role['id']]);
                        $users_in_role = $user_count->fetchColumn();
                        ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                    <div style="width: 2rem; height: 2rem; border-radius: 50%; background: var(--success-color); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">
                                        <i class="fas fa-user-tag"></i>
                                    </div>
                                    <strong><?= htmlspecialchars($role['name']) ?></strong>
                                </div>
                            </td>
                            <td><?= htmlspecialchars($role['display_name']) ?></td>
                            <td><?= htmlspecialchars($role['description'] ?: 'توضیحی ندارد') ?></td>
                            <td>
                                <?php if ($role['is_active']): ?>
                                    <span class="status-badge status-active">
                                        <i class="fas fa-check-circle"></i>
                                        فعال
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge status-inactive">
                                        <i class="fas fa-times-circle"></i>
                                        غیرفعال
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span style="background: var(--info-color); color: white; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.8rem; font-weight: 600;">
                                    <?= $users_in_role ?> کاربر
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-sm" onclick="manageRolePermissions(<?= $role['id'] ?>, '<?= htmlspecialchars($role['display_name']) ?>')">
                                        <i class="fas fa-key"></i>
                                        مجوزها
                                    </button>
                                    <button class="btn btn-warning btn-sm" onclick="editRole(<?= $role['id'] ?>)">
                                        <i class="fas fa-edit"></i>
                                        ویرایش
                                    </button>
                                    <?php if (!in_array($role['name'], ['admin', 'user', 'outsource'])): ?>
                                    <button class="btn btn-danger btn-sm" onclick="deleteRole(<?= $role['id'] ?>, '<?= htmlspecialchars($role['display_name']) ?>', <?= $users_in_role ?>)">
                                        <i class="fas fa-trash"></i>
                                        حذف
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <?php if (empty($roles)): ?>
                <div class="empty-state">
                    <i class="fas fa-user-tag"></i>
                    <h3>هیچ نقشی یافت نشد</h3>
                    <p>در حال حاضر هیچ نقشی در سیستم تعریف نشده است.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- مودال کاربر -->
<div id="userModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); backdrop-filter: blur(4px);">
    <div style="background: white; margin: 2% auto; border-radius: var(--border-radius-xl); width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: var(--shadow-xl); border: 1px solid var(--gray-200);">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem 2rem; border-bottom: 1px solid var(--gray-200); background: var(--gray-50); border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;">
            <h3 id="userModalTitle" style="font-size: 1.25rem; font-weight: 700; color: var(--gray-800); margin: 0;">افزودن کاربر جدید</h3>
            <button onclick="closeUserModal()" style="width: 2rem; height: 2rem; border-radius: 50%; background: var(--gray-200); border: none; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--gray-600); transition: all 0.2s ease;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div style="padding: 2rem;">
            <form id="userForm">
                <input type="hidden" id="userId" name="user_id">
                <input type="hidden" id="userAction" name="action" value="create_user">

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--gray-700); font-size: 0.9rem;">نام کاربری </label>
                        <input type="text" id="username" name="username" required style="width: 100%; padding: 0.75rem 1rem; border: 2px solid var(--gray-200); border-radius: var(--border-radius); font-size: 1rem; transition: all 0.3s ease; background: var(--gray-50);">
                    </div>

                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--gray-700); font-size: 0.9rem;">نام و نام خانوادگی </label>
                        <input type="text" id="name" name="name" required style="width: 100%; padding: 0.75rem 1rem; border: 2px solid var(--gray-200); border-radius: var(--border-radius); font-size: 1rem; transition: all 0.3s ease; background: var(--gray-50);">
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--gray-700); font-size: 0.9rem;">رمز عبور</label>
                        <input type="password" id="password" name="password" style="width: 100%; padding: 0.75rem 1rem; border: 2px solid var(--gray-200); border-radius: var(--border-radius); font-size: 1rem; transition: all 0.3s ease; background: var(--gray-50);">
                        <small style="color: var(--gray-500); font-size: 0.8rem;">برای ویرایش خالی بگذارید</small>
                    </div>

                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--gray-700); font-size: 0.9rem;">نقش </label>
                        <select id="roleId" name="role_id" required style="width: 100%; padding: 0.75rem 1rem; border: 2px solid var(--gray-200); border-radius: var(--border-radius); font-size: 1rem; transition: all 0.3s ease; background: var(--gray-50);">
                            <option value="">انتخاب نقش</option>
                            <?php foreach ($roles as $role): ?>
                            <option value="<?= $role['id'] ?>"><?= htmlspecialchars($role['display_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label style="display: flex; align-items: center; padding: 0.75rem; background: var(--gray-50); border: 1px solid var(--gray-200); border-radius: var(--border-radius); transition: all 0.2s ease; cursor: pointer;">
                        <input type="checkbox" id="isActive" name="is_active" checked style="margin-left: 0.5rem; width: auto; accent-color: var(--primary-color);">
                        <span style="cursor: pointer; font-size: 0.9rem; color: var(--gray-700);">کاربر فعال باشد</span>
                    </label>
                </div>

                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" onclick="closeUserModal()" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        انصراف
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        ذخیره
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال نقش -->
<?php if (has_permission('user_management', 'manage_roles')): ?>
<div id="roleModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); backdrop-filter: blur(4px);">
    <div style="background: white; margin: 2% auto; border-radius: var(--border-radius-xl); width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: var(--shadow-xl); border: 1px solid var(--gray-200);">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem 2rem; border-bottom: 1px solid var(--gray-200); background: var(--gray-50); border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;">
            <h3 id="roleModalTitle" style="font-size: 1.25rem; font-weight: 700; color: var(--gray-800); margin: 0;">افزودن نقش جدید</h3>
            <button onclick="closeRoleModal()" style="width: 2rem; height: 2rem; border-radius: 50%; background: var(--gray-200); border: none; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--gray-600); transition: all 0.2s ease;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div style="padding: 2rem;">
            <form id="roleForm">
                <input type="hidden" id="roleIdEdit" name="role_id">
                <input type="hidden" id="roleAction" name="action" value="create_role">

                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--gray-700); font-size: 0.9rem;">نام نقش (انگلیسی) </label>
                    <input type="text" id="roleName" name="name" required style="width: 100%; padding: 0.75rem 1rem; border: 2px solid var(--gray-200); border-radius: var(--border-radius); font-size: 1rem; transition: all 0.3s ease; background: var(--gray-50);">
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--gray-700); font-size: 0.9rem;">نام نمایشی </label>
                    <input type="text" id="roleDisplayName" name="display_name" required style="width: 100%; padding: 0.75rem 1rem; border: 2px solid var(--gray-200); border-radius: var(--border-radius); font-size: 1rem; transition: all 0.3s ease; background: var(--gray-50);">
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: var(--gray-700); font-size: 0.9rem;">توضیحات</label>
                    <textarea id="roleDescription" name="description" rows="3" style="width: 100%; padding: 0.75rem 1rem; border: 2px solid var(--gray-200); border-radius: var(--border-radius); font-size: 1rem; transition: all 0.3s ease; background: var(--gray-50); resize: vertical;"></textarea>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label style="display: flex; align-items: center; padding: 0.75rem; background: var(--gray-50); border: 1px solid var(--gray-200); border-radius: var(--border-radius); transition: all 0.2s ease; cursor: pointer;">
                        <input type="checkbox" id="roleIsActive" name="is_active" checked style="margin-left: 0.5rem; width: auto; accent-color: var(--primary-color);">
                        <span style="cursor: pointer; font-size: 0.9rem; color: var(--gray-700);">نقش فعال باشد</span>
                    </label>
                </div>

                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" onclick="closeRoleModal()" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        انصراف
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i>
                        ذخیره
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال مجوزهای نقش -->
<div id="permissionsModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); backdrop-filter: blur(4px);">
    <div style="background: white; margin: 1% auto; border-radius: var(--border-radius-xl); width: 95%; max-width: 1000px; max-height: 95vh; overflow-y: auto; box-shadow: var(--shadow-xl); border: 1px solid var(--gray-200);">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem 2rem; border-bottom: 1px solid var(--gray-200); background: var(--gray-50); border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;">
            <h3 id="permissionsModalTitle" style="font-size: 1.25rem; font-weight: 700; color: var(--gray-800); margin: 0;">مدیریت مجوزهای نقش</h3>
            <button onclick="closePermissionsModal()" style="width: 2rem; height: 2rem; border-radius: 50%; background: var(--gray-200); border: none; cursor: pointer; display: flex; align-items: center; justify-content: center; color: var(--gray-600); transition: all 0.2s ease;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div style="padding: 2rem;">
            <form id="permissionsForm">
                <input type="hidden" id="permissionsRoleId" name="role_id">
                <input type="hidden" name="action" value="update_role_permissions">

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                    <?php foreach ($pages as $page): ?>
                    <div style="border: 1px solid var(--gray-200); border-radius: var(--border-radius); padding: 1.5rem; background: var(--gray-50);">
                        <h4 style="margin: 0 0 1rem 0; color: var(--gray-800); font-size: 1.1rem; font-weight: 600; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="<?= $page['icon'] ?: 'fas fa-file' ?>"></i>
                            <?= htmlspecialchars($page['display_name']) ?>
                        </h4>
                        <div style="display: grid; gap: 0.75rem;">
                            <?php foreach ($all_permissions[$page['id']] as $permission): ?>
                            <label style="display: flex; align-items: center; padding: 0.5rem; background: white; border: 1px solid var(--gray-200); border-radius: var(--border-radius); transition: all 0.2s ease; cursor: pointer;">
                                <input type="checkbox" name="permissions[]" value="<?= $permission['id'] ?>" style="margin-left: 0.5rem; width: auto; accent-color: var(--primary-color);">
                                <span style="cursor: pointer; font-size: 0.9rem; color: var(--gray-700);"><?= htmlspecialchars($permission['display_name']) ?></span>
                            </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid var(--gray-200);">
                    <button type="button" onclick="closePermissionsModal()" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        انصراف
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        ذخیره مجوزها
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Toast Container -->
<div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

<script>
// Global Toast Message Functions
function showToast(message, type = 'info', duration = 4000) {
    const container = document.getElementById('toast-container');
    if (!container) return;
    
    const toast = document.createElement('div');
    toast.style.cssText = `
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 1rem 1.5rem;
        margin-bottom: 10px;
        border-right: 4px solid;
        min-width: 300px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        opacity: 0;
        position: relative;
    `;
    
    const title = type === 'success' ? 'موفقیت' : 
                  type === 'warning' ? 'هشدار' : 
                  type === 'danger' ? 'خطا' : 'اطلاعات';

    // Set border and text color based on type
    let borderColor, textColor;
    switch(type) {
        case 'success':
            borderColor = '#28a745';
            textColor = '#155724';
            break;
        case 'warning':
            borderColor = '#ffc107';
            textColor = '#856404';
            break;
        case 'danger':
            borderColor = '#dc3545';
            textColor = '#721c24';
            break;
        default:
            borderColor = '#17a2b8';
            textColor = '#0c5460';
    }
    
    toast.style.borderColor = borderColor;
    toast.style.color = textColor;
    
    toast.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
            <span style="font-weight: 600; font-size: 0.9rem;">${title}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 1.2rem; cursor: pointer; color: #666; padding: 0; line-height: 1;">&times;</button>
        </div>
        <div style="font-size: 0.9rem; line-height: 1.4;">${message}</div>
    `;
    
    container.appendChild(toast);
    
    // Show animation
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
        toast.style.opacity = '1';
    }, 100);
    
    // Auto remove after duration
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 300);
    }, duration);
}

// تابع تغییر تب
function showTab(tabName) {
    // مخفی کردن همه تب‌ها
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // حذف کلاس active از همه دکمه‌های تب
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // نمایش تب انتخاب شده
    document.getElementById(tabName + '-tab').classList.add('active');

    // اضافه کردن کلاس active به دکمه تب
    event.target.classList.add('active');
}

// جستجو در جدول کاربران
document.getElementById('userSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('#usersTable tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// جستجو در جدول نقش‌ها
const roleSearch = document.getElementById('roleSearch');
if (roleSearch) {
    roleSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('#rolesTable tbody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

// مدیریت مودال کاربر
function openUserModal(userId = null) {
    const modal = document.getElementById('userModal');
    const form = document.getElementById('userForm');
    const title = document.getElementById('userModalTitle');
    const action = document.getElementById('userAction');

    form.reset();

    if (userId) {
        title.textContent = 'ویرایش کاربر';
        action.value = 'update_user';
        document.getElementById('userId').value = userId;

        // بارگذاری اطلاعات کاربر
        fetch('', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'action=get_user&user_id=' + userId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('username').value = data.user.username;
                document.getElementById('name').value = data.user.name;
                document.getElementById('roleId').value = data.user.role_id;
                document.getElementById('isActive').checked = data.user.is_active == 1;
                document.getElementById('username').readOnly = true;
            }
        });
    } else {
        title.textContent = 'افزودن کاربر جدید';
        action.value = 'create_user';
        document.getElementById('username').readOnly = false;
    }

    // جلوگیری از اسکرول صفحه پشت
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    document.body.style.top = `-${scrollTop}px`;
    document.body.classList.add('modal-open');

    modal.style.display = 'block';
}

function closeUserModal() {
    document.getElementById('userModal').style.display = 'none';

    // بازگرداندن اسکرول صفحه
    document.body.classList.remove('modal-open');
    const scrollTop = parseInt(document.body.style.top || '0') * -1;
    document.body.style.top = '';
    window.scrollTo(0, scrollTop);
}

function editUser(userId) {
    openUserModal(userId);
}

function deleteUser(userId, userName) {
    if (confirm('آیا از حذف کاربر "' + userName + '" اطمینان دارید؟')) {
        fetch('', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'action=delete_user&user_id=' + userId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('خطا: ' + data.message);
            }
        });
    }
}

// مدیریت فرم کاربر
document.getElementById('userForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeUserModal();
            showToast(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('خطا: ' + data.message, 'danger');
        }
    });
});

// مدیریت مودال نقش
function openRoleModal(roleId = null) {
    const modal = document.getElementById('roleModal');
    if (!modal) return;

    const form = document.getElementById('roleForm');
    const title = document.getElementById('roleModalTitle');
    const action = document.getElementById('roleAction');

    form.reset();

    if (roleId) {
        title.textContent = 'ویرایش نقش';
        action.value = 'update_role';
        document.getElementById('roleIdEdit').value = roleId;

        // بارگذاری اطلاعات نقش
        fetch('', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'action=get_role&role_id=' + roleId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('roleName').value = data.role.name;
                document.getElementById('roleDisplayName').value = data.role.display_name;
                document.getElementById('roleDescription').value = data.role.description || '';
                document.getElementById('roleIsActive').checked = data.role.is_active == 1;
                document.getElementById('roleName').readOnly = true;
            }
        });
    } else {
        title.textContent = 'افزودن نقش جدید';
        action.value = 'create_role';
        document.getElementById('roleName').readOnly = false;
    }

    // جلوگیری از اسکرول صفحه پشت
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    document.body.style.top = `-${scrollTop}px`;
    document.body.classList.add('modal-open');

    modal.style.display = 'block';
}

function closeRoleModal() {
    const modal = document.getElementById('roleModal');
    if (modal) {
        modal.style.display = 'none';

        // بازگرداندن اسکرول صفحه
        document.body.classList.remove('modal-open');
        const scrollTop = parseInt(document.body.style.top || '0') * -1;
        document.body.style.top = '';
        window.scrollTo(0, scrollTop);
    }
}

function editRole(roleId) {
    openRoleModal(roleId);
}

function deleteRole(roleId, roleName, userCount) {
    let confirmMessage;
    
    if (userCount > 0) {
        confirmMessage = 'این نقش دارای ' + userCount + ' کاربر است.\n\nدر صورت حذف، کاربران این نقش به نقش "کاربر عادی" منتقل خواهند شد.\n\nآیا از حذف نقش "' + roleName + '" اطمینان دارید؟';
    } else {
        confirmMessage = 'آیا از حذف نقش "' + roleName + '" اطمینان دارید؟';
    }
    
    if (confirm(confirmMessage)) {
        fetch('', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'action=delete_role&role_id=' + roleId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('خطا: ' + data.message, 'danger');
            }
        });
    }
}

// مدیریت فرم نقش
const roleForm = document.getElementById('roleForm');
if (roleForm) {
    roleForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch('', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeRoleModal();
                location.reload();
            } else {
                alert('خطا: ' + data.message);
            }
        });
    });
}

// مدیریت مجوزهای نقش
function manageRolePermissions(roleId, roleName) {
    const modal = document.getElementById('permissionsModal');
    if (!modal) return;

    const title = document.getElementById('permissionsModalTitle');
    title.textContent = 'مدیریت مجوزهای نقش: ' + roleName;

    document.getElementById('permissionsRoleId').value = roleId;

    // بارگذاری مجوزهای فعلی نقش
    fetch('', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'action=get_role_permissions&role_id=' + roleId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // پاک کردن همه چک‌باکس‌ها
            document.querySelectorAll('#permissionsForm input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // چک کردن مجوزهای موجود
            data.permission_ids.forEach(permissionId => {
                const checkbox = document.querySelector('#permissionsForm input[value="' + permissionId + '"]');
                if (checkbox) checkbox.checked = true;
            });
        }
    });

    // جلوگیری از اسکرول صفحه پشت
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    document.body.style.top = `-${scrollTop}px`;
    document.body.classList.add('modal-open');

    modal.style.display = 'block';
}

function closePermissionsModal() {
    const modal = document.getElementById('permissionsModal');
    if (modal) {
        modal.style.display = 'none';

        // بازگرداندن اسکرول صفحه
        document.body.classList.remove('modal-open');
        const scrollTop = parseInt(document.body.style.top || '0') * -1;
        document.body.style.top = '';
        window.scrollTo(0, scrollTop);
    }
}

// مدیریت فرم مجوزها
const permissionsForm = document.getElementById('permissionsForm');
if (permissionsForm) {
    permissionsForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch('', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closePermissionsModal();
                alert('مجوزهای نقش با موفقیت به‌روزرسانی شد.');
            } else {
                alert('خطا: ' + data.message);
            }
        });
    });
}

// بستن مودال‌ها با کلیک خارج از آن‌ها
window.addEventListener('click', function(event) {
    const modals = [
        { id: 'userModal', closeFunc: closeUserModal },
        { id: 'roleModal', closeFunc: closeRoleModal },
        { id: 'permissionsModal', closeFunc: closePermissionsModal }
    ];

    modals.forEach(modalInfo => {
        const modal = document.getElementById(modalInfo.id);
        if (modal && event.target === modal) {
            modalInfo.closeFunc();
        }
    });
});

// اضافه کردن استایل‌های فوکوس برای فیلدهای فرم
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.borderColor = 'var(--primary-color)';
            this.style.backgroundColor = 'white';
            this.style.boxShadow = '0 0 0 3px rgb(37 99 235 / 0.1)';
        });

        input.addEventListener('blur', function() {
            this.style.borderColor = 'var(--gray-200)';
            this.style.backgroundColor = 'var(--gray-50)';
            this.style.boxShadow = 'none';
        });
    });

    // اضافه کردن استایل‌های hover برای دکمه‌های بستن مودال
    const closeButtons = document.querySelectorAll('[onclick*="close"][onclick*="Modal"]');
    closeButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'var(--gray-300)';
            this.style.color = 'var(--gray-800)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'var(--gray-200)';
            this.style.color = 'var(--gray-600)';
        });
    });
});

// اضافه کردن استایل empty state
const style = document.createElement('style');
style.textContent = `
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--gray-500);
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: var(--gray-300);
    }

    .empty-state h3 {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
        color: var(--gray-600);
    }

    .empty-state p {
        margin: 0;
        font-size: 1rem;
    }

    @media (max-width: 768px) {
        .page-container {
            padding: 1rem 0.5rem;
        }

        .page-header {
            padding: 1.5rem;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .page-title i {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-number {
            font-size: 2rem;
        }

        .tabs {
            flex-direction: column;
        }

        .tab {
            min-width: auto;
            padding: 1rem;
        }

        .tab-content {
            padding: 1rem;
        }

        .content-header {
            flex-direction: column;
            align-items: stretch;
        }

        .search-container {
            max-width: none;
        }

        .data-table-container {
            overflow-x: auto;
        }

        .data-table {
            min-width: 600px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 0.25rem;
        }

        .btn-sm {
            padding: 0.375rem 0.5rem;
            font-size: 0.75rem;
        }
    }

    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .page-title {
            font-size: 1.25rem;
            flex-direction: column;
            text-align: center;
        }

        .page-title i {
            font-size: 1.5rem;
        }
    }
`;
document.head.appendChild(style);
</script>

</body>
</html>

<?php include '../includes/footer.php'; ?>
