<?php
require_once __DIR__ . '/../db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== اضافه کردن جدول یادآوری‌های poke ===\n\n";
    
    // بررسی وجود جدول
    $check_table = $pdo->query("SHOW TABLES LIKE 'breakdown_report_pokes'")->fetch();
    
    if ($check_table) {
        echo "✓ جدول breakdown_report_pokes قبلاً وجود دارد.\n";
    } else {
        // ایجاد جدول
        $create_table_sql = "
        CREATE TABLE breakdown_report_pokes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            report_id INT NOT NULL,
            sender_id INT NOT NULL,
            recipient_id INT NOT NULL,
            message TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (report_id) REFERENCES breakdown_reports(id) ON DELETE CASCADE,
            FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci
        ";
        
        $pdo->exec($create_table_sql);
        echo "✓ جدول breakdown_report_pokes با موفقیت ایجاد شد.\n";
    }
    
    echo "\n=== مهاجرت کامل شد ===\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
