// ویجت‌های داشبورد - JavaScript

class DashboardWidgets {
    constructor() {
        this.widgets = [];
        this.init();
    }

    init() {
        this.loadAllWidgets();
        this.bindEvents();
    }

    bindEvents() {
        // بارگذاری مجدد ویجت
        $(document).on('click', '.refresh-widget', (e) => {
            const widgetId = $(e.target).closest('.dashboard-widget').data('widget-id');
            this.refreshWidget(widgetId);
        });

        // حذف ویجت از داشبورد
        $(document).on('click', '.remove-widget', (e) => {
            const widgetId = $(e.target).closest('.dashboard-widget').data('widget-id');
            this.removeWidget(widgetId);
        });
    }

    loadAllWidgets() {
        $('.dashboard-widget').each((index, element) => {
            const widgetId = $(element).data('widget-id');
            const widgetType = $(element).data('widget-type');
            const reportId = $(element).data('report-id');
            
            this.loadWidget(widgetId, widgetType, reportId);
        });
    }

    loadWidget(widgetId, widgetType, reportId) {
        const contentContainer = $(`#widget-content-${widgetId}`);
        
        $.ajax({
            url: 'get_report.php',
            method: 'GET',
            data: { id: reportId },
            success: (response) => {
                if (response.success) {
                    this.renderWidget(widgetId, widgetType, response.data, response.config);
                } else {
                    this.showWidgetError(widgetId, response.message);
                }
            },
            error: (xhr, status, error) => {
                this.showWidgetError(widgetId, 'خطا در بارگذاری داده‌ها');
            }
        });
    }

    renderWidget(widgetId, widgetType, data, config) {
        const contentContainer = $(`#widget-content-${widgetId}`);
        
        if (!data || data.length === 0) {
            contentContainer.html('<p class="text-center text-muted">داده‌ای یافت نشد</p>');
            return;
        }

        switch (widgetType) {
            case 'table':
                this.renderTableWidget(contentContainer, data);
                break;
            case 'chart':
                this.renderChartWidget(contentContainer, data, config, widgetId);
                break;
            case 'card':
                this.renderCardWidget(contentContainer, data);
                break;
            case 'kpi':
                this.renderKPIWidget(contentContainer, data);
                break;
            default:
                this.renderTableWidget(contentContainer, data);
        }
    }

    renderTableWidget(container, data) {
        const headers = Object.keys(data[0]);
        let tableHtml = '<div class="table-responsive"><table class="table table-sm table-striped">';
        
        // سرتیتر
        tableHtml += '<thead><tr>';
        headers.forEach(header => {
            tableHtml += `<th style="font-size: 0.75rem;">${header}</th>`;
        });
        tableHtml += '</tr></thead>';
        
        // داده‌ها (محدود به 10 ردیف برای ویجت)
        tableHtml += '<tbody>';
        data.slice(0, 10).forEach(row => {
            tableHtml += '<tr>';
            headers.forEach(header => {
                let value = row[header] || '';
                // کوتاه کردن متن‌های طولانی
                if (typeof value === 'string' && value.length > 20) {
                    value = value.substring(0, 20) + '...';
                }
                tableHtml += `<td style="font-size: 0.75rem;">${value}</td>`;
            });
            tableHtml += '</tr>';
        });
        tableHtml += '</tbody></table></div>';
        
        container.html(tableHtml);
    }

    renderChartWidget(container, data, config, widgetId) {
        const canvasId = `chart-${widgetId}`;
        container.html(`<div class="chart-container"><canvas id="${canvasId}"></canvas></div>`);
        
        const chartData = this.prepareChartData(data);
        const chartType = config.chart_type || 'bar';
        
        new Chart(document.getElementById(canvasId), {
            type: chartType,
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    y: {
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    }

    renderCardWidget(container, data) {
        let cardsHtml = '<div class="widget-cards">';
        
        // نمایش حداکثر 4 کارت
        data.slice(0, 4).forEach(row => {
            const headers = Object.keys(row);
            cardsHtml += `
                <div class="widget-card">
                    <div class="widget-card-title">${row[headers[0]]}</div>
                    ${headers.slice(1, 3).map(header => 
                        `<div class="widget-card-text">${header}: ${row[header]}</div>`
                    ).join('')}
                </div>
            `;
        });
        
        cardsHtml += '</div>';
        container.html(cardsHtml);
    }

    renderKPIWidget(container, data) {
        if (data && data.length > 0) {
            const row = data[0];
            const entries = Object.entries(row);
            
            if (entries.length > 0) {
                const [key, value] = entries[0];
                container.html(`
                    <div class="widget-kpi">
                        <div class="widget-kpi-value">${value}</div>
                        <div class="widget-kpi-label">${key}</div>
                    </div>
                `);
            }
        }
    }

    prepareChartData(data) {
        if (!data || data.length === 0) return { labels: [], datasets: [] };
        
        const headers = Object.keys(data[0]);
        const labelField = headers[0];
        const valueField = headers[1];
        
        return {
            labels: data.map(row => {
                let label = row[labelField];
                // کوتاه کردن برچسب‌های طولانی
                if (typeof label === 'string' && label.length > 15) {
                    label = label.substring(0, 15) + '...';
                }
                return label;
            }),
            datasets: [{
                label: valueField,
                data: data.map(row => row[valueField]),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
                    '#4BC0C0', '#9966FF'
                ],
                borderColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
                    '#4BC0C0', '#9966FF'
                ],
                borderWidth: 1
            }]
        };
    }

    refreshWidget(widgetId) {
        const widget = $(`.dashboard-widget[data-widget-id="${widgetId}"]`);
        const widgetType = widget.data('widget-type');
        const reportId = widget.data('report-id');
        
        // نمایش لودینگ
        $(`#widget-content-${widgetId}`).html(`
            <div class="text-center">
                <i class="fas fa-spinner fa-spin"></i> در حال بارگذاری...
            </div>
        `);
        
        // بارگذاری مجدد
        this.loadWidget(widgetId, widgetType, reportId);
    }

    removeWidget(widgetId) {
        if (!confirm('آیا از حذف این ویجت از داشبورد اطمینان دارید؟')) {
            return;
        }

        $.ajax({
            url: 'remove_widget.php',
            method: 'POST',
            data: { widget_id: widgetId },
            success: (response) => {
                if (response.success) {
                    $(`.dashboard-widget[data-widget-id="${widgetId}"]`).fadeOut(300, function() {
                        $(this).remove();
                    });
                    this.showToast('ویجت از داشبورد حذف شد', 'success');
                } else {
                    this.showToast('خطا در حذف ویجت: ' + response.message, 'danger');
                }
            },
            error: () => {
                this.showToast('خطا در ارتباط با سرور', 'danger');
            }
        });
    }

    showWidgetError(widgetId, message) {
        $(`#widget-content-${widgetId}`).html(`
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <p class="mt-2 mb-0">${message}</p>
            </div>
        `);
    }

    showToast(message, type) {
        const toast = $(`
            <div class="toast toast-${type}">
                ${message}
            </div>
        `);
        
        if (!$('.toast-container').length) {
            $('body').append('<div class="toast-container"></div>');
        }
        
        $('.toast-container').append(toast);
        
        setTimeout(() => {
            toast.addClass('show');
        }, 100);
        
        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
}

// راه‌اندازی ویجت‌های داشبورد
$(document).ready(() => {
    if ($('.dashboard-widget').length > 0) {
        new DashboardWidgets();
    }
});
