<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'report_builder', 'view')) {
    header('Location: ../pages/dashboard.php?error=access_denied');
    exit;
}

$page_title = "سازنده گزارش";
include '../includes/header.php';

// دریافت لیست جداول و فیلدهای آنها
function getTableStructure($pdo) {
    $tables = [
        'devices' => [
            'name' => 'دستگاه‌ها',
            'fields' => [
                'id' => 'شناسه',
                'name' => 'نام دستگاه',
                'serial_number' => 'شماره سریال',
                'status' => 'وضعیت',
                'location' => 'مکان',
                'purchase_date' => 'تاریخ خرید',
                'vendor_name' => 'نام فروشنده'
            ]
        ],
        'work_orders' => [
            'name' => 'دستورات کار',
            'fields' => [
                'id' => 'شناسه',
                'workorder_id' => 'شماره دستور کار',
                'title' => 'عنوان',
                'type' => 'نوع',
                'priority' => 'اولویت',
                'status' => 'وضعیت',
                'request_date' => 'تاریخ درخواست',
                'due_date' => 'تاریخ سررسید'
            ]
        ],
        'breakdown_reports' => [
            'name' => 'گزارش‌های خرابی',
            'fields' => [
                'id' => 'شناسه',
                'problem_description' => 'شرح مشکل',
                'urgency' => 'فوریت',
                'status' => 'وضعیت',
                'report_datetime' => 'تاریخ گزارش',
                'breakdown_datetime' => 'تاریخ خرابی'
            ]
        ],
        'activities' => [
            'name' => 'فعالیت‌ها',
            'fields' => [
                'id' => 'شناسه',
                'activity_name' => 'نام فعالیت',
                'service_interval_days' => 'فاصله سرویس (روز)',
                'last_activity_time' => 'آخرین فعالیت',
                'next_service_date' => 'تاریخ سرویس بعدی'
            ]
        ],
        'users' => [
            'name' => 'کاربران',
            'fields' => [
                'id' => 'شناسه',
                'username' => 'نام کاربری',
                'name' => 'نام',
                'email' => 'ایمیل',
                'role' => 'نقش'
            ]
        ]
    ];
    
    return $tables;
}

// دریافت قالب‌های گزارش
$stmt = $pdo->query("SELECT * FROM report_templates ORDER BY is_default DESC, name ASC");
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

$tableStructure = getTableStructure($pdo);
?>

<div class="main-content">
    <div class="page-header">
        <h1><i class="fas fa-chart-bar"></i> سازنده گزارش</h1>
        <p>ایجاد و مدیریت گزارش‌های تحلیلی</p>
    </div>

    <div class="row">
        <!-- پنل انتخاب قالب -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-template"></i> قالب‌های گزارش</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>انتخاب قالب:</label>
                        <select id="templateSelect" class="form-control">
                            <option value="">قالب جدید</option>
                            <?php foreach ($templates as $template): ?>
                                <option value="<?= $template['id'] ?>" 
                                        data-config='<?= htmlspecialchars($template['config']) ?>'>
                                    <?= htmlspecialchars($template['name']) ?>
                                    <?= $template['is_default'] ? ' (پیش‌فرض)' : '' ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>نام گزارش:</label>
                        <input type="text" id="reportName" class="form-control" placeholder="نام گزارش را وارد کنید">
                    </div>
                </div>
            </div>

            <!-- پنل انتخاب جداول -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> انتخاب جداول</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($tableStructure as $tableName => $tableInfo): ?>
                        <div class="form-check">
                            <input class="form-check-input table-checkbox" type="checkbox" 
                                   value="<?= $tableName ?>" id="table_<?= $tableName ?>">
                            <label class="form-check-label" for="table_<?= $tableName ?>">
                                <?= $tableInfo['name'] ?>
                            </label>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- پنل تنظیمات گزارش -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> تنظیمات گزارش</h5>
                </div>
                <div class="card-body">
                    <!-- انتخاب فیلدها -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6>فیلدهای موجود:</h6>
                            <div id="availableFields" class="field-list">
                                <!-- فیلدها به صورت پویا اضافه می‌شوند -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>فیلدهای انتخاب شده:</h6>
                            <div id="selectedFields" class="field-list selected-fields">
                                <!-- فیلدهای انتخاب شده -->
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- نوع نمایش -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>نوع نمایش:</label>
                                <select id="displayType" class="form-control">
                                    <option value="table">جدول</option>
                                    <option value="chart">نمودار</option>
                                    <option value="card">کارت</option>
                                    <option value="kpi">شاخص کلیدی</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6" id="chartTypeContainer" style="display: none;">
                            <div class="form-group">
                                <label>نوع نمودار:</label>
                                <select id="chartType" class="form-control">
                                    <option value="bar">ستونی</option>
                                    <option value="line">خطی</option>
                                    <option value="pie">دایره‌ای</option>
                                    <option value="doughnut">حلقه‌ای</option>
                                    <option value="area">ناحیه‌ای</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- دکمه‌های عملیات -->
                    <div class="mt-4">
                        <button type="button" id="previewReport" class="btn btn-primary">
                            <i class="fas fa-eye"></i> پیش‌نمایش
                        </button>
                        <button type="button" id="saveReport" class="btn btn-success">
                            <i class="fas fa-save"></i> ذخیره گزارش
                        </button>
                        <button type="button" id="saveTemplate" class="btn btn-info">
                            <i class="fas fa-bookmark"></i> ذخیره به عنوان قالب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نمایش نتایج -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card" id="resultsCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> نتایج گزارش</h5>
                </div>
                <div class="card-body">
                    <div id="reportResults">
                        <!-- نتایج گزارش اینجا نمایش داده می‌شود -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script>
// ساختار جداول برای JavaScript
const tableStructure = <?= json_encode($tableStructure) ?>;
</script>
<script src="../assets/js/report-builder.js"></script>

<?php include '../includes/footer.php'; ?>
