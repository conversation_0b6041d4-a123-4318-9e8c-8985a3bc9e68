<?php
// ## تغییر کلیدی ۱: تنظیم منطقه زمانی سرور روی تهران ##
date_default_timezone_set('Asia/Tehran');

session_start();
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// بررسی دسترسی به صفحه دستور کار
require_page_access('work_order', 'view');

$db = db_connect();

// تابع پاسخ JSON
function json_response($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Helper to convert English digits to Persian
function en_to_fa_digits($str) {
    $en = ['0','1','2','3','4','5','6','7','8','9'];
    $fa = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
    return str_replace($en, $fa, $str);
}

// ## بخش جدید: مدیریت درخواست‌های AJAX برای جزئیات ##
if (isset($_GET['action'])) {
    if ($_GET['action'] === 'get_work_order_details') {
        // بررسی مجوز مشاهده جزئیات
        require_page_access('work_order', 'view');

        $id = $_GET['id'] ?? 0;
        if (!$id) {
            json_response(['success' => false, 'message' => 'شناسه نامعتبر است.'], 400);
        }

        // Fetch main work order details
        $stmt_wo = $db->prepare("
            SELECT wo.*, d.name as device_name, u.name as requester_name, sa.activity_name as scheduled_activity_name
            FROM work_orders wo
            LEFT JOIN devices d ON wo.device_id = d.id
            LEFT JOIN users u ON wo.requester_id = u.id
            LEFT JOIN activities sa ON wo.scheduled_activity_id = sa.id
            WHERE wo.id = ?
        ");
        $stmt_wo->execute([$id]);
        $work_order = $stmt_wo->fetch(PDO::FETCH_ASSOC);

        if (!$work_order) {
            json_response(['success' => false, 'message' => 'دستور کار یافت نشد.'], 404);
        }

        // ## پیدا کردن و واکشی کامل اطلاعات گزارش خرابی مبدا ##
        $stmt_source_report = $db->prepare("SELECT id FROM breakdown_reports WHERE converted_to_wo_id = ?");
        $stmt_source_report->execute([$id]);
        $source_report_info = $stmt_source_report->fetch(PDO::FETCH_ASSOC);
        $source_report_details = null;

        if ($source_report_info) {
            $source_id = $source_report_info['id'];
            $stmt_full_report = $db->prepare("
                SELECT br.*, d.name AS device_name, u.name AS reporter_name,
                       (SELECT GROUP_CONCAT(image_path) FROM breakdown_report_images WHERE report_id = br.id) AS images
                FROM breakdown_reports br
                JOIN devices d ON br.device_id = d.id
                JOIN users u ON br.reported_by_id = u.id
                WHERE br.id = ?
            ");
            $stmt_full_report->execute([$source_id]);
            $source_report_details = $stmt_full_report->fetch(PDO::FETCH_ASSOC);

            if ($source_report_details) {
                // تبدیل تاریخ‌های گزارش خرابی به شمسی
                $source_report_details['breakdown_datetime_shamsi'] = to_shamsi($source_report_details['breakdown_datetime'], 'Y/m/d H:i');
                $source_report_details['report_datetime_shamsi'] = to_shamsi($source_report_details['report_datetime'], 'Y/m/d H:i');
                $source_report_details['line_stoppage_datetime_shamsi'] = $source_report_details['line_stoppage_datetime'] ? to_shamsi($source_report_details['line_stoppage_datetime'], 'Y/m/d H:i') : null;
                // تبدیل رشته تصاویر به آرایه
                $source_report_details['images'] = !empty($source_report_details['images']) ? explode(',', $source_report_details['images']) : [];
            }
        }
        $work_order['source_report_details'] = $source_report_details;


        // Fetch assignees
        $stmt_assignees = $db->prepare("
            SELECT u.name FROM users u
            JOIN work_order_assignees woa ON u.id = woa.user_id
            WHERE woa.work_order_id = ?
        ");
        $stmt_assignees->execute([$id]);
        $assignees = $stmt_assignees->fetchAll(PDO::FETCH_ASSOC);

        // Fetch attachments
        $stmt_attachments = $db->prepare("SELECT file_path FROM work_order_attachments WHERE work_order_id = ?");
        $stmt_attachments->execute([$id]);
        $attachments = $stmt_attachments->fetchAll(PDO::FETCH_COLUMN);

        // === [NEW] Fetch repair/execution details ===
        $stmt_exec = $db->prepare("SELECT exec.*, u.name AS verifier_name,
                   (SELECT GROUP_CONCAT(us.name SEPARATOR '، ') FROM work_order_labor wol JOIN users us ON wol.user_id = us.id WHERE wol.work_order_id = ?) AS labor_users,
                   (SELECT GROUP_CONCAT(CONCAT(wop.part_name, ' (', wop.quantity_used, ' ', wop.unit, ')') SEPARATOR ', ') FROM work_order_parts wop WHERE wop.work_order_id = ?) AS parts_used
            FROM work_order_execution exec
            LEFT JOIN users u ON exec.completed_by_id = u.id
            WHERE exec.work_order_id = ?");
        $stmt_exec->execute([$id,$id,$id]);
        $execution = $stmt_exec->fetch(PDO::FETCH_ASSOC);
        if ($execution) {
            $execution['actual_start_datetime_shamsi'] = $execution['actual_start_datetime'] ? to_shamsi($execution['actual_start_datetime'], 'Y/m/d H:i') : '-';
            $execution['actual_end_datetime_shamsi'] = $execution['actual_end_datetime'] ? to_shamsi($execution['actual_end_datetime'], 'Y/m/d H:i') : '-';
            // NEW: nicer display like my_tasks.php
            if ($execution['actual_start_datetime']) {
                $d = new DateTime($execution['actual_start_datetime']);
                $execution['start_display'] = en_to_fa_digits(to_shamsi($d->format('Y-m-d')) . '، ساعت ' . $d->format('H:i'));
            } else {
                $execution['start_display'] = '-';
            }
            if ($execution['actual_end_datetime']) {
                $d2 = new DateTime($execution['actual_end_datetime']);
                $execution['end_display'] = en_to_fa_digits(to_shamsi($d2->format('Y-m-d')) . '، ساعت ' . $d2->format('H:i'));
            } else {
                $execution['end_display'] = '-';
            }
            
            // تبدیل تاریخ‌های برون‌سپاری به شمسی
            if ($execution['exit_date']) {
                $execution['exit_date'] = en_to_fa_digits(to_shamsi($execution['exit_date']));
            }
            if ($execution['back_date']) {
                $execution['back_date'] = en_to_fa_digits(to_shamsi($execution['back_date']));
            }
        }

        // تبدیل تاریخ‌های دستور کار به شمسی
        $work_order['request_date_shamsi'] = to_shamsi($work_order['request_date']);
        $work_order['due_date_shamsi'] = $work_order['due_date'] ? to_shamsi($work_order['due_date']) : '-';
        $work_order['stop_datetime_shamsi'] = $work_order['stop_datetime'] ? to_shamsi($work_order['stop_datetime'], 'Y/m/d H:i') : '-';
        $work_order['restart_datetime_shamsi'] = $work_order['restart_datetime'] ? to_shamsi($work_order['restart_datetime'], 'Y/m/d H:i') : '-';

        json_response([
            'success' => true,
            'work_order' => $work_order,
            'assignees' => $assignees,
            'attachments' => $attachments,
            'execution' => $execution
        ]);
    }
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? null;

    // ## جدید: منطق حذف دستور کار ##
    if ($action === 'delete_work_order') {
        // بررسی مجوز حذف
        require_page_access('work_order', 'delete');

        $id = $_POST['id'] ?? 0;
        if (!$id) {
            json_response(['success' => false, 'message' => 'شناسه نامعتبر است.'], 400);
        }
        try {
            $db->beginTransaction();

            // حذف فایل‌های پیوست از سرور
            $stmt_get_attachments = $db->prepare("SELECT file_path FROM work_order_attachments WHERE work_order_id = ?");
            $stmt_get_attachments->execute([$id]);
            $attachments_to_delete = $stmt_get_attachments->fetchAll(PDO::FETCH_COLUMN);
            foreach ($attachments_to_delete as $file) {
                if (file_exists($file) && is_file($file)) {
                    unlink($file);
                }
            }

            // حذف رکوردهای مرتبط
            $db->prepare("DELETE FROM work_order_assignees WHERE work_order_id = ?")->execute([$id]);
            $db->prepare("DELETE FROM work_order_attachments WHERE work_order_id = ?")->execute([$id]);
            
            // بازگرداندن وضعیت گزارش خرابی مبدا (در صورت وجود)
            $db->prepare("UPDATE breakdown_reports SET converted_to_wo_id = NULL, status = 'باز' WHERE converted_to_wo_id = ?");
            $db->prepare("UPDATE work_order_execution SET status = 'باز' WHERE work_order_id = ?");

            // حذف دستور کار اصلی
            $stmt_wo = $db->prepare("DELETE FROM work_orders WHERE id = ?");
            $stmt_wo->execute([$id]);

            $db->commit();
            json_response(['success' => true, 'message' => 'دستور کار با موفقیت حذف شد.']);
        } catch (PDOException $e) {
            $db->rollBack();
            json_response(['success' => false, 'message' => 'خطا در حذف از پایگاه داده: ' . $e->getMessage()], 500);
        }
    }
    // ## جدید: منطق تایید نهایی دستور کار ##
    else if ($action === 'final_approve_work_order') {
        // بررسی مجوز تغییر وضعیت
        require_page_access('work_order', 'change_status');

        $work_order_id = $_POST['work_order_id'] ?? 0;
        if (!$work_order_id) {
            json_response(['success' => false, 'message' => 'شناسه دستور کار نامعتبر است.'], 400);
        }

        try {
            $db->beginTransaction();

            // به‌روزرسانی وضعیت دستور کار
            $stmt = $db->prepare("UPDATE work_orders SET status = 'تایید نهایی و بسته شد' WHERE id = ?");
            $stmt->execute([$work_order_id]);

            // به‌روزرسانی وضعیت گزارش خرابی مرتبط (اگر وجود دارد)
            $stmt = $db->prepare("UPDATE breakdown_reports SET status = 'تایید نهایی و بسته شد' WHERE converted_to_wo_id = ?");
            $stmt->execute([$work_order_id]);

            $db->commit();
            json_response(['success' => true, 'message' => 'دستور کار با موفقیت تایید نهایی و بسته شد.']);

        } catch (Exception $e) {
            $db->rollBack();
            json_response(['success' => false, 'message' => 'خطا در تایید نهایی: ' . $e->getMessage()], 500);
        }
    }
    // ## جدید: منطق بستن با وضعیت فعلی (فقط برای مدیران) ##
    else if ($action === 'close_with_current_status') {
        // بررسی مجوز بستن دستور کار
        require_page_access('work_order', 'close');

        $work_order_id = $_POST['work_order_id'] ?? 0;
        $current_status = $_POST['current_status'] ?? '';

        if (!$work_order_id || !$current_status) {
            json_response(['success' => false, 'message' => 'اطلاعات ناکافی برای انجام عملیات.'], 400);
        }

        try {
            $db->beginTransaction();

            // ایجاد وضعیت جدید
            $new_status = "در وضعیت \"$current_status\" بسته شد";

            // به‌روزرسانی وضعیت دستور کار
            $stmt = $db->prepare("UPDATE work_orders SET status = ? WHERE id = ?");
            $stmt->execute([$new_status, $work_order_id]);

            // به‌روزرسانی وضعیت گزارش خرابی مرتبط (اگر وجود دارد)
            $stmt = $db->prepare("UPDATE breakdown_reports SET status = ? WHERE converted_to_wo_id = ?");
            $stmt->execute([$new_status, $work_order_id]);

            $db->commit();
            json_response(['success' => true, 'message' => "دستور کار با وضعیت \"$current_status\" بسته شد."]);

        } catch (Exception $e) {
            $db->rollBack();
            json_response(['success' => false, 'message' => 'خطا در بستن دستور کار: ' . $e->getMessage()], 500);
        }
    }
    // منطق ثبت و پیش‌نویس
    else if ($action === 'submit' || $action === 'draft') {
        // بررسی مجوز ایجاد
        require_page_access('work_order', 'create');
        $workorder_id = $_POST['workorder_id'];
        $title = $_POST['title'];
        $request_date = $_POST['request_date'];
        $type = $_POST['type'];
        $device_id = $_POST['device_id'];
        $scheduled_activity_id = !empty($_POST['scheduled_activity_id']) ? $_POST['scheduled_activity_id'] : null;
        $description = $_POST['description'];
        $requester_id = current_user_id();
        $assignee_ids = $_POST['assignee_id'] ?? [];
        $priority = $_POST['priority'];
        $line_stopped = isset($_POST['line_stopped']) ? 1 : 0;
        $stop_time = !empty($_POST['stop_time']) ? $_POST['stop_time'] : null;
        $due_date = !empty($_POST['due_date']) ? to_miladi($_POST['due_date']) : null;
        $stop_date = !empty($_POST['stop_date']) ? to_miladi($_POST['stop_date']) : null;
        $stop_datetime = null;
        if ($line_stopped && $stop_date && $stop_time) $stop_datetime = $stop_date . ' ' . $stop_time;
        $status = $action === 'draft' ? 'پیش‌نویس' : 'دستورکار صادر شد';

        function saveWorkOrder($db, $params, $assignee_ids) {
            $stmt = $db->prepare("
                INSERT INTO work_orders (workorder_id, title, type, device_id, scheduled_activity_id, description, requester_id, priority, due_date, status, line_stopped, stop_datetime, request_date, created_at) 
                VALUES (:workorder_id, :title, :type, :device_id, :scheduled_activity_id, :description, :requester_id, :priority, :due_date, :status, :line_stopped, :stop_datetime, :request_date, NOW())
            ");
            $stmt->execute($params);
            $work_order_id = $db->lastInsertId();
            if (!empty($assignee_ids)) {
                $assignee_stmt = $db->prepare("INSERT INTO work_order_assignees (work_order_id, user_id) VALUES (:work_order_id, :user_id)");
                foreach ($assignee_ids as $assignee_id) {
                    $assignee_stmt->execute([':work_order_id' => $work_order_id, ':user_id' => $assignee_id]);
                }
            }
            return $work_order_id;
        }

        try {
            $db->beginTransaction();
            $params = [
                ':workorder_id' => $workorder_id, ':title' => $title, ':type' => $type, ':device_id' => $device_id,
                ':scheduled_activity_id' => $scheduled_activity_id, ':description' => $description, ':requester_id' => $requester_id,
                ':priority' => $priority, ':due_date' => $due_date, ':status' => $status, ':line_stopped' => $line_stopped,
                ':stop_datetime' => $stop_datetime, ':request_date' => $request_date
            ];
            $new_work_order_id = saveWorkOrder($db, $params, $assignee_ids);
            $attachment_stmt = $db->prepare("INSERT INTO work_order_attachments (work_order_id, file_path) VALUES (?, ?)");

            // پردازش فایل‌های موجود
            $existing_files = $_POST['existing_files'] ?? [];
            foreach ($existing_files as $filePath) {
                if (!empty($filePath)) {
                    // اگر فایل از گزارش خرابی است، آن را کپی کنیم
                    if (strpos($filePath, '../uploads/breakdowns/') === 0) {
                        $upload_dir = '../uploads/work_orders/';
                        if (!is_dir($upload_dir)) mkdir($upload_dir, 0755, true);

                        $fileName = 'wo_' . $new_work_order_id . '_' . uniqid() . '_' . basename($filePath);
                        $newPath = $upload_dir . $fileName;

                        if (file_exists($filePath) && copy($filePath, $newPath)) {
                            $attachment_stmt->execute([$new_work_order_id, $newPath]);
                        }
                    } else {
                        // فایل‌های عادی
                        $attachment_stmt->execute([$new_work_order_id, $filePath]);
                    }
                }
            }

            // پردازش فایل‌های جدید
            if (isset($_FILES['new_attachments']) && !empty($_FILES['new_attachments']['name'][0])) {
                $upload_dir = '../uploads/work_orders/';
                if (!is_dir($upload_dir)) mkdir($upload_dir, 0755, true);

                $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
                $max_file_size = 2 * 1024 * 1024; // 2MB

                foreach ($_FILES['new_attachments']['name'] as $key => $name) {
                    if ($_FILES['new_attachments']['error'][$key] === UPLOAD_ERR_OK) {
                        $file_ext = strtolower(pathinfo($name, PATHINFO_EXTENSION));
                        $file_size = $_FILES['new_attachments']['size'][$key];

                        if (in_array($file_ext, $allowed_types) && $file_size <= $max_file_size) {
                            $fileName = 'wo_' . uniqid() . '-' . basename($name);
                            $targetPath = $upload_dir . $fileName;
                            if (move_uploaded_file($_FILES['new_attachments']['tmp_name'][$key], $targetPath)) {
                                $attachment_stmt->execute([$new_work_order_id, $targetPath]);
                            }
                        }
                    }
                }
            }
            if (isset($_POST['source_report_id']) && !empty($_POST['source_report_id'])) {
                $source_report_id = (int)$_POST['source_report_id'];
                $updateStmt = $db->prepare("UPDATE breakdown_reports SET status = 'دستورکار صادر شد', converted_to_wo_id = ? WHERE id = ?");
                $updateStmt->execute([$new_work_order_id, $source_report_id]);
            }
            $db->commit();
            json_response(['success' => true, 'message' => 'دستور کار و پیوست‌ها با موفقیت ثبت شدند.']);
        } catch (PDOException $e) {
            $db->rollBack();
            $message = 'خطای پایگاه داده: ' . $e->getMessage();
            if ($e->errorInfo[1] == 1062) $message = 'خطای شماره دستور کار تکراری. لطفاً صفحه را رفرش کرده و دوباره تلاش کنید.';
            json_response(['success' => false, 'message' => $message], 500);
        }
    }
}

$source_report_id = null;
$prefill_data = [
    'title' => '', 'description' => '', 'device_id' => '', 'priority' => 'متوسط',
    'type' => 'پیشگیرانه', 'line_stopped' => false, 'stop_date' => '',
	'stop_hour' => '', 'stop_minute' => '', 'images' => []
];

if (isset($_GET['from_report_id']) && is_numeric($_GET['from_report_id'])) {
    $source_report_id = (int)$_GET['from_report_id'];
    $stmt = $db->prepare("
        SELECT br.*, d.name as device_name,
               (SELECT GROUP_CONCAT(image_path) FROM breakdown_report_images WHERE report_id = br.id) AS images
        FROM breakdown_reports br
        JOIN devices d ON br.device_id = d.id
        WHERE br.id = ?
    ");
    $stmt->execute([$source_report_id]);
    $report_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($report_data) {
        $prefill_data['title'] = "رفع خرابی: " . htmlspecialchars($report_data['device_name']);
        $prefill_data['description'] = htmlspecialchars($report_data['problem_description']);
        $prefill_data['device_id'] = $report_data['device_id'];
        $prefill_data['type'] = 'اضطراری';
        if ($report_data['urgency'] === 'فوری') $prefill_data['priority'] = 'بحرانی';
        if ($report_data['line_stoppage_datetime']) {
            $prefill_data['line_stopped'] = true;
            $stoppage_dt = new DateTime($report_data['line_stoppage_datetime']);
            $prefill_data['stop_date'] = to_shamsi($stoppage_dt->format('Y-m-d'));
            $prefill_data['stop_hour'] = $stoppage_dt->format('H');
            $prefill_data['stop_minute'] = $stoppage_dt->format('i');
        }
        if (!empty($report_data['images'])) $prefill_data['images'] = explode(',', $report_data['images']);
    }
}


require_once '../includes/user_helper.php';

$devices = $db->query("SELECT d.id, d.name, d.serial_number, l.location_name FROM devices d LEFT JOIN locations l ON d.location = l.id")->fetchAll(PDO::FETCH_ASSOC);
$scheduled_activities = $db->query("SELECT id, activity_name, device_id FROM activities")->fetchAll(PDO::FETCH_ASSOC);
$users = getUsersForSelect($db);

$today = date('Ymd');
$prefix = "WO-$today-";
$stmt = $db->prepare("SELECT MAX(CAST(SUBSTRING(workorder_id, LENGTH(:prefix) + 1) AS UNSIGNED)) AS last_sequence FROM work_orders WHERE workorder_id LIKE :like_prefix");
$stmt->execute([':prefix' => $prefix, ':like_prefix' => $prefix . '%']);
$result = $stmt->fetch(PDO::FETCH_ASSOC);
$sequence = ($result && $result['last_sequence'] !== null) ? (int)$result['last_sequence'] + 1 : 1;
$workorder_number = $prefix . str_pad($sequence, 3, '0', STR_PAD_LEFT);

$current_user_id = current_user_id();
$current_user_name = current_user_name();

// تعیین فیلتر بر اساس مجوزهای کاربر
$where_condition = "";
$query_params = [];

if (has_permission('work_order', 'view_all')) {
    // کاربر می‌تواند همه دستورکارها را ببیند
    $where_condition = "";
} elseif (has_permission('work_order', 'view_own')) {
    // کاربر فقط می‌تواند دستورکارهای خود را ببیند (درخواست کرده یا تخصیص یافته)
    $where_condition = "WHERE (wo.requester_id = ? OR woa.user_id = ?)";
    $query_params = [$current_user_id, $current_user_id];
} else {
    // کاربر هیچ دستورکاری نمی‌تواند ببیند
    $where_condition = "WHERE 1=0";
}

$sql = "
    SELECT
        wo.id, wo.workorder_id, wo.title, wo.request_date, wo.due_date, wo.priority, wo.status, d.name AS device_name,
        GROUP_CONCAT(DISTINCT u.name SEPARATOR ', ') AS assignees
    FROM work_orders wo
    LEFT JOIN devices d ON wo.device_id = d.id
    LEFT JOIN work_order_assignees woa ON wo.id = woa.work_order_id
    LEFT JOIN users u ON woa.user_id = u.id
    $where_condition
    GROUP BY wo.id
    ORDER BY
        CASE
            WHEN wo.status IN ('دستورکار صادر شد', 'در حال انجام') THEN 1
            WHEN wo.status = 'پیش‌نویس' THEN 2
            WHEN wo.status = 'انجام شده' THEN 3
            WHEN wo.status = 'انجام و تایید شده' THEN 3
            WHEN wo.status = 'لغو شده' THEN 4
            ELSE 5
        END,
        wo.created_at DESC
";

if (!empty($query_params)) {
    $stmt = $db->prepare($sql);
    $stmt->execute($query_params);
    $work_orders_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    $work_orders_list = $db->query($sql)->fetchAll(PDO::FETCH_ASSOC);
}

include '../includes/header.php';
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدیریت دستورکارها</title>
    <style>
        * {
            box-sizing: border-box;
        }
        .select2-container--default .select2-selection--single { height: 38px; padding: 5px 12px; }
        .select2-container--default .select2-selection--multiple { min-height: 38px; }
        .hidden-section { display: none; }
        .wo-list tr.hidden-row { display: none !important; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.5); }
        .modal-body { background-color: #fefefe; margin: 10% auto; padding: 20px; border: 1px solid #888; width: 60%; max-width: 700px; border-radius: 8px; position: relative; }
        /* Close button now uses typography scale */
        .close-btn { position: absolute; left: 15px; top: 10px; color: #aaa; font-size: var(--fs-3xl); font-weight: bold; cursor: pointer; }
        @-webkit-keyframes fadeIn { from {opacity: 0} to {opacity: 1} }
        @keyframes fadeIn { from {opacity: 0} to {opacity: 1} }

        .status-badge {
            padding: 0.25em 0.6em;
            border-radius: 0.25rem;
            color: #fff;
            font-weight: bold;
            font-size: var(--fs-sm);
            text-align: center;
            display: inline-block;
            min-width: 80px;
        }
        .status-issued { background-color: #0d6efd; }
        .status-in-progress { background-color: #ffc107; color: #000; }
        .status-draft { background-color: #6c757d; }
        .status-done { background-color: #198754; }
        .status-cancelled { background-color: #dc3545; }
        .status-default { background-color: #adb5bd; }
        .status-done-confirmed { background-color: #28a745 !important; color: #fff !important; }

        /* ## جدید: استایل برای اطلاع‌رسانی Toast ## */
        .toast-notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: #fff;
            padding: 15px 25px;
            border-radius: 8px;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s, bottom 0.3s;
        }
        .toast-notification.show {
            opacity: 1;
            visibility: visible;
            bottom: 40px;
        }
        .toast-notification.success { background-color: #198754; }
        .toast-notification.error { background-color: #dc3545; }

        .accordion-header { display: none; }
        .accordion-panel { display: block; }

        /* استایل تب‌ها */
        .tab-container {
            margin-top: 15px;
        }

        .nav-tabs {
            border-bottom: 2px solid #dee2e6;
            margin-bottom: unset;
            display: flex;
            flex-wrap: nowrap;
            list-style: none;
            padding: 0;
        }

        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }

        .nav-tabs .nav-link {
            border: 1px solid transparent;
            color: #495057;
            background-color: #f8f9fa;
            margin-left: 2px;
            padding: 10px 20px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: block;
            border-bottom: 1px solid #dee2e6;
            border-radius: unset;
        }

        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
            background-color: #e9ecef;
            isolation: isolate;
            border-radius: unset;
        }

        .nav-tabs .nav-link.active {
            color: #495057;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
            border-bottom-color: transparent;
            border-radius: unset;
        }

        .tab-content {
            padding: 20px 0;
            background: #fff;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;           
            padding-bottom: 15px;
            top: 0;           
            z-index: 10;
        }

        .modal-header .btn {
            margin-left: 5px;
        }

        .modal-header h4 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-info {
            color: #31708f;
            background-color: #d9edf7;
            border-color: #bce8f1;
        }

        /* استایل‌های قالب‌بندی گزارش‌ها */
        .repair-report-layout,
        .outsourcing-report-layout {
            margin: 0;
        }

        .repair-report-layout .row,
        .outsourcing-report-layout .row {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
        }

        .repair-report-layout .col-4,
        .repair-report-layout .col-6,
        .repair-report-layout .col-12,
        .outsourcing-report-layout .col-4,
        .outsourcing-report-layout .col-6,
        .outsourcing-report-layout .col-12 {
            padding: 0 10px;
        }

        .repair-report-layout .col-4,
        .outsourcing-report-layout .col-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .repair-report-layout .col-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .repair-report-layout .col-12,
        .outsourcing-report-layout .col-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }


        .repair-report-layout .detail-content,
        .outsourcing-report-layout .detail-content {
            margin-top: 5px;
            padding: 5px 0;
            line-height: 1.5;
        }
        #detailsModalBody a.btn.btn-secondary {margin: inherit;}
        @media (max-width: 768px) {
            html, body { width: 100%; overflow-x: hidden; }
            .wo-container { flex-direction: column !important; width: 100% !important; padding: 1rem 15px !important; }
            .wo-add, .wo-list { width: 100% !important; min-width: 0 !important; padding: 0 !important; margin: 0 0 2rem 0 !important; }
            .wo-add > h3 { display: none; }
            .accordion-header { display: flex; justify-content: space-between; align-items: center; cursor: pointer; padding: 1rem; background-color: #f1f1f1; border: 1px solid #ddd; border-radius: 8px; }
            .accordion-header.active { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
            .accordion-header h3 { margin: 0; font-size: var(--fs-lg); }
            .accordion-icon { font-size: var(--fs-xl); font-weight: bold; transition: transform 0.3s ease; }
            .accordion-header.active .accordion-icon { transform: rotate(45deg); }
            .accordion-panel { display: none; padding: 1rem; border: 1px solid #ddd; border-top: none; border-radius: 0 0 8px 8px; }
            .wo-search-container { width: 100% !important; margin: 0 0 1rem 0 !important; }
            .wo-add-head, .wo-add-1 { display: flex !important; flex-direction: column !important; }
            .wo-add-head > div, .wo-add-1 > div { width: 100% !important; margin-bottom: 1rem !important; }
            #stop-time-section .row { flex-direction: column !important; }
            #stop-time-section .row .col { width: 100% !important; margin-bottom: 1rem !important; }
            #stop-time-section .row .col:last-child { margin-bottom: 0 !important; }
            .modal-body { width: 95% !important; margin: 5% auto !important; padding: 15px !important; }
            .details-grid { display: block !important; }
            .details-grid > div { margin-bottom: 1rem !important; }
            .table-scroll-container { overflow-x: hidden !important; }
            .wo-list table { border-collapse: collapse !important; width: 100% !important; }
            .wo-list thead { display: none !important; }
            .wo-list tr { display: block !important; margin-bottom: 1rem !important; border: 1px solid #ddd !important; border-radius: 8px !important; padding: 1rem !important; background-color: #fff !important; box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important; }
            .wo-list tr:last-child { margin-bottom: 0 !important; }
            .wo-list td { display: flex !important; justify-content: space-between !important; align-items: center !important; padding: 0.5rem 0 !important; border-bottom: 1px solid #eee !important; text-align: left !important; word-break: break-word !important; overflow-wrap: break-word !important; }
            .wo-list td:last-child { border-bottom: none !important; flex-direction: row !important; justify-content: left !important; align-items: center !important; padding-top: 1rem !important; }
            .wo-list td:last-child button { width: auto !important; margin: 0 0.25rem !important; }
            .wo-list td:last-child::before { display: none !important; }
            .wo-list td::before { content: attr(data-label); font-weight: bold !important; text-align: right !important; padding-left: 1rem !important; flex-shrink: 0 !important; }
            #add-wo-form .btn { width: 100% !important; margin-bottom: 0.5rem !important; }
            #add-wo-form .btn.ms-2 { margin: 0 0 0.5rem 0 !important; }
            #add-wo-form button:last-of-type { margin-bottom: 0 !important; }
            
        }
    </style>
</head>
<body class="p-4">

    <div class="wo-container">
		<div class="wo-add">
            <?php if (has_permission('work_order', 'create')): ?>
            <h3>صدور دستور کار جدید</h3>
            <div id="accordion-toggle" class="accordion-header">
                <h3>صدور دستور کار جدید</h3>
                <span class="accordion-icon">+</span>
            </div>
            <div id="accordion-panel" class="accordion-panel">
			    <form id="add-wo-form" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="submit">
                    <?php if ($source_report_id): ?>
                        <div class="alert alert-info mb-3">این دستور کار بر اساس <strong>گزارش خرابی شماره <?= $source_report_id ?></strong> در حال صدور است.</div>
                        <input type="hidden" name="source_report_id" value="<?= $source_report_id ?>">
                    <?php endif; ?>
                    <div class="wo-add-head">
                        <div class="mb-3">
                            <label class="form-label">شماره دستور کار:</label>
                            <input type="text" name="workorder_id" value="<?= htmlspecialchars($workorder_number) ?>" class="form-control" readonly />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ایجاد کننده:</label>
                            <input type="text" value="<?= htmlspecialchars($current_user_name) ?>" class="form-control" readonly />
                            <input type="hidden" name="requester_id" value="<?= $current_user_id ?>" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تاریخ ایجاد:</label>
                            <input type="text" value="<?= jdate('Y/m/d') ?>" class="form-control" readonly />
                            <input type="hidden" name="request_date" value="<?= date('Y-m-d') ?>" />
                        </div>
                    </div>	
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان دستور کار:</label>
                        <input type="text" name="title" class="form-control" value="<?= htmlspecialchars($prefill_data['title']) ?>" required>
                    </div>
				    <div class="wo-add-1">
                        <div class="mb-3">
                            <label class="form-label">نوع دستور کار:</label>
                            <select name="type" class="form-select">
                                <option value="پیشگیرانه" <?= $prefill_data['type'] === 'پیشگیرانه' ? 'selected' : '' ?>>پیشگیرانه</option>
                                <option value="اصلاحی" <?= $prefill_data['type'] === 'اصلاحی' ? 'selected' : '' ?>>اصلاحی</option>
                                <option value="اضطراری" <?= $prefill_data['type'] === 'اضطراری' ? 'selected' : '' ?>>اضطراری</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="priority" class="form-label">اولویت:</label>
                            <select name="priority" class="form-select">
                                <option value="متوسط" <?= $prefill_data['priority'] === 'متوسط' ? 'selected' : '' ?>>متوسط</option>
                                <option value="پایین" <?= $prefill_data['priority'] === 'پایین' ? 'selected' : '' ?>>پایین</option>
                                <option value="بالا" <?= $prefill_data['priority'] === 'بالا' ? 'selected' : '' ?>>بالا</option>
                                <option value="بحرانی" <?= $prefill_data['priority'] === 'بحرانی' ? 'selected' : '' ?>>بحرانی</option>
                            </select>
                        </div>
				    </div>
				    <div class="mb-3">
                        <label class="form-label">دستگاه مربوطه:</label>
                        <select name="device_id" id="device-select" class="form-select" required>
                            <option></option>
                            <?php foreach ($devices as $device): ?>
                                <option value="<?= $device['id'] ?>" data-location="<?= htmlspecialchars($device['location_name'] ?: 'بدون محل') ?>" data-serial="<?= htmlspecialchars($device['serial_number'] ?: 'بدون سریال') ?>">
								    <?= htmlspecialchars($device['name']) ?>
							    </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">فعالیت برنامه‌ریزی‌شده (اختیاری):</label>
                        <select name="scheduled_activity_id" id="activity-select" class="form-select" disabled>
                            <option value="">-- بدون ارتباط --</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">شرح فعالیت/مشکل:</label>
                        <textarea name="description" rows="5" class="form-control" required><?= $prefill_data['description'] ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">مسئول اجرا:</label>
                        <select name="assignee_id[]" id="assignee-select" class="form-select" multiple required>
                            <?php foreach ($users as $user): ?>
                                <option value="<?= $user['id'] ?>"><?= htmlspecialchars($user['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
				    <div class="mb-3">
                        <label class="form-label">تاریخ سررسید:</label>
                        <input type="text" id="due-date" name="due_date" class="form-control" autocomplete="off" />
                    </div>
                    <div class="mb-3">
                        <?php
                        require_once '../includes/file_uploader.php';

                        // تبدیل فایل‌های موجود به فرمت مورد نیاز
                        $existing_files = [];
                        foreach ($prefill_data['images'] as $img_path) {
                            $existing_files[] = [
                                'path' => $img_path,
                                'name' => basename($img_path),
                                'size' => file_exists($img_path) ? filesize($img_path) : 0
                            ];
                        }

                        echo render_file_uploader([
                            'id' => 'workOrderAttachments',
                            'name' => 'new_attachments[]',
                            'accept' => 'image/*,.pdf',
                            'max_size' => 2,
                            'label' => 'فایل‌های ضمیمه',
                            'description' => 'فایل‌های عکس و PDF - حداکثر 2 مگابایت',
                            'existing_files' => $existing_files,
                            'show_existing' => true
                        ]);
                        ?>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" name="line_stopped" id="line-stopped" class="form-check-input" <?= $prefill_data['line_stopped'] ? 'checked' : '' ?> />
                        <label for="line-stopped" class="form-check-label">خط متوقف شده است</label>
                    </div>
                    <div id="stop-time-section" class="mb-3 <?= !$prefill_data['line_stopped'] ? 'hidden-section' : '' ?>">
                        <label class="form-label">تاریخ و زمان توقف خط:</label>
                        <div class="row">
                            <div class="col">
                                <input type="text" id="stop-date" name="stop_date" class="form-control" placeholder="تاریخ" autocomplete="off" value="<?= htmlspecialchars($prefill_data['stop_date']) ?>" />
                            </div>
                            <div class="col">
                                <div class="time-input-container">
                                    <input type="text" id="stop_minute" placeholder="دقیقه" maxlength="2" value="<?= htmlspecialchars($prefill_data['stop_minute']) ?>">
                                    <span class="time-colon">:</span>
                                    <input type="text" id="stop_hour" placeholder="ساعت" maxlength="2" value="<?= htmlspecialchars($prefill_data['stop_hour']) ?>">
                                    <input type="hidden" id="stop_time" name="stop_time">
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="submit" name="action" value="submit" class="btn btn-primary">ثبت دستور کار</button>
                    <button type="submit" name="action" value="draft" class="btn btn-secondary ms-2">ذخیره به‌صورت پیش‌نویس</button>
                </form>
            </div>
            <?php else: ?>
            <div class="alert alert-warning">
                <strong>توجه:</strong> شما مجوز ایجاد دستور کار جدید را ندارید.
            </div>
            <?php endif; ?>
        </div>
        <div class="wo-list">
            <h3 class="mb-4">لیست دستورکارها</h3>
			 <div class="wo-search-container">
                <input type="text" id="workOrderSearch" placeholder="جستجو با شماره، عنوان، دستگاه، اولویت، مسئول و ..." class="form-control">
            </div>
			<div class="table-scroll-container">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>عنوان</th>
                        <th>دستگاه</th>
                        <th>تاریخ صدور</th>
                        <th>تاریخ سررسید</th>
                        <th>اولویت</th>
                        <th>وضعیت</th>
                        <th>عملیات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($work_orders_list)): ?>
                        <tr><td colspan="7" class="text-center">دستور کاری یافت نشد.</td></tr>
                    <?php else: ?>
                        <?php foreach ($work_orders_list as $wo): ?>
                           <tr data-id="<?= $wo['id'] ?>" data-search-terms="<?= strtolower(htmlspecialchars($wo['workorder_id'].' '.$wo['title'].' '.$wo['device_name'].' '.$wo['priority'].' '.($wo['assignees'] ?? '').' '.$wo['status'])) ?>">
                               <td data-label="عنوان"><?= htmlspecialchars($wo['title']) ?></td>
                               <td data-label="دستگاه"><?= htmlspecialchars($wo['device_name'] ?: 'N/A') ?></td>
                               <td data-label="تاریخ صدور"><?= to_shamsi($wo['request_date']) ?></td>
                               <td data-label="تاریخ سررسید"><?= $wo['due_date'] ? to_shamsi($wo['due_date']) : '-' ?></td>
                               <td data-label="اولویت"><?= htmlspecialchars($wo['priority']) ?></td>
                               <?php
                                    $status = htmlspecialchars($wo['status']);
                                    $status_class = '';
                                    switch ($status) {
                                        case 'دستورکار صادر شد': $status_class = 'status-issued'; break;
                                        case 'در حال انجام': $status_class = 'status-in-progress'; break;
                                        case 'پیش‌نویس': $status_class = 'status-draft'; break;
                                        case 'انجام شده': $status_class = 'status-done'; break;
                                        case 'انجام و تایید شده': $status_class = 'status-done-confirmed'; break;
                                        case 'لغو شده': $status_class = 'status-cancelled'; break;
                                        default: $status_class = 'status-default';
                                    }
                               ?>
                               <td data-label="وضعیت"><span class="status-badge <?= $status_class ?>"><?= $status ?></span></td>
                               <td data-label="عملیات">
                                   <button class="btn btn-sm btn-info" onclick="showWorkOrderDetails(<?= $wo['id'] ?>)">جزئیات</button>
                                   <?php if (has_permission('work_order', 'edit')): ?>
                                   <button class="btn btn-sm btn-warning" onclick="window.location.href='work_order_edit.php?id=<?= $wo['id'] ?>'">ویرایش</button>
                                   <?php endif; ?>
                                   <?php if (has_permission('work_order', 'delete')): ?>
                                   <button class="btn btn-sm btn-danger" onclick="deleteWorkOrder(<?= $wo['id'] ?>)">حذف</button>
                                   <?php endif; ?>
                               </td>
                           </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
			</div>
        </div>
    </div>

    <div id="detailsModal" class="modal">
        <div id="detailsModalBody" class="modal-body" style="max-width: 900px;">
            <div class="modal-header">
                <h4 id="modalTitle">جزئیات دستور کار</h4>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <button id="finalApprovalBtn" class="btn btn-success" style="display: none;" onclick="finalApproveWorkOrder()">
                        تایید نهایی و بستن دستورکار
                    </button>
                    <a class="btn btn-secondary" onclick="closeModal('#detailsModal')">
                        بازگشت
                                </a>
                </div>
            </div>

            <!-- تب‌ها -->
            <div class="tab-container">
                <ul class="nav nav-tabs" id="detailsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details-content" type="button" role="tab">جزئیات</button>
                    </li>
                    <li class="nav-item" role="presentation" id="repair-tab-li" style="display: none;">
                        <button class="nav-link" id="repair-tab" data-bs-toggle="tab" data-bs-target="#repair-content" type="button" role="tab">گزارش تعمیر</button>
                    </li>
                    <li class="nav-item" role="presentation" id="outsourcing-tab-li" style="display: none;">
                        <button class="nav-link" id="outsourcing-tab" data-bs-toggle="tab" data-bs-target="#outsourcing-content" type="button" role="tab">گزارش برون‌سپاری</button>
                    </li>
                </ul>

                <div class="tab-content" id="detailsTabContent">
                    <div class="tab-pane fade show active" id="details-content" role="tabpanel">
                        <div id="modalContent">...در حال بارگذاری...</div>
                    </div>
                    <div class="tab-pane fade" id="repair-content" role="tabpanel">
                        <div id="repairContent">گزارش تعمیری یافت نشد.</div>
                    </div>
                    <div class="tab-pane fade" id="outsourcing-content" role="tabpanel">
                        <div id="outsourcingContent">اطلاعات برون‌سپاری یافت نشد.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div id="sourceReportModal" class="modal">
        <div class="modal-body">
            <span class="close-btn" onclick="closeModal('#sourceReportModal')">&times;</span>
            <div id="sourceReportModalContent"></div>
        </div>
    </div>

    <!-- [NEW] Repair Report Modal -->
    <div id="repairReportModal" class="modal">
        <div class="modal-body">
            <span class="close-btn" onclick="closeModal('#repairReportModal')">&times;</span>
            <div id="repairReportModalContent"></div>
        </div>
    </div>

    <!-- [NEW] Outsourcing Details Modal -->
    <div id="outsourcingModal" class="modal">
        <div class="modal-body">
            <span class="close-btn" onclick="closeModal('#outsourcingModal')">&times;</span>
            <div id="outsourcingModalContent"></div>
        </div>
    </div>

    <div id="imageViewerModal" class="image-modal">
        <span class="image-modal-close">&times;</span>
        <img class="image-modal-content" id="modalImageView">
    </div>

    <!-- ## جدید: مودال تایید حذف ## -->
    <div id="deleteConfirmModal" class="modal">
        <div class="modal-body" style="max-width: 400px; text-align: center;">
            <h4>تایید حذف</h4>
            <p>آیا از حذف این دستور کار مطمئن هستید؟ این عمل غیرقابل بازگشت است.</p>
            <div style="margin-top: 20px;">
                <button id="confirmDeleteBtn" class="btn btn-danger">بله، حذف کن</button>
                <button onclick="closeModal('#deleteConfirmModal')" class="btn btn-secondary">انصراف</button>
            </div>
        </div>
    </div>

    <!-- ## جدید: کانتینر برای اطلاع‌رسانی Toast ## -->
    <div id="toast-notification" class="toast-notification"></div>

<?php include '../includes/footer.php'; ?>

<script>
    const activities = <?= json_encode($scheduled_activities, JSON_UNESCAPED_UNICODE) ?>;
    let currentSourceReportData = null;
    let currentRepairReportData = null;
    let workOrderIdToDelete = null;


    function getStatusBadge(status) {
        let statusClass = '';
        switch (status) {
            case 'دستورکار صادر شد': statusClass = 'status-issued'; break;
            case 'در حال انجام': statusClass = 'status-in-progress'; break;
            case 'پیش‌نویس': statusClass = 'status-draft'; break;
            case 'انجام شده': statusClass = 'status-done'; break;
            case 'انجام و تایید شده': statusClass = 'status-done-confirmed'; break;
            case 'لغو شده': statusClass = 'status-cancelled'; break;
            default: statusClass = 'status-default';
        }
        return `<span class="status-badge ${statusClass}">${status}</span>`;
    }

    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast-notification');
        toast.textContent = message;
        toast.className = 'toast-notification'; // Reset classes
        toast.classList.add(type, 'show');
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    $(document).ready(function() {

		
        $('#accordion-toggle').on('click', function() {
            $(this).toggleClass('active');
            $('#accordion-panel').slideToggle(300);
        });

        // $('#device-select').select2({ ... });
        // $('#activity-select').select2({ ... });
        // $('#assignee-select').select2({ ... });
        
        initializeDatepicker('#due-date');
        initializeDatepicker('#stop-date');

        function filterActivitiesByDevice(deviceId) {
            let filtered = activities.filter(act => act.device_id == deviceId);
            let $activitySelect = $('#activity-select');
            $activitySelect.empty().append(new Option('-- بدون ارتباط --', '', true, true));
            filtered.forEach(act => $activitySelect.append(new Option(act.activity_name, act.id, false, false)));
            $activitySelect.prop('disabled', filtered.length === 0).trigger('change');
        }

        $('#device-select').on('change', function() {
            filterActivitiesByDevice($(this).val());
        });

        function handleStopTimeVisibility() {
            $('#stop-time-section').toggleClass('hidden-section', !$('#line-stopped').is(':checked'));
        }
        handleStopTimeVisibility();
        $('#line-stopped').on('change', handleStopTimeVisibility);

        $('#activity-select').prop('disabled', true);
        const deviceToSelect = '<?= $prefill_data['device_id'] ?>';
        if (deviceToSelect) {
            $('#device-select').val(deviceToSelect).trigger('change');
        }

        const imageModal = $('#imageViewerModal');
        $(document).on('click', '.thumbnail-image', function() {
            $('#modalImageView').attr('src', $(this).attr('src'));        
            imageModal.fadeIn(200);             
        });
        function closeImageModal() { imageModal.fadeOut(200); }
        imageModal.find('.image-modal-close').on('click', closeImageModal);
        imageModal.on('click', function(e) { if (e.target === this) closeImageModal(); });

        // ## جدید: اتصال رویداد به دکمه تایید حذف ##
        $('#confirmDeleteBtn').on('click', performDelete);

        // مدیریت تب‌ها
        $(document).on('click', '.nav-link', function(e) {
            e.preventDefault();
            const target = $(this).data('bs-target');

            // حذف active از همه تب‌ها
            $('.nav-link').removeClass('active');
            $('.tab-pane').removeClass('active');

            // اضافه کردن active به تب انتخاب شده
            $(this).addClass('active');
            $(target).addClass('active');
        });
    });

    let currentWorkOrderId = null;
    let currentWorkOrderData = null;

    function showWorkOrderDetails(id) {
        currentWorkOrderId = id;
        const modal = document.getElementById('detailsModal');
        const content = document.getElementById('modalContent');
        const repairContent = document.getElementById('repairContent');
        const outsourcingContent = document.getElementById('outsourcingContent');

        // Reset تب‌ها
        document.getElementById('details-tab').classList.add('active');
        document.getElementById('repair-tab').classList.remove('active');
        document.getElementById('outsourcing-tab').classList.remove('active');
        document.getElementById('details-content').classList.add('active');
        document.getElementById('repair-content').classList.remove('active');
        document.getElementById('outsourcing-content').classList.remove('active');

        // مخفی کردن تب‌های اضافی
        document.getElementById('repair-tab-li').style.display = 'none';
        document.getElementById('outsourcing-tab-li').style.display = 'none';
        document.getElementById('finalApprovalBtn').style.display = 'none';

        // تنظیم عنوان پیش‌فرض
        document.getElementById('modalTitle').textContent = 'جزئیات دستور کار';

        content.innerHTML = '... در حال بارگذاری اطلاعات ...';
        repairContent.innerHTML = 'گزارش تعمیری یافت نشد.';
        outsourcingContent.innerHTML = 'اطلاعات برون‌سپاری یافت نشد.';
        modal.style.display = 'block';

        fetch(`work_order.php?action=get_work_order_details&id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const wo = data.work_order;
                    currentSourceReportData = wo.source_report_details;
                    currentRepairReportData = data.execution;
                    currentWorkOrderData = wo;
                    
                    // برای دیباگ
                    console.log('currentRepairReportData loaded:', currentRepairReportData);
                    if (currentRepairReportData) {
                        console.log('Outsourcing fields:', {
                            exit_date: currentRepairReportData.exit_date,
                            exit_where: currentRepairReportData.exit_where,
                            exit_desc: currentRepairReportData.exit_desc,
                            back_date: currentRepairReportData.back_date,
                            back_desc: currentRepairReportData.back_desc,
                            back_pay: currentRepairReportData.back_pay
                        });
                    }
                    
                    let assignees = data.assignees.map(a => a.name).join('، ') || 'مشخص نشده';
                    let priorityClass = '';
                    switch (wo.priority.toLowerCase()) {
                        case 'بالا': case 'بحرانی': priorityClass = 'priority-high'; break;
                        case 'متوسط': priorityClass = 'priority-medium'; break;
                        case 'پایین': priorityClass = 'priority-low'; break;
                    }

                    const statusBadgeHtml = getStatusBadge(wo.status);

                    // به‌روزرسانی عنوان مودال
                    document.getElementById('modalTitle').textContent = `جزئیات دستور کار: ${wo.workorder_id}`;

                    let html = `<div class="details-grid">`;
                    html += `<div>
                                 <div class="detail-item"><strong>تاریخ ایجاد:</strong><span>${wo.request_date_shamsi}</span></div>
                                 <div class="detail-item"><strong>دستگاه:</strong><span>${wo.device_name || '-'}</span></div>                                 
                                 <div class="detail-item"><strong>وضعیت:</strong><span>${statusBadgeHtml}</span></div>
                             </div>`;
                    html += `<div>
                                 <div class="detail-item"><strong>عنوان درخواست:</strong><span>${wo.title}</span></div>
                                 <div class="detail-item"><strong>مسئول(ان) اجرا:</strong><span>${assignees}</span></div>
                                 <div class="detail-item"><strong>نوع دستور کار:</strong><span>${wo.type}</span></div>
                             </div>`;
                    html += `<div>
                                 <div class="detail-item"><strong>ایجاد کننده:</strong><span>${wo.requester_name || '-'}</span></div>
                                 <div class="detail-item"><strong>اولویت:</strong><span class="priority-badge ${priorityClass}">${wo.priority}</span></div> 
                                 <div class="detail-item"><strong>تاریخ سررسید:</strong><span>${wo.due_date_shamsi}</span></div>
                             </div>`;
                    
                    if (wo.source_report_details) {
                        html += `<div class="detail-item detail-full-width"><strong>منبع:</strong><span>بر اساس <a href="javascript:void(0)" onclick="showSourceReportModal()">گزارش خرابی شماره ${wo.source_report_details.id}</a> صادر شده است.</span></div>`;
                    }
                    if (wo.scheduled_activity_name) {
                        html += `<div class="detail-item detail-full-width"><strong>مرتبط با فعالیت برنامه‌ریزی‌شده:</strong><span>${wo.scheduled_activity_name}</span></div>`;
                    }
                    html += `<div class="detail-item detail-full-width"><strong>شرح / مشکل:</strong><div>${wo.description.replace(/\n/g, '<br>')}</div></div>`;
                    if (wo.line_stopped == 1 && wo.stop_datetime) {
                        html += `<div class="detail-item detail-full-width"><strong>توقف خط:</strong><span>بله، در تاریخ و ساعت: ${wo.stop_datetime_shamsi}</span></div>`;
                    } else {
                        html += `<div class="detail-item detail-full-width"><strong>توقف خط:</strong><span>خیر</span></div>`;
                    }
                    if (data.attachments && data.attachments.length > 0) {
                        html += `<div class="detail-item detail-full-width" style="margin-top: 1rem;"><strong>فایل‌های ضمیمه:</strong><div class="attachments-list" style="margin-top: 0.5rem; border-top: none; padding-top: 0.5rem;">`;
                        data.attachments.forEach(filePath => {
                            const normalizedPath = `../${filePath.replace('../', '')}`;
                            html += `<div class="attachment-item"><img src="${normalizedPath}" class="thumbnail-image" style="cursor: pointer;" alt="پیوست"></div>`;
                        });
                        html += `</div></div>`;
                    }
                    content.innerHTML = html;

                    // نمایش تب گزارش تعمیر اگر وجود دارد
                    if (currentRepairReportData) {
                        document.getElementById('repair-tab-li').style.display = 'block';
                        showRepairReportInTab();
                    }

                    // نمایش تب برون‌سپاری اگر وجود دارد
                    if (currentRepairReportData && (currentRepairReportData.exit_date || currentRepairReportData.back_date)) {
                        document.getElementById('outsourcing-tab-li').style.display = 'block';
                        showOutsourcingInTab();
                    }

                    // مدیریت دکمه تایید نهایی بر اساس وضعیت و نقش کاربر
                    const finalBtn = document.getElementById('finalApprovalBtn');
                    const isAdmin = <?= is_admin() ? 'true' : 'false' ?>;

                    // اگر دستور کار قبلاً بسته شده، دکمه را مخفی کن
                    if (wo.status.includes('بسته شد') || wo.status === 'لغو شده') {
                        finalBtn.style.display = 'none';
                    }
                    // اگر وضعیت "انجام و تایید شده" است
                    else if (wo.status === 'انجام و تایید شده') {
                        finalBtn.style.display = 'inline-block';
                        finalBtn.disabled = false;
                        finalBtn.textContent = 'تایید نهایی و بستن دستورکار';
                        finalBtn.setAttribute('data-action', 'final_approve');
                    }
                    // اگر وضعیت دیگری است
                    else {
                        if (isAdmin) {
                            // مدیر می‌تواند در هر وضعیتی ببندد
                            finalBtn.style.display = 'inline-block';
                            finalBtn.disabled = false;
                            finalBtn.textContent = 'بستن با وضعیت فعلی';
                            finalBtn.setAttribute('data-action', 'close_current_status');
                            finalBtn.setAttribute('data-current-status', wo.status);
                        } else {
                            // کاربر عادی نمی‌تواند ببندد
                            finalBtn.style.display = 'inline-block';
                            finalBtn.disabled = true;
                            finalBtn.textContent = 'فقط در وضعیت "انجام و تایید شده" قابل بستن است';
                        }
                    }
                } else {
                    content.innerHTML = 'خطا در دریافت اطلاعات: ' + data.message;
                }
            })
            .catch(error => {
                console.error('Fetch Error:', error);
                content.innerHTML = 'خطا در ارتباط با سرور.';
            });
	}

    function showSourceReportModal() {
        if (!currentSourceReportData) return;
        const reportData = currentSourceReportData;
        const modal = document.getElementById('sourceReportModal');
        const content = document.getElementById('sourceReportModalContent');

        let imagesHtml = '';
        if (reportData.images && reportData.images.length > 0) {
            imagesHtml += `<div class="detail-item detail-full-width"><strong>تصاویر پیوست:</strong><div class="attachments-list" style="margin-top: 0.5rem;">`;
            reportData.images.forEach(imagePath => {
                const fullImagePath = `../${imagePath.replace('../', '')}`;
                imagesHtml += `<div class="attachment-item"><img src="${fullImagePath}" alt="تصویر خرابی" class="thumbnail-image"></div>`;
            });
            imagesHtml += `</div></div>`;
        }

        let html = `
            <h4>جزئیات گزارش خرابی مبدا: #${reportData.id}</h4>
            <div class="details-grid">
                <div>
                    <div class="detail-item"><strong>دستگاه:</strong><span>${reportData.device_name}</span></div>
                    <div class="detail-item"><strong>فوریت:</strong><span>${reportData.urgency}</span></div>
                    <div class="detail-item"><strong>وضعیت گزارش:</strong><span>${reportData.status}</span></div>
                </div>
                <div>
                    <div class="detail-item"><strong>گزارش دهنده:</strong><span>${reportData.reporter_name}</span></div>
                    <div class="detail-item"><strong>زمان خرابی:</strong><span>${reportData.breakdown_datetime_shamsi}</span></div>
                    <div class="detail-item"><strong>زمان ثبت:</strong><span>${reportData.report_datetime_shamsi}</span></div>
                </div>
                <div>
                    ${reportData.line_stoppage_datetime_shamsi ? `<div class="detail-item"><strong>زمان توقف خط:</strong><span>${reportData.line_stoppage_datetime_shamsi}</span></div>` : '<div class="detail-item"><strong>توقف خط</strong><span>خیر</span></div>'}
                </div>
            </div>
            <div class="detail-item detail-full-width"><strong>شرح کامل مشکل:</strong><div>${reportData.problem_description.replace(/\n/g, '<br>')}</div></div>
            ${imagesHtml}
        `;
        content.innerHTML = html;
        modal.style.display = 'block';
    }

    // ======== MODAL: showRepairReportModal ========
    function showRepairReportModal() {
        if (!currentRepairReportData) return;
        const rep = currentRepairReportData;
        const modal = document.getElementById('repairReportModal');
        const content = document.getElementById('repairReportModalContent');

        let html = `<h4>گزارش تعمیر</h4><div class="details-grid">`;
        html += `<div class="detail-item detail-full-width"><strong>شروع:</strong><span>${rep.start_display || '-'}</span></div>`;
        html += `<div class="detail-item detail-full-width"><strong>پایان:</strong><span>${rep.end_display || '-'}</span></div>`;
        html += `<div><div class="detail-item"><strong>تحویل گیرنده:</strong><span>${rep.verifier_name || '-'}</span></div>
                        ${rep.delay_reason ? `<div class="detail-item"><strong>دلیل تاخیر:</strong><span>${rep.delay_reason.replace(/\n/g,'<br>')}</span></div>` : ''}</div>`;
        html += `<div class="detail-item detail-full-width"><strong>شرح اقدامات:</strong><div>${rep.completion_notes ? rep.completion_notes.replace(/\n/g,'<br>') : 'ثبت نشده'}</div></div>`;
        html += `<div class="detail-item detail-full-width"><strong>نفرات:</strong><span>${rep.labor_users || 'ثبت نشده'}</span></div>`;
        html += `<div class="detail-item detail-full-width"><strong>قطعات مصرفی:</strong><div>${rep.parts_used ? rep.parts_used.replace(/,/g,'<br>') : 'ثبت نشده'}</div></div>`;

        content.innerHTML = html;
        modal.style.display = 'block';
    }
    
    // ======== MODAL: showOutsourcingModal ========
    // تبدیل اعداد انگلیسی به فارسی در جاوااسکریپت
    function en_to_fa_digits_js(str) {
        const en = ['0','1','2','3','4','5','6','7','8','9'];
        const fa = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
        return String(str).replace(/[0-9]/g, function(w) {
            return fa[en.indexOf(w)];
        });
    }
    
    function showOutsourcingModal() {
        console.log('showOutsourcingModal called');
        console.log('currentRepairReportData:', currentRepairReportData);
        
        if (!currentRepairReportData) {
            console.log('currentRepairReportData is null or undefined');
            return;
        }
        
        const rep = currentRepairReportData;
        const modal = document.getElementById('outsourcingModal');
        const content = document.getElementById('outsourcingModalContent');
        console.log('Modal elements:', modal, content);
        
        let html = `<h4>اطلاعات برون‌سپاری</h4><div class="details-grid">`;
        
        // اطلاعات خروج دستگاه
        if (rep.exit_date) {
            html += `<div class="detail-item detail-full-width"><strong>تاریخ ارسال:</strong><span>${rep.exit_date}</span></div>`;
            html += `<div class="detail-item detail-full-width"><strong>به کجا:</strong><span>${rep.exit_where || '-'}</span></div>`;
            if (rep.exit_desc) {
                html += `<div class="detail-item detail-full-width"><strong>توضیحات ارسال:</strong><div>${rep.exit_desc.replace(/\n/g,'<br>')}</div></div>`;
            }
        } else {
            html += `<div class="detail-item detail-full-width"><strong>اطلاعات ارسال:</strong><span>ثبت نشده</span></div>`;
        }
        
        // اطلاعات بازگشت دستگاه
        if (rep.back_date) {
            html += `<div class="detail-item detail-full-width"><strong>تاریخ بازگشت:</strong><span>${rep.back_date}</span></div>`;
            if (rep.back_desc) {
                html += `<div class="detail-item detail-full-width"><strong>توضیحات بازگشت:</strong><div>${rep.back_desc.replace(/\n/g,'<br>')}</div></div>`;
            }
            if (rep.back_pay) {
                html += `<div class="detail-item detail-full-width"><strong>مبلغ فاکتور:</strong><span>${en_to_fa_digits_js(Number(rep.back_pay).toLocaleString())} تومان</span></div>`;
            }
        } else if (rep.exit_date) {
            html += `<div class="detail-item detail-full-width"><strong>وضعیت بازگشت:</strong><span>هنوز بازگشت داده نشده است</span></div>`;
        }
        
        html += `</div>`;
        content.innerHTML = html;
        // جلوگیری از اسکرول صفحه پشت
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        document.body.style.top = `-${scrollTop}px`;
        document.body.classList.add('modal-open');

        modal.style.display = 'block';
    }

    function closeModal(modalSelector) {
        $(modalSelector).fadeOut(200);

        // بازگرداندن اسکرول صفحه
        document.body.classList.remove('modal-open');
        const scrollTop = parseInt(document.body.style.top || '0') * -1;
        document.body.style.top = '';
        window.scrollTo(0, scrollTop);
    }

    // نمایش گزارش تعمیر در تب
    function showRepairReportInTab() {
        if (!currentRepairReportData) return;
        const rep = currentRepairReportData;
        const content = document.getElementById('repairContent');

        let html = `<div class="repair-report-layout">`;

        // ردیف اول: شروع، پایان، تحویل گیرنده (سه ستون)
        html += `<div class="row">
                    <div class="col-4">
                        <div class="detail-item"><strong>شروع:</strong><span>${rep.start_display || '-'}</span></div>
                    </div>
                    <div class="col-4">
                        <div class="detail-item"><strong>پایان:</strong><span>${rep.end_display || '-'}</span></div>
                    </div>
                    <div class="col-4">
                        <div class="detail-item"><strong>تحویل گیرنده:</strong><span>${rep.verifier_name || '-'}</span></div>
                    </div>
                 </div>`;

        // ردیف دلیل تاخیر (در صورت وجود)
        if (rep.delay_reason && rep.delay_reason.trim() !== '') {
            html += `<div class="row">
                        <div class="col-12">
                            <div class="detail-item detail-full-width">
                                <strong>دلیل تاخیر:</strong>
                                <div class="detail-content">${rep.delay_reason.replace(/\n/g,'<br>')}</div>
                            </div>
                        </div>
                     </div>`;
        }

        // ردیف دوم: شرح اقدامات (تمام عرض)
        html += `<div class="row">
                    <div class="col-12">
                        <div class="detail-item detail-full-width">
                            <strong>شرح اقدامات:</strong>
                            <div class="detail-content">${rep.completion_notes ? rep.completion_notes.replace(/\n/g,'<br>') : 'ثبت نشده'}</div>
                        </div>
                    </div>
                 </div>`;

        // ردیف سوم: نفرات و قطعات مصرفی (دو ستون)
        html += `<div class="row">
                    <div class="col-6">
                        <div class="detail-item">
                            <strong>نفرات:</strong>
                            <div class="detail-content">${rep.labor_users || 'ثبت نشده'}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="detail-item">
                            <strong>قطعات مصرفی:</strong>
                            <div class="detail-content">${rep.parts_used ? rep.parts_used.replace(/,/g,'<br>') : 'ثبت نشده'}</div>
                        </div>
                    </div>
                 </div>`;

        // ردیف چهارم: زمان شروع مجدد خط (در صورت وجود)
        if (currentWorkOrderData && currentWorkOrderData.line_stopped == 1 && currentWorkOrderData.restart_datetime_shamsi && currentWorkOrderData.restart_datetime_shamsi !== '-') {
            html += `<div class="row">
                        <div class="col-12">
                            <div class="detail-item detail-full-width">
                                <strong>زمان شروع مجدد خط تولید:</strong>
                                <span>${currentWorkOrderData.restart_datetime_shamsi}</span>
                            </div>
                        </div>
                     </div>`;
        }

        html += `</div>`;
        content.innerHTML = html;
    }

    // نمایش اطلاعات برون‌سپاری در تب
    function showOutsourcingInTab() {
        const content = document.getElementById('outsourcingContent');

        if (!currentRepairReportData) {
            content.innerHTML = '<div class="alert alert-info">اطلاعات برون‌سپاری یافت نشد.</div>';
            return;
        }

        const rep = currentRepairReportData;

        // بررسی وجود اطلاعات برون‌سپاری
        if (!rep.exit_date && !rep.back_date) {
            content.innerHTML = '<div class="alert alert-info">اطلاعات برون‌سپاری یافت نشد.</div>';
            return;
        }

        let html = `<div class="outsourcing-report-layout">`;

        // ردیف اول: تاریخ خروج، مقصد، تاریخ بازگشت (سه ستون)
        html += `<div class="row">
                    <div class="col-4">
                        <div class="detail-item"><strong>تاریخ خروج:</strong><span>${rep.exit_date_shamsi || rep.exit_date || '-'}</span></div>
                    </div>
                    <div class="col-4">
                        <div class="detail-item"><strong>مقصد:</strong><span>${rep.exit_where || '-'}</span></div>
                    </div>
                    <div class="col-4">
                        <div class="detail-item"><strong>تاریخ بازگشت:</strong><span>${rep.back_date_shamsi || rep.back_date || '-'}</span></div>
                    </div>
                 </div>`;

        // ردیف دوم: توضیحات هنگام خروج (تمام عرض)
        html += `<div class="row">
                    <div class="col-12">
                        <div class="detail-item detail-full-width">
                            <strong>توضیحات هنگام خروج:</strong>
                            <div class="detail-content">${rep.exit_desc ? rep.exit_desc.replace(/\n/g,'<br>') : '-'}</div>
                        </div>
                    </div>
                 </div>`;

        // ردیف سوم: توضیحات هنگام بازگشت (تمام عرض)
        html += `<div class="row">
                    <div class="col-12">
                        <div class="detail-item detail-full-width">
                            <strong>توضیحات هنگام بازگشت:</strong>
                            <div class="detail-content">${rep.back_desc ? rep.back_desc.replace(/\n/g,'<br>') : '-'}</div>
                        </div>
                    </div>
                 </div>`;

        // ردیف چهارم: مبلغ فاکتور (تمام عرض)
        html += `<div class="row">
                    <div class="col-12">
                        <div class="detail-item detail-full-width">
                            <strong>مبلغ فاکتور:</strong>
                            <span>${rep.back_pay ? en_to_fa_digits_js(rep.back_pay) + ' تومان' : '-'}</span>
                        </div>
                    </div>
                 </div>`;

        html += `</div>`;
        content.innerHTML = html;
    }

    // تایید نهایی دستور کار
    function finalApproveWorkOrder() {
        if (!currentWorkOrderId) return;

        const finalBtn = document.getElementById('finalApprovalBtn');
        const action = finalBtn.getAttribute('data-action');
        const currentStatus = finalBtn.getAttribute('data-current-status');

        let confirmMessage, actionType;

        if (action === 'final_approve') {
            confirmMessage = 'آیا از تایید نهایی و بستن این دستور کار مطمئن هستید؟ این عمل غیرقابل بازگشت است.';
            actionType = 'final_approve_work_order';
        } else if (action === 'close_current_status') {
            confirmMessage = `آیا از بستن این دستور کار با وضعیت فعلی "${currentStatus}" مطمئن هستید؟ این عمل غیرقابل بازگشت است.`;
            actionType = 'close_with_current_status';
        } else {
            return;
        }

        if (!confirm(confirmMessage)) {
            return;
        }

        finalBtn.disabled = true;
        finalBtn.textContent = 'در حال پردازش...';

        const formData = new FormData();
        formData.append('action', actionType);
        formData.append('work_order_id', currentWorkOrderId);
        if (currentStatus) {
            formData.append('current_status', currentStatus);
        }

        fetch('work_order.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                finalBtn.style.display = 'none';
                closeModal('#detailsModal');
                // به‌روزرسانی جدول
                location.reload();
            } else {
                showToast(data.message, 'error');
                finalBtn.disabled = false;
                // بازگرداندن متن اصلی دکمه
                if (action === 'final_approve') {
                    finalBtn.textContent = 'تایید نهایی و بستن دستورکار';
                } else {
                    finalBtn.textContent = 'بستن با وضعیت فعلی';
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('خطا در ارتباط با سرور', 'error');
            finalBtn.disabled = false;
            // بازگرداندن متن اصلی دکمه
            if (action === 'final_approve') {
                finalBtn.textContent = 'تایید نهایی و بستن دستورکار';
            } else {
                finalBtn.textContent = 'بستن با وضعیت فعلی';
            }
        });
    }

    // ## جدید: توابع مربوط به حذف ##
    function deleteWorkOrder(id) {
        workOrderIdToDelete = id;
        $('#deleteConfirmModal').fadeIn(200);
    }

    function performDelete() {
        if (!workOrderIdToDelete) return;

        const id = workOrderIdToDelete;
        const deleteBtn = $('#confirmDeleteBtn');
        deleteBtn.prop('disabled', true).text('در حال حذف...');

        const formData = new FormData();
        formData.append('action', 'delete_work_order');
        formData.append('id', id);

        fetch('work_order.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast(result.message, 'success');
                $(`tr[data-id="${id}"]`).fadeOut(400, function() { $(this).remove(); });
                closeModal('#deleteConfirmModal');
            } else {
                showToast(result.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('خطا در ارتباط با سرور.', 'error');
        })
        .finally(() => {
            deleteBtn.prop('disabled', false).text('بله، حذف کن');
            workOrderIdToDelete = null;
        });
    }
    
    $('#add-wo-form').on('submit', function(event) {
        event.preventDefault(); 
        const form = this;
        const formData = new FormData(form);
        const submitter = event.originalEvent.submitter;
        formData.set('action', $(submitter).val());

        $(submitter).prop('disabled', true).text('در حال ثبت...');

        // فایل‌ها از طریق کامپوننت file uploader مدیریت می‌شوند
        
        fetch('work_order.php', { method: 'POST', body: formData })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast(result.message, 'success');
                setTimeout(() => window.location.href = 'work_order.php', 1000);
            } else {
                showToast(result.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('خطا در ارتباط با سرور.', 'error');
        })
        .finally(() => {
            $(submitter).prop('disabled', false).text($(submitter).val() === 'draft' ? 'ذخیره به‌صورت پیش‌نویس' : 'ثبت دستور کار');
        });
    });

    const hourInput = document.getElementById('stop_hour');
    const minuteInput = document.getElementById('stop_minute');
    const hiddenTimeInput = document.getElementById('stop_time');
    if (hourInput && minuteInput) {
        const updateHiddenTime = () => {
            const hour = hourInput.value.padStart(2, '0');
            const minute = minuteInput.value.padStart(2, '0');
            hiddenTimeInput.value = `${hour}:${minute}`;
        };
        hourInput.addEventListener('input', updateHiddenTime);
        minuteInput.addEventListener('input', updateHiddenTime);
        updateHiddenTime();
        hourInput.addEventListener('keyup', () => {
            if (hourInput.value.length >= 2) minuteInput.focus();
        });
    }

    document.getElementById('workOrderSearch').addEventListener('input', function() {
        const query = this.value.toLowerCase().trim();
        const rows = document.querySelectorAll('.wo-list table tbody tr');
        rows.forEach(row => {
            const searchData = row.dataset.searchTerms || '';
            if (searchData.includes(query)) {
                row.classList.remove('hidden-row');
            } else {
                row.classList.add('hidden-row');
            }
        });
    });

</script>

</body>
</html>
