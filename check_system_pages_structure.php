<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== بررسی ساختار جدول system_pages ===\n\n";
    
    $columns = $pdo->query("DESCRIBE system_pages")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "ستون‌های موجود:\n";
    foreach ($columns as $col) {
        echo "- {$col['Field']} ({$col['Type']}) " . ($col['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
    
    echo "\nنمونه داده‌های موجود:\n";
    $sample = $pdo->query("SELECT * FROM system_pages LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sample as $row) {
        echo "ID: {$row['id']}, Name: {$row['name']}, Display: {$row['display_name']}\n";
    }
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
