<footer>
    <div class="footer-content" role="contentinfo" aria-label="پاورقی سایت">
        <p>© ۱۴۰۴ نرم افزار نت دمباز. تمامی حقوق محفوظ است.</p>
    </div>
</footer>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/modal_manager.js"></script>
<script src="../assets/js/file_uploader.js"></script>
<script src="../assets/js/select2.min.js"></script>
<script src="../assets/js/persian-date.min.js"></script>
<script src="../assets/js/persian-datepicker.min.js"></script>
<script src="../assets/js/datepicker-init.js"></script>

<script>
 $(document).ready(function() {
    // راه‌اندازی عمومی select2 فقط روی المنت‌هایی که کلاس use-select2 یا data-select2 دارند
    $('select.use-select2, select[data-select2], select.persian-select').each(function() {
        if (!$(this).hasClass('select2-hidden-accessible')) {
            const options = {
                language: 'fa',
                placeholder: $(this).data('select2-placeholder') || $(this).attr('placeholder') || 'انتخاب کنید...',
                allowClear: $(this).attr('multiple') ? true : false,
                width: '100%',
                dropdownAutoWidth: true
            };
            if ($(this).closest('.modal').length) {
                options.dropdownParent = $(this).closest('.modal');
            }
            $(this).select2(options);
        }
    });
    // راه‌اندازی عمومی datepicker فقط روی .persian-datepicker
    if ($.fn.persianDatepicker) {
        $('.persian-datepicker').each(function() {
            if (!$(this).data('has-datepicker')) {
                $(this).persianDatepicker({
     format: 'YYYY/MM/DD',
     autoClose: true,
     initialValue: false
   });
                $(this).data('has-datepicker', true);
            }
        });
    }
 });
</script>

<script>
    $(function() {
        $('select:not(.no-select2)').each(function() {
            if (!$(this).hasClass('select2-hidden-accessible')) {
                const options = {
                    language: 'fa',
                    placeholder: $(this).attr('placeholder') || 'انتخاب کنید...',
                    allowClear: $(this).attr('multiple') ? true : false,
                    width: '100%',
                    dropdownAutoWidth: true
                };

                if ($(this).closest('.modal').length) {
                    options.dropdownParent = $(this).closest('.modal');
                }

                $(this).select2(options);
            }
        });

        $(document).on('show.bs.modal', function() {
            $('.select2-container').css('z-index', '999999');
        });
    });
</script>

<script>
(function($) {
    window.markRequiredFields = function() {
        $('.required-asterisk').remove();
        $('input[required], select[required], textarea[required]').each(function() {
            var $input = $(this);
            var $label = null;
            var inputId = $input.attr('id');
            if (inputId) {
                $label = $(`label[for="${inputId}"]`);
            }
            if (!$label || $label.length === 0) {
                var $container = $input.closest('.mb-3, .form-group');
                if ($container.length) {
                    $label = $container.find('label').first();
                }
            }
            if (!$label || $label.length === 0) {
                $label = $input.prevAll('label').first();
            }
            if ($label && $label.length > 0) {
                $label.append('<span class="required-asterisk"> *</span>');
            }
        });
    }

    $(document).ready(function() {
        markRequiredFields();
    });

})(jQuery);
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const hamburger = document.getElementById('hamburger-menu');
        const navLinks = document.getElementById('nav-links');

        if (hamburger && navLinks) {
            hamburger.addEventListener('click', () => {
                navLinks.classList.toggle('active');
            });
        }
    });
</script>

<script>
// Global Toast Message Functions
function showToast(message, type = 'info', duration = 4000) {
    const container = document.getElementById('toast-container');
    if (!container) return;
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    const title = type === 'success' ? 'موفقیت' : 
                  type === 'warning' ? 'هشدار' : 
                  type === 'danger' ? 'خطا' : 'اطلاعات';
    
    toast.innerHTML = `
        <div class="toast-header">
            <span class="toast-title">${title}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
        <div class="toast-message">${message}</div>
    `;
    
    container.appendChild(toast);
    
    // Show animation
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // Auto remove after duration
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 300);
    }, duration);
}

// Show toast on page load if message exists in session
document.addEventListener('DOMContentLoaded', function() {
    // بررسی اینکه آیا صفحه خودش پیام را مدیریت می‌کند
    if (window.skipFooterToast) {
        return;
    }

    // Check if there's a toast message from PHP session
    const toastMessage = document.querySelector('meta[name="toast-message"]');
    const toastType = document.querySelector('meta[name="toast-type"]');

    if (toastMessage && toastMessage.content) {
        showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
        }
    });


</script>



</body>
</html>