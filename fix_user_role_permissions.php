<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "اصلاح مجوزهای نقش user...\n\n";
    
    // دریافت ID نقش user
    $user_role_stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'user'");
    $user_role_stmt->execute();
    $user_role_id = $user_role_stmt->fetchColumn();
    
    if (!$user_role_id) {
        echo "نقش user یافت نشد!\n";
        exit;
    }
    
    // مجوزهای مناسب برای نقش user
    $permissions_to_add = [
        'work_order' => ['view_own'],
        'reports_list' => ['view_own'],
        'dashboard' => ['view_own_activities'],
        'user_management' => ['edit_own_profile']
    ];
    
    foreach ($permissions_to_add as $page_name => $perms) {
        // دریافت ID صفحه
        $page_stmt = $pdo->prepare("SELECT id FROM system_pages WHERE name = ?");
        $page_stmt->execute([$page_name]);
        $page_id = $page_stmt->fetchColumn();
        
        if (!$page_id) {
            echo "صفحه $page_name یافت نشد!\n";
            continue;
        }
        
        foreach ($perms as $perm_name) {
            // دریافت ID مجوز
            $perm_stmt = $pdo->prepare("SELECT id FROM permissions WHERE page_id = ? AND name = ?");
            $perm_stmt->execute([$page_id, $perm_name]);
            $perm_id = $perm_stmt->fetchColumn();
            
            if (!$perm_id) {
                echo "مجوز $perm_name برای صفحه $page_name یافت نشد!\n";
                continue;
            }
            
            // بررسی اینکه مجوز از قبل تخصیص یافته یا نه
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission_id = ?");
            $check_stmt->execute([$user_role_id, $perm_id]);
            
            if ($check_stmt->fetchColumn() == 0) {
                // اضافه کردن مجوز
                $insert_stmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                $insert_stmt->execute([$user_role_id, $perm_id]);
                echo "✓ مجوز '$perm_name' برای صفحه '$page_name' به نقش user اضافه شد\n";
            } else {
                echo "- مجوز '$perm_name' برای صفحه '$page_name' از قبل موجود است\n";
            }
        }
    }
    
    // حذف مجوز view عمومی work_order (چون حالا view_own داریم)
    $work_order_view_stmt = $pdo->prepare("
        DELETE rp FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.id
        JOIN system_pages sp ON p.page_id = sp.id
        WHERE rp.role_id = ? AND sp.name = 'work_order' AND p.name = 'view'
    ");
    $work_order_view_stmt->execute([$user_role_id]);
    
    if ($work_order_view_stmt->rowCount() > 0) {
        echo "✓ مجوز 'view' عمومی work_order از نقش user حذف شد\n";
    }
    
    echo "\n=== نتیجه نهایی ===\n";
    echo "مجوزهای جدید نقش user:\n";
    
    $final_perms = $pdo->prepare("
        SELECT sp.name as page_name, p.name as permission_name, p.display_name
        FROM role_permissions rp
        JOIN permissions p ON rp.permission_id = p.id
        JOIN system_pages sp ON p.page_id = sp.id
        WHERE rp.role_id = ?
        ORDER BY sp.name, p.name
    ");
    $final_perms->execute([$user_role_id]);
    
    $current_page = '';
    while ($perm = $final_perms->fetch(PDO::FETCH_ASSOC)) {
        if ($current_page !== $perm['page_name']) {
            $current_page = $perm['page_name'];
            echo "\n{$perm['page_name']}:\n";
        }
        echo "  - {$perm['permission_name']} ({$perm['display_name']})\n";
    }
    
    echo "\nعملیات با موفقیت انجام شد!\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
