<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "شروع حذف مجوزهای غیرضروری...\n\n";
    
    // لیست مجوزهای غیرضروری برای هر صفحه
    $unnecessary_permissions = [
        'my_tasks' => ['create', 'edit', 'delete', 'reassign', 'change_priority', 'view_team_tasks'],
        'activities' => ['assign_to_device', 'schedule'],
        'devices' => ['export', 'manage_images', 'view_history'],
        'reports_list' => ['edit', 'delete', 'convert_to_wo'],
        'user_management' => ['export', 'import', 'bulk_operations', 'reset_password', 'view_activity_log']
    ];
    
    $total_removed = 0;
    
    foreach ($unnecessary_permissions as $page_name => $permissions) {
        echo "حذف مجوزهای غیرضروری برای صفحه $page_name:\n";
        
        // دریافت ID صفحه
        $page_stmt = $pdo->prepare("SELECT id FROM system_pages WHERE name = ?");
        $page_stmt->execute([$page_name]);
        $page_id = $page_stmt->fetchColumn();
        
        if (!$page_id) {
            echo "  - صفحه $page_name یافت نشد!\n";
            continue;
        }
        
        foreach ($permissions as $permission_name) {
            // دریافت ID مجوز
            $perm_stmt = $pdo->prepare("SELECT id FROM permissions WHERE page_id = ? AND name = ?");
            $perm_stmt->execute([$page_id, $permission_name]);
            $permission_id = $perm_stmt->fetchColumn();
            
            if ($permission_id) {
                // حذف از role_permissions
                $role_perm_stmt = $pdo->prepare("DELETE FROM role_permissions WHERE permission_id = ?");
                $role_perm_stmt->execute([$permission_id]);
                $role_perm_count = $role_perm_stmt->rowCount();
                
                // حذف از permissions
                $perm_delete_stmt = $pdo->prepare("DELETE FROM permissions WHERE id = ?");
                $perm_delete_stmt->execute([$permission_id]);
                
                echo "  ✓ حذف مجوز '$permission_name' (تعداد تخصیص‌های حذف شده: $role_perm_count)\n";
                $total_removed++;
            } else {
                echo "  - مجوز '$permission_name' یافت نشد\n";
            }
        }
        echo "\n";
    }
    
    echo "=== خلاصه ===\n";
    echo "تعداد کل مجوزهای حذف شده: $total_removed\n\n";
    
    // نمایش مجوزهای باقی‌مانده برای هر صفحه
    echo "مجوزهای باقی‌مانده:\n";
    echo "==================\n";
    
    $remaining_stmt = $pdo->query("
        SELECT sp.name as page_name, p.name as permission_name, p.display_name
        FROM permissions p
        JOIN system_pages sp ON p.page_id = sp.id
        ORDER BY sp.name, p.name
    ");
    
    $current_page = '';
    while ($row = $remaining_stmt->fetch(PDO::FETCH_ASSOC)) {
        if ($current_page !== $row['page_name']) {
            $current_page = $row['page_name'];
            echo "\n$current_page:\n";
        }
        echo "  - {$row['permission_name']} ({$row['display_name']})\n";
    }
    
    echo "\nعملیات با موفقیت انجام شد!\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
