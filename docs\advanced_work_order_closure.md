# پیاده‌سازی سیستم پیشرفته بستن دستور کار

## تغییرات انجام شده

### 1. تغییر ساختار دیتابیس

#### مشکل ENUM:
فیلدهای `status` در جداول `work_orders` و `breakdown_reports` از نوع `ENUM` بودند که امکان ایجاد وضعیت‌های پویا را نداشتند.

#### راه‌حل:
```sql
-- قبل
status ENUM('پیش‌نویس', 'دستورکار صادر شد', ...) NOT NULL DEFAULT 'دستورکار صادر شد',

-- بعد  
status VARCHAR(100) NOT NULL DEFAULT 'دستورکار صادر شد',
```

### 2. منطق پیچیده دکمه بستن

#### سه حالت مختلف:

**حالت 1: وضعیت "انجام و تایید شده"**
- کاربر عادی: دکمه فعال با متن "تایید نهایی و بستن دستورکار"
- مدیر: دکمه فعال با متن "تایید نهایی و بستن دستورکار"
- عملیات: تغییر وضعیت به "تایید نهایی و بسته شد"

**حالت 2: وضعیت‌های دیگر + کاربر عادی**
- دکمه غیرفعال با متن "فقط در وضعیت 'انجام و تایید شده' قابل بستن است"

**حالت 3: وضعیت‌های دیگر + مدیر**
- دکمه فعال با متن "بستن با وضعیت فعلی"
- عملیات: تغییر وضعیت به "در وضعیت '[نام وضعیت فعلی]' بسته شد"

### 3. کد JavaScript

```javascript
// مدیریت دکمه تایید نهایی بر اساس وضعیت و نقش کاربر
const finalBtn = document.getElementById('finalApprovalBtn');
const isAdmin = <?= is_admin() ? 'true' : 'false' ?>;

// اگر دستور کار قبلاً بسته شده، دکمه را مخفی کن
if (wo.status.includes('بسته شد') || wo.status === 'لغو شده') {
    finalBtn.style.display = 'none';
}
// اگر وضعیت "انجام و تایید شده" است
else if (wo.status === 'انجام و تایید شده') {
    finalBtn.style.display = 'inline-block';
    finalBtn.disabled = false;
    finalBtn.textContent = 'تایید نهایی و بستن دستورکار';
    finalBtn.setAttribute('data-action', 'final_approve');
}
// اگر وضعیت دیگری است
else {
    if (isAdmin) {
        // مدیر می‌تواند در هر وضعیتی ببندد
        finalBtn.style.display = 'inline-block';
        finalBtn.disabled = false;
        finalBtn.textContent = 'بستن با وضعیت فعلی';
        finalBtn.setAttribute('data-action', 'close_current_status');
        finalBtn.setAttribute('data-current-status', wo.status);
    } else {
        // کاربر عادی نمی‌تواند ببندد
        finalBtn.style.display = 'inline-block';
        finalBtn.disabled = true;
        finalBtn.textContent = 'فقط در وضعیت "انجام و تایید شده" قابل بستن است';
    }
}
```

### 4. تابع finalApproveWorkOrder

```javascript
function finalApproveWorkOrder() {
    if (!currentWorkOrderId) return;
    
    const finalBtn = document.getElementById('finalApprovalBtn');
    const action = finalBtn.getAttribute('data-action');
    const currentStatus = finalBtn.getAttribute('data-current-status');
    
    let confirmMessage, actionType;
    
    if (action === 'final_approve') {
        confirmMessage = 'آیا از تایید نهایی و بستن این دستور کار مطمئن هستید؟';
        actionType = 'final_approve_work_order';
    } else if (action === 'close_current_status') {
        confirmMessage = `آیا از بستن این دستور کار با وضعیت فعلی "${currentStatus}" مطمئن هستید؟`;
        actionType = 'close_with_current_status';
    }
    
    if (!confirm(confirmMessage)) return;
    
    // ارسال درخواست AJAX
    const formData = new FormData();
    formData.append('action', actionType);
    formData.append('work_order_id', currentWorkOrderId);
    if (currentStatus) {
        formData.append('current_status', currentStatus);
    }
    
    // ... ادامه کد
}
```

### 5. کد PHP

#### تایید نهایی عادی:
```php
else if ($action === 'final_approve_work_order') {
    $work_order_id = $_POST['work_order_id'] ?? 0;
    
    try {
        $db->beginTransaction();
        
        // به‌روزرسانی وضعیت دستور کار
        $stmt = $db->prepare("UPDATE work_orders SET status = 'تایید نهایی و بسته شد' WHERE id = ?");
        $stmt->execute([$work_order_id]);
        
        // به‌روزرسانی وضعیت گزارش خرابی مرتبط
        $stmt = $db->prepare("UPDATE breakdown_reports SET status = 'تایید نهایی و بسته شد' WHERE converted_to_wo_id = ?");
        $stmt->execute([$work_order_id]);
        
        $db->commit();
        json_response(['success' => true, 'message' => 'دستور کار با موفقیت تایید نهایی و بسته شد.']);
    } catch (Exception $e) {
        $db->rollBack();
        json_response(['success' => false, 'message' => 'خطا در تایید نهایی: ' . $e->getMessage()]);
    }
}
```

#### بستن با وضعیت فعلی (فقط مدیران):
```php
else if ($action === 'close_with_current_status') {
    // بررسی دسترسی مدیریت
    if (!is_admin()) {
        json_response(['success' => false, 'message' => 'شما مجاز به انجام این عملیات نیستید.'], 403);
    }
    
    $work_order_id = $_POST['work_order_id'] ?? 0;
    $current_status = $_POST['current_status'] ?? '';
    
    try {
        $db->beginTransaction();
        
        // ایجاد وضعیت جدید
        $new_status = "در وضعیت \"$current_status\" بسته شد";
        
        // به‌روزرسانی وضعیت دستور کار
        $stmt = $db->prepare("UPDATE work_orders SET status = ? WHERE id = ?");
        $stmt->execute([$new_status, $work_order_id]);
        
        // به‌روزرسانی وضعیت گزارش خرابی مرتبط
        $stmt = $db->prepare("UPDATE breakdown_reports SET status = ? WHERE converted_to_wo_id = ?");
        $stmt->execute([$new_status, $work_order_id]);
        
        $db->commit();
        json_response(['success' => true, 'message' => "دستور کار با وضعیت \"$current_status\" بسته شد."]);
    } catch (Exception $e) {
        $db->rollBack();
        json_response(['success' => false, 'message' => 'خطا در بستن دستور کار: ' . $e->getMessage()]);
    }
}
```

## مزایای این پیاده‌سازی

### 1. کنترل دسترسی دقیق
- کاربران عادی فقط در وضعیت مناسب می‌توانند ببندند
- مدیران در هر وضعیتی امکان بستن دارند

### 2. ردیابی دقیق وضعیت
- وضعیت‌های بسته شده به صورت توصیفی ثبت می‌شوند
- مثال: "در وضعیت 'در حال انجام' بسته شد"

### 3. انعطاف‌پذیری
- تغییر از ENUM به VARCHAR امکان وضعیت‌های پویا را فراهم کرد
- عدم محدودیت در تعداد و نوع وضعیت‌ها

### 4. تجربه کاربری بهتر
- پیام‌های واضح برای کاربر
- دکمه‌های متناسب با نقش کاربر
- تایید مناسب قبل از انجام عملیات

## نتیجه

### قبل از تغییر:
- ❌ فقط یک نوع بستن دستور کار
- ❌ محدودیت ENUM در وضعیت‌ها
- ❌ عدم تفکیک دسترسی

### بعد از تغییر:
- ✅ دو نوع بستن: عادی و مدیریتی
- ✅ وضعیت‌های پویا و توصیفی
- ✅ کنترل دسترسی دقیق
- ✅ ردیابی کامل تغییرات
- ✅ تجربه کاربری بهبود یافته
