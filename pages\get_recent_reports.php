<?php
// get_recent_reports.php
require_once '../includes/auth.php';
require_once '../db_connection.php';

header('Content-Type: application/json');
$pdo = db_connect();

$deviceId = $_GET['device_id'] ?? 0;

if (!$deviceId) {
    echo json_encode(['success' => false, 'reports' => []]);
    exit;
}

try {
    // فقط 3 گزارش آخر که هنوز به دستور کار تبدیل نشده‌اند را برمی‌گردانیم
    $stmt = $pdo->prepare(
        "SELECT problem_description, status, report_datetime 
         FROM breakdown_reports 
         WHERE device_id = :device_id AND status = 'گزارش شده'
         ORDER BY report_datetime DESC 
         LIMIT 3"
    );
    $stmt->execute([':device_id' => $deviceId]);
    $reports = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode(['success' => true, 'reports' => $reports]);

} catch (PDOException $e) {
    echo json_encode(['success' => false, 'reports' => []]);
}
?>