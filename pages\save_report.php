<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'report_builder', 'create')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'عدم دسترسی']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'متد نامعتبر']);
    exit;
}

$name = trim($_POST['name'] ?? '');
$config = $_POST['config'] ?? '';
$templateId = $_POST['template_id'] ?? null;

if (empty($name)) {
    echo json_encode(['success' => false, 'message' => 'نام گزارش الزامی است']);
    exit;
}

if (empty($config)) {
    echo json_encode(['success' => false, 'message' => 'تنظیمات گزارش الزامی است']);
    exit;
}

// اعتبارسنجی JSON
$configArray = json_decode($config, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode(['success' => false, 'message' => 'تنظیمات نامعتبر']);
    exit;
}

try {
    $pdo->beginTransaction();
    
    // اگر template_id ارسال شده، از آن استفاده کن، وگرنه قالب جدید ایجاد کن
    if ($templateId) {
        // بررسی وجود قالب
        $stmt = $pdo->prepare("SELECT id FROM report_templates WHERE id = ?");
        $stmt->execute([$templateId]);
        if (!$stmt->fetch()) {
            throw new Exception('قالب مورد نظر یافت نشد');
        }
    } else {
        // ایجاد قالب جدید
        $stmt = $pdo->prepare("
            INSERT INTO report_templates (name, description, config, created_by) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $name . ' - قالب',
            'قالب ایجاد شده از گزارش ' . $name,
            $config,
            $_SESSION['user_id']
        ]);
        $templateId = $pdo->lastInsertId();
    }
    
    // ذخیره گزارش
    $stmt = $pdo->prepare("
        INSERT INTO saved_reports (template_id, name, filters, created_by) 
        VALUES (?, ?, ?, ?)
    ");
    $stmt->execute([
        $templateId,
        $name,
        json_encode($configArray['filters'] ?? []),
        $_SESSION['user_id']
    ]);
    
    $reportId = $pdo->lastInsertId();
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'گزارش با موفقیت ذخیره شد',
        'report_id' => $reportId
    ]);
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo json_encode([
        'success' => false, 
        'message' => 'خطا در ذخیره گزارش: ' . $e->getMessage()
    ]);
}
?>
