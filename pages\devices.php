<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php'; // اضافه کردن فایل تبدیل تاریخ

// بررسی دسترسی به صفحه دستگاه‌ها
require_page_access('devices', 'view');

$pdo = db_connect();
// START DEVICE REPAIR HISTORY HELPERS
if (!function_exists('to_persian_numerals')) {
    function to_persian_numerals($string) {
        $persian_digits = ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'];
        $english_digits = ['0','1','2','3','4','5','6','7','8','9'];
        return str_replace($english_digits, $persian_digits, $string);
    }
}
if (!function_exists('format_shamsi_datetime')) {
    function format_shamsi_datetime($datetime_string) {
        if (empty($datetime_string) || str_starts_with($datetime_string,'0000-00-00')) {
            return '-';
        }
        try {
            $date_obj = new DateTime($datetime_string);
            $shamsi_date = to_shamsi($datetime_string,'Y/m/d');
            if (strpos($datetime_string,' ') !== false && $date_obj->format('H:i:s') !== '00:00:00') {
                $time = $date_obj->format('H:i');
                return $shamsi_date.'، ساعت '.to_persian_numerals($time);
            }
            return $shamsi_date;
        } catch (Exception $e) {
            return to_shamsi($datetime_string,'Y/m/d');
        }
    }
}
if (!function_exists('json_response')) {
    function json_response($data, $statusCode = 200) {
        if (ob_get_level() > 0) { ob_clean(); }
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
// Handle AJAX request for device history
if (isset($_GET['action']) && $_GET['action'] === 'get_device_history') {
    $device_id = $_GET['device_id'] ?? 0;
    if (empty($device_id)) {
        json_response(['success'=>false,'message'=>'شناسه دستگاه نامعتبر است.'],400);
    }
    $stmt = $pdo->prepare("
        SELECT
            wo.id, wo.title, wo.status,
            exec.actual_start_datetime,
            exec.actual_end_datetime,
            exec.completion_notes,
            exec.delay_reason,
            exec.exit_date,
            exec.exit_where,
            exec.exit_desc,
            exec.back_date,
            exec.back_desc,
            exec.back_pay,
            u_verifier.name as verifier_name,
            (SELECT GROUP_CONCAT(u.name) FROM work_order_labor wol JOIN users u ON wol.user_id=u.id WHERE wol.work_order_id = wo.id) as labor_users,
            (SELECT GROUP_CONCAT(wop.part_name, ' (', wop.quantity_used, ' ', wop.unit, ')') FROM work_order_parts wop WHERE wop.work_order_id = wo.id) as parts_used
        FROM work_orders wo
        LEFT JOIN work_order_execution exec ON wo.id = exec.work_order_id
        LEFT JOIN users u_verifier ON wo.verifier_id = u_verifier.id
        WHERE wo.device_id = ?
        ORDER BY exec.actual_end_datetime DESC LIMIT 100
    ");
    $stmt->execute([$device_id]);
    $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($history as &$task) {
        $task['actual_start_datetime_shamsi'] = format_shamsi_datetime($task['actual_start_datetime']);
        $task['actual_end_datetime_shamsi'] = format_shamsi_datetime($task['actual_end_datetime']);
        $task['exit_date_shamsi'] = $task['exit_date'] ? to_shamsi($task['exit_date']) : null;
        $task['back_date_shamsi'] = $task['back_date'] ? to_shamsi($task['back_date']) : null;
    }
    json_response(['success'=>true,'history'=>$history]);
}
// END DEVICE REPAIR HISTORY HELPERS

function get_locations() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, location_name FROM locations ORDER BY location_name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// افزودن دستگاه جدید و قطعات مصرفی
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_device') {
    // بررسی مجوز ایجاد دستگاه
    require_page_access('devices', 'create');

    try {
        $uploaded_filenames = [];
        if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
            $file_count = count($_FILES['images']['name']);
            $targetDir = "../uploads/devices/";
            if (!is_dir($targetDir)) mkdir($targetDir, 0755, true);

            for ($i = 0; $i < $file_count; $i++) {
                if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                    $filename = uniqid() . "_" . basename($_FILES['images']['name'][$i]);
                    if (move_uploaded_file($_FILES['images']['tmp_name'][$i], $targetDir . $filename)) {
                        $uploaded_filenames[] = $filename;
                    }
                }
            }
        }
        $images_json = !empty($uploaded_filenames) ? json_encode($uploaded_filenames) : null;

        $purchase_date = !empty($_POST['purchase_date']) ? to_miladi($_POST['purchase_date']) : null;
        
        $pdo->beginTransaction();

        $stmt = $pdo->prepare("INSERT INTO devices 
        (name, serial_number, description, status, purchase_date, vendor_name, vendor_phone, images, location) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $_POST['name'],
            $_POST['serial_number'],
            $_POST['description'],
            $_POST['status'],
            $purchase_date,
            $_POST['vendor_name'],
            $_POST['vendor_phone'],
            $images_json,
            $_POST['location']
        ]);
        $deviceId = $pdo->lastInsertId();

        if (!empty($_POST['consumable_name']) && is_array($_POST['consumable_name'])) {
            $stmtPart = $pdo->prepare("INSERT INTO device_parts (device_id, part_name, quantity, unit, description, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            foreach ($_POST['consumable_name'] as $index => $partName) {
                $quantity = isset($_POST['consumable_quantity'][$index]) ? (int)$_POST['consumable_quantity'][$index] : 1;
                $unit = trim($_POST['consumable_unit'][$index] ?? 'عدد');
                $desc = $_POST['consumable_description'][$index] ?? null;
                if(trim($partName) !== '') {
                    $stmtPart->execute([$deviceId, $partName, $quantity, $unit, $desc]);
                }
            }
        }

        $pdo->commit();

        $_SESSION['toast_message'] = 'دستگاه با موفقیت افزوده شد.';
        $_SESSION['toast_type'] = 'success';
        header('Location: devices.php');
        exit;

    } catch (PDOException $e) {
        $pdo->rollBack();
        $_SESSION['toast_message'] = 'خطا در پایگاه داده: ' . $e->getMessage();
        $_SESSION['toast_type'] = 'danger';
        header('Location: devices.php');
        exit;
    }
}

$locations = get_locations();
$devices = $pdo->query("
    SELECT d.*, l.location_name 
    FROM devices d
    LEFT JOIN locations l ON d.location = l.id
    ORDER BY updated_at DESC, created_at DESC
")->fetchAll();

// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}

include '../includes/header.php';
?>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/persian-datepicker.min.css">
    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/persian-date.min.js"></script>
    <script src="../assets/js/persian-datepicker.min.js"></script>
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>

    <style>
        * { box-sizing: border-box; }
        body { overflow-x: hidden; }

        
        .device-list { flex: 2; }
        .table-scroll-container { width: 100%; overflow-x: auto; }
        .toggle-icon { display: none; }
        
        .form-row { margin-bottom: 15px; }
        .form-row:last-child { margin-bottom: 0; }
        .form-row label { display: block; margin-bottom: 5px; }
        #consumableFieldset { border: 1px solid #ddd; padding: 10px; border-radius: 5px; min-width: 0; }

        /* **اصلاح کامل**: استایل مودال برای نمایش صحیح در مرکز */
        #deviceDetailsModal {
            position: fixed;
            z-index: 1000;
            inset: 0; /* Shorthand for top, right, bottom, left = 0 */
            background-color: rgba(0,0,0,0.6);
            display: none; /* Changed to flex by JS */
            align-items: center;
            justify-content: center;
            padding: 15px;
        }
        #deviceDetailsModalBody {
            background-color: #fefefe;
            padding: 20px;
            border-radius: 8px;
            width: 90%; /* Responsive width */
            max-width: 600px;
            max-height: 90vh; /* Limit height */
            overflow-y: auto; /* Allow content to scroll */
            position: relative;
            word-wrap: break-word; /* Break long words */
        }
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: var(--fs-3xl);
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        .modal-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .modal-gallery img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        #imagePreviewContainer { display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px; padding: 10px; border: 1px dashed #ccc; border-radius: 5px; min-height: 80px; }
        .image-preview { position: relative; width: 100px; height: 100px; }
        .image-preview img { width: 100%; height: 100%; object-fit: cover; border-radius: 5px; }
        .delete-image-btn { position: absolute; top: -8px; right: -8px; background-color: #ff4d4d; color: white; border: 2px solid white; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-weight: bold; display: flex; align-items: center; justify-content: center; padding: 0; font-size: 14px; line-height: 1; }

        @media (max-width: 768px) {
			.accardeon-panel {border-top: none;border-radius: 0 0 8px 8px;}
            .device-container { flex-direction: column; padding: 10px; }
            .add-device, .device-list { width: 100%; flex: none; }
            .accordion-trigger { display: flex; justify-content: space-between; align-items: center; background-color: #f2f2f2; border-radius: 8px; cursor: pointer; padding: 0px 5px; }
            .accordion-trigger h3 { margin: 0; font-size: var(--fs-md); }
            .toggle-icon { display: block; font-size: var(--fs-xl); font-weight: bold; transition: transform 0.2s ease-in-out; }
            .accordion-trigger.active .toggle-icon { transform: rotate(45deg); }
            #addDeviceForm { display: none; border: 1px solid #ddd; border-top: none; padding: 15px; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; }
            .add-device form input, .add-device form select, .add-device form textarea { width: 100%; }
            .consumable-row { display: flex; flex-wrap: wrap; gap: 5px; }
            .consumable-row input[type="text"] { flex-grow: 1; min-width: 100px; }
            .consumable-row input[type="number"] { width: 70px !important; flex-grow: 0; }
            .device-list table { border: none; }
            .device-list table thead { display: none; }
            .device-list table tr { 
                display: block; 
                margin-bottom: 15px; 
                border: 1px solid #ddd; 
                border-radius: 8px; 
                padding: 15px; 
                background-color: transparent; /* **اصلاح**: حذف پس‌زمینه سفید */
                box-shadow: 0 2px 4px rgba(0,0,0,0.05); 
            }
            .device-list table td { display: flex; justify-content: space-between; align-items: center; padding: 10px 5px; text-align: right; border-bottom: 1px dotted #eee; }
            .device-list table td:last-child { border-bottom: none; }
            .device-list table td::before { content: attr(data-label); font-weight: bold; margin-left: 10px; color: #333; }
            
            /* **اصلاح**: چیدمان جدید برای دکمه‌های عملیات */
            .device-list table td[data-label="عملیات"] { 
                display: flex;
                flex-direction: row;
                justify-content: left;
                gap: 8px; /* فاصله بین دکمه‌ها */
                padding: 10px 0 0 0; /* فاصله از بالای سلول */
            }
            .device-list table td[data-label="عملیات"]::before { display: none; }
            .device-list table td[data-label="عملیات"] button, 
            .device-list table td[data-label="عملیات"] a {
                display: block;
                text-align: center;
            }
            .device-list table td[data-label="عملیات"] a button {
                width: 100%; /* دکمه داخل لینک عرض کامل بگیرد */
            }
        }/* ------ Device repair history modal styles ------ */
#device-history-modal { display:none; position:fixed; inset:0; background-color:rgba(0,0,0,0.6); z-index:1000; justify-content:center; align-items:center; }
#device-history-modal .modal-header { padding: 1rem 1.5rem; border-bottom: 1px solid #e9ecef; display: flex; flex-direction: column; align-items: stretch; gap: 1rem; flex-shrink: 0; }
#device-history-modal .modal-header-top { display: flex; justify-content: space-between; align-items: center; }
#device-history-modal .modal-body { padding: 0.5rem 1.5rem 1.5rem 1.5rem; }
#device-history-modal .history-filters { display: flex; flex-direction: column; gap: 1rem; }
#device-history-modal .filter-actions { display: flex; justify-content: space-between; align-items: center; gap: 1rem; }
#device-history-modal .advanced-filters { display: none; flex-direction: column; gap: 1rem; padding: 1rem; border-radius: 6px; background-color: #f8f9fa; border: 1px solid #e9ecef; margin-top: 0.5rem; }
#device-history-modal .filter-row { display: flex; flex-direction: column; gap: 0.5rem; }
#device-history-modal .filter-row input, #device-history-modal .filter-row select { width: 100%; padding: 0.5rem; border: 1px solid #ccc; border-radius: 4px; }
        #device-history-modal #toggle-filters-btn { background: none; border: 1px dashed #aaa; color: #555; padding: 0.5rem 1rem; border-radius: 6px; flex-grow: 1; text-align: right; cursor: pointer; }
        #device-history-modal #toggle-filters-btn .icon { float: left; transition: transform 0.3s; }
        #device-history-modal #toggle-filters-btn.open .icon { transform: rotate(180deg); }
        #device-history-modal #clear-filters-btn { background: none; border: none; color: #dc3545; font-size: var(--fs-2xl); cursor: pointer; padding: 0 0.5rem; }
        #device-history-modal .history-cards-container { max-height: 55vh; overflow-y: auto; padding: 5px; }
        #device-history-modal .history-card { background: #fff; border-radius: 8px; border-left: 5px solid #6c757d; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.08); padding: 0; }
        #device-history-modal .history-card-main { cursor: pointer; padding: 1rem; position: relative; }
        #device-history-modal .history-card-summary { flex-grow: 1; padding-left: 2rem; }
        #device-history-modal .history-card-summary p { margin: 0.25rem 0; color: #555; }
        #device-history-modal .history-card-title-line { display: flex; justify-content: space-between; align-items: center; gap: 1rem; margin-bottom: 0.5rem; }
        #device-history-modal .history-card-title-line strong { color: #333; flex-grow: 1; }
        #device-history-modal .history-card-toggle { position: absolute; bottom: 0.5rem; left: 0.5rem; background: none; border: none; font-size: var(--fs-sm); color: #aaaaaa; cursor: pointer; padding: 0.2rem; transition: transform 0.3s ease; }
        #device-history-modal .history-card.active .history-card-toggle { transform: rotate(180deg); }
        #device-history-modal .history-card-collapsible-content { max-height: 0; overflow: hidden; transition: max-height 0.4s ease-out; }
        #device-history-modal .history-card.active .history-card-collapsible-content { max-height: max-content; transition: max-height 0.5s ease-in; }
        #device-history-modal .history-card-details { padding: 0 1rem 1rem 1rem; }
        #device-history-modal .history-card-details p { font-size: var(--fs-sm); color: #555; }
        #device-history-modal .history-card-details p strong { color: #333; }
        #device-history-modal .status-badge, .priority-badge { padding: 0.2em 0.6em; border-radius: 0.25rem; font-size: var(--fs-xs); font-weight: bold; color: white; flex-shrink: 0; }
        #device-history-modal .status-انجام-شده { background-color: #28a745; }
        #device-history-modal .status-منتظر-تایید { background-color: #ffc107; color: #333; }
        #device-history-modal .status-تایید-شده { background-color: #17a2b8; }
        #device-history-modal .status-بسته-شده { background-color: #6c757d; }
        #device-history-modal .status-done-confirmed { background-color: #28a745 !important; color: #fff !important; }
        #device-history-modal .status-rejected { background-color: #dc3545 !important; color: #fff !important; }
        #device-history-modal .history-card-divider { border: 0; border-top: 1px solid #eee; margin: 0 1rem; }
        #device-history-modal .modal-content { width: 80%; padding: 38px 20px !important; }

        /* استایل‌های بخش برون‌سپاری */
        #device-history-modal .outsourcing-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 20px;
        }

        #device-history-modal .outsourcing-section h5 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            font-weight: 600;
        }

        #device-history-modal .outsourcing-section p {
            margin: 5px 0;
            font-size: 0.9rem;
        }

        #device-history-modal .outsourcing-section p strong {
            color: #495057;
        }

        /* استایل کانتینر وضعیت و آیکون برون‌سپاری */
        #device-history-modal .status-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #device-history-modal .outsourcing-icon {
            color: #007bff;
            font-size: 14px;
            cursor: help;
        }
/* ----------------------------------------------- */
    </style>
</head>
<body>
<div class="device-container">
    <?php if (has_permission('devices', 'create')): ?>
    <div class="add-device">
        <div class="accordion-trigger">
            <h3>تعریف دستگاه جدید</h3>
            <span class="toggle-icon">+</span>
        </div>
		<div class="accardeon-panel">
        <form method="post" enctype="multipart/form-data" id="addDeviceForm">
            <input type="hidden" name="action" value="add_device">
            <div class="form-row"><label for="name">نام دستگاه:</label><input type="text" id="name" name="name" required></div>
            <div class="form-row"><label for="serial_number">شماره سریال:</label><input type="text" id="serial_number" name="serial_number"></div>
            <div class="form-row"><label for="location">محل استفاده:</label><select name="location" id="location" required><option value="">انتخاب محل استفاده</option><?php foreach ($locations as $loc): ?><option value="<?= $loc['id'] ?>"><?= htmlspecialchars($loc['location_name']) ?></option><?php endforeach; ?></select></div>
            <div class="form-row"><label for="status">وضعیت:</label><select name="status" id="status" required><option value="فعال">فعال</option><option value="غیرفعال">غیرفعال</option><option value="در حال تعمیر">در حال تعمیر</option></select></div>
            <div class="form-row"><label for="purchase_date">تاریخ خرید:</label><input type="text" class="persian-datepicker" id="purchase_date" name="purchase_date" placeholder="انتخاب تاریخ" autocomplete="off"></div>
            <div class="form-row"><label for="vendor_name">فروشنده:</label><input type="text" id="vendor_name" name="vendor_name"></div>
            <div class="form-row"><label for="vendor_phone">شماره تماس فروشنده:</label><input type="tel" id="vendor_phone" name="vendor_phone"></div>
            <div class="form-row"><label>قطعات مصرفی:</label><fieldset id="consumableFieldset"><div id="consumablesContainer"></div><button type="button" onclick="addConsumableRow()">+ افزودن قطعه</button></fieldset></div>
            <div class="form-row">
                <?php
                require_once '../includes/file_uploader.php';
                echo render_file_uploader([
                    'id' => 'deviceImages',
                    'name' => 'images[]',
                    'accept' => 'image/*',
                    'max_size' => 2,
                    'label' => 'تصاویر دستگاه',
                    'description' => 'فایل‌های عکس - حداکثر 2 مگابایت',
                    'show_existing' => false
                ]);
                ?>
            </div>
            <div class="form-row"><label for="description">توضیحات:</label><textarea name="description" id="description"></textarea></div>
            <button type="submit" id="add-device-btn">افزودن دستگاه</button>
        </form>
		</div>
    </div>
    <?php endif; ?>

    <div class="device-list">
        <h3>لیست دستگاه‌ها</h3>
        <div class="device-search-container"><input type="text" id="deviceSearch" placeholder="جستجو در لیست..." class="device-search-input"></div>
        <div class="table-scroll-container">
            <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width:100%;">
                <thead><tr><th>نام دستگاه</th><th>شماره سریال</th><th>محل استفاده</th><th>وضعیت</th><th>آخرین ویرایش</th><th>عملیات</th></tr></thead>
                <tbody>
                    <?php foreach ($devices as $device): ?>
                        <tr data-name="<?= htmlspecialchars($device['name']) ?>" data-serial="<?= htmlspecialchars($device['serial_number']) ?>" data-status="<?= htmlspecialchars($device['status']) ?>" data-vendor="<?= htmlspecialchars($device['vendor_name']) ?>" data-parts="<?php $stmtParts = $pdo->prepare('SELECT part_name FROM device_parts WHERE device_id = ?'); $stmtParts->execute([$device['id']]); echo htmlspecialchars(implode(', ', $stmtParts->fetchAll(PDO::FETCH_COLUMN))); ?>">
                            <td data-label="نام دستگاه"><?= htmlspecialchars($device['name']) ?></td>
                            <td data-label="شماره سریال"><?= htmlspecialchars($device['serial_number']) ?: '-' ?></td>
                            <td data-label="محل استفاده"><?= htmlspecialchars($device['location_name'] ?? '-') ?></td>
                            <td data-label="وضعیت"><?= htmlspecialchars($device['status']) ?></td>
                            <td data-label="آخرین ویرایش"><?php $date = $device['updated_at'] ?: $device['created_at']; echo to_shamsi($date, 'Y/m/d H:i'); ?></td>
                            <td data-label="عملیات">
                                <button onclick="showDeviceDetails(<?= $device['id'] ?>, '<?= htmlspecialchars(addslashes($device['images'] ?? '[]')) ?>')">جزئیات</button>
                                <?php if (has_permission('devices', 'view_history')): ?>
                                <button onclick="openDeviceHistoryModal(<?= $device['id'] ?>)">سوابق تعمیرات</button>
                                <?php endif; ?>
                                <?php if (has_permission('devices', 'edit')): ?>
                                <a href="device_edit.php?id=<?= $device['id'] ?>"><button>ویرایش</button></a>
                                <?php endif; ?>
                                <?php if (has_permission('devices', 'delete')): ?>
                                <button onclick="if(confirm('آیا از حذف دستگاه <?= htmlspecialchars(addslashes($device['name'])) ?> مطمئن هستید؟')) { deleteDevice(<?= $device['id'] ?>); }">حذف</button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div id="deviceDetailsModal">
    <div id="deviceDetailsModalBody">
        <button onclick="closeDeviceModal()" class="close-btn" aria-label="بستن مودال">&times;</button>
        <!-- Modal content is added here by JS -->
    </div>
</div>
<!-- Device repair history modal -->
<div id="device-history-modal">
    <div class="modal-content" style="background:#fff; margin:5% auto; border-radius:8px; max-width:750px; padding:1.5rem; position:relative;">
        <span class="close-btn" onclick="closeDeviceHistoryModal()" style="position:absolute;left:15px;top:10px;font-size:var(--fs-3xl);cursor:pointer;">&times;</span>
        <h4 style="margin-top:0;">سوابق تعمیرات دستگاه</h4>
        <div id="device-history-cards-container" class="history-cards-container"></div>
    </div>
</div>
<?php include '../includes/footer.php'; ?>
<script>


function setupAccordion() {
    const trigger = $('.accordion-trigger'), content = $('#addDeviceForm');
    trigger.off('click').on('click', function() { if (window.innerWidth <= 768) $(this).toggleClass('active'), content.slideToggle(300); });
    $(window).off('resize.accordion').on('resize.accordion', function() { if (window.innerWidth > 768) content.show(), trigger.removeClass('active'); else if (!trigger.hasClass('active')) content.hide(); }).trigger('resize');
}

function addConsumableRow() {
    const container = document.getElementById('consumablesContainer');
    const div = document.createElement('div');
    div.className = 'consumable-row';
    div.style.marginBottom = '10px';
    div.innerHTML = `<input type="text" name="consumable_name[]" placeholder="نام قطعه" required><input type="number" name="consumable_quantity[]" placeholder="تعداد" min="1" value="1" required><input type="text" name="consumable_unit[]" placeholder="واحد شمارش" value="عدد" required><input type="text" name="consumable_description[]" placeholder="توضیحات قطعه (اختیاری)"><button type="button" onclick="removeConsumableRow(this)">−</button>`;
    container.appendChild(div);
}

function removeConsumableRow(button) { button.parentElement.remove(); }

function showDeviceDetails(id, imagesJson) {
    fetch(`device_details.php?id=${id}&format=json`)
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                let d = data.device;
                let imagesHtml = '';
                try {
                    const images = JSON.parse(imagesJson.replace(/\\'/g, "'"));
                    if (Array.isArray(images) && images.length > 0) {
                        imagesHtml += '<h5>تصاویر دستگاه:</h5><div class="modal-gallery">';
                        images.forEach(imgName => {
                            imagesHtml += `<a href="../uploads/devices/${imgName}" target="_blank"><img src="../uploads/devices/${imgName}" alt="تصویر دستگاه"></a>`;
                        });
                        imagesHtml += '</div>';
                    }
                } catch (e) { console.error("Error parsing images JSON:", e); }

                let htmlContent = `<h4>جزئیات دستگاه</h4>
                <p><strong>نام:</strong> ${d.name}</p>
                <p><strong>شماره سریال:</strong> ${d.serial_number || '-'}</p>
                <p><strong>محل استفاده:</strong> ${d.location_name || '-'}</p>
                <p><strong>توضیحات:</strong> ${d.description || '-'}</p>
                <p><strong>وضعیت:</strong> ${d.status || '-'}</p>
                <p><strong>تاریخ خرید:</strong> ${d.purchase_date || '-'}</p>
                <p><strong>نام فروشنده:</strong> ${d.vendor_name || '-'}</p>
                <p><strong>شماره تماس فروشنده:</strong> ${d.vendor_phone || '-'}</p>
                ${imagesHtml}`;
                
                if(d.parts && d.parts.length > 0) {
                    htmlContent += `<h5>قطعات مصرفی:</h5><ul>`;
                    d.parts.forEach(part => { htmlContent += `<li><strong>${part.part_name}</strong> - تعداد: ${part.quantity} ${part.unit || 'عدد'} ${part.description ? '- توضیحات: ' + part.description : ''}</li>`; });
                    htmlContent += `</ul>`;
                } else { htmlContent += `<p><em>قطعات مصرفی ثبت نشده است.</em></p>`; }
                
                const modalBody = document.getElementById('deviceDetailsModalBody');
                // Clear previous content but keep the close button
                const closeButton = modalBody.querySelector('.close-btn');
                modalBody.innerHTML = '';
                modalBody.appendChild(closeButton);
                $(modalBody).append(htmlContent); // Use jQuery append to add the new content
                
                // جلوگیری از اسکرول صفحه پشت
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                document.body.style.top = `-${scrollTop}px`;
                document.body.classList.add('modal-open');

                document.getElementById('deviceDetailsModal').style.display = 'flex';
            } else { alert('خطا در دریافت اطلاعات دستگاه.'); }
        }).catch(() => alert('خطا در ارتباط با سرور'));
}

function closeDeviceModal() {
    const modal = document.getElementById('deviceDetailsModal');
    modal.style.display = 'none';

    // بازگرداندن اسکرول صفحه
    document.body.classList.remove('modal-open');
    const scrollTop = parseInt(document.body.style.top || '0') * -1;
    document.body.style.top = '';
    window.scrollTo(0, scrollTop);

    // Optional: Clear content on close
    const modalBody = document.getElementById('deviceDetailsModalBody');
    const closeButton = modalBody.querySelector('.close-btn');
    modalBody.innerHTML = '';
    modalBody.appendChild(closeButton);
}

document.getElementById('deviceDetailsModal').addEventListener('click', function(e) {
    if (e.target.id === 'deviceDetailsModal') {
        closeDeviceModal();
    }
});


function deleteDevice(id) {
    if (confirm('آیا از حذف این دستگاه اطمینان دارید؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'device_delete.php';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'id';
        input.value = id;
        
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}

document.getElementById('deviceSearch').addEventListener('input', function () {
    const query = this.value.toLowerCase();
    document.querySelectorAll('.device-list table tbody tr').forEach(row => {
        row.style.display = row.textContent.toLowerCase().includes(query) ? '' : 'none';
    });
});

document.getElementById('addDeviceForm').addEventListener('submit', function(event) {
    const form = event.target;
    const btn = document.getElementById('add-device-btn');

    // بررسی validation
    if (!form.checkValidity()) {
        event.preventDefault();
        showToast('لطفاً تمام فیلدهای اجباری را پر کنید.', 'warning');
        return;
    }

    btn.disabled = true;
    btn.textContent = 'در حال افزودن...';

    // فرم به صورت معمولی ارسال می‌شود
    // فایل‌ها از طریق کامپوننت file uploader مدیریت می‌شوند
});

$(document).ready(function() {
    setupAccordion();
});
// NEW JS FOR DEVICE HISTORY MODAL
const deviceHistoryModal = document.getElementById('device-history-modal');
const deviceHistoryContainer = document.getElementById('device-history-cards-container');
function openDeviceHistoryModal(deviceId) {
    // جلوگیری از اسکرول صفحه پشت
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    document.body.style.top = `-${scrollTop}px`;
    document.body.classList.add('modal-open');

    deviceHistoryModal.style.display = 'flex';
    fetchAndRenderDeviceHistory(deviceId);
}
function closeDeviceHistoryModal() {
    deviceHistoryModal.style.display = 'none';

    // بازگرداندن اسکرول صفحه
    document.body.classList.remove('modal-open');
    const scrollTop = parseInt(document.body.style.top || '0') * -1;
    document.body.style.top = '';
    window.scrollTo(0, scrollTop);

    deviceHistoryContainer.innerHTML = '';
}
deviceHistoryModal.addEventListener('click', function(e) {
    if (e.target.id === 'device-history-modal') {
        closeDeviceHistoryModal();
    }
});
function fetchAndRenderDeviceHistory(deviceId) {
    deviceHistoryContainer.innerHTML = '<p style="text-align:center;">در حال بارگذاری...</p>';
    fetch(`devices.php?action=get_device_history&device_id=${deviceId}`)
        .then(res => res.json())
        .then(data => {
            if (!data.success) {
                deviceHistoryContainer.innerHTML = '<p class="no-tasks">خطا در بارگذاری اطلاعات.</p>';
                return;
            }
            if (data.history.length === 0) {
                deviceHistoryContainer.innerHTML = '<p class="no-tasks">سابقه‌ای یافت نشد.</p>';
                return;
            }
            let cardsHtml = '';
            data.history.forEach(task => {
                let statusClass = '';
                switch (task.status) {
                    case 'انجام شده': statusClass = 'status-انجام-شده'; break;
                    case 'منتظر تایید': statusClass = 'status-منتظر-تایید'; break;
                    case 'تایید شده': statusClass = 'status-تایید-شده'; break;
                    case 'بسته شده': statusClass = 'status-بسته-شده'; break;
                    case 'انجام و تایید شده': statusClass = 'status-done-confirmed'; break;
                    case 'رد شده': statusClass = 'status-rejected'; break;
                }
                const notes = task.completion_notes ? task.completion_notes.replace(/\n/g, '<br>') : 'ثبت نشده';
                const labor = task.labor_users || 'ثبت نشده';
                const parts = task.parts_used ? task.parts_used.replace(/,/g, '<br>') : 'ثبت نشده';
                const delayReason = task.delay_reason ? `<p><strong>دلیل تاخیر:</strong><br>${task.delay_reason.replace(/\n/g, '<br>')}</p>` : '';
                const verifier = task.verifier_name || 'ثبت نشده';
                const startDate = task.actual_start_datetime_shamsi || 'ثبت نشده';
                const endDate = task.actual_end_datetime_shamsi || 'ثبت نشده';

                // بررسی وجود اطلاعات برون‌سپاری
                const hasOutsourcing = task.exit_date || task.back_date;
                let outsourcingSection = '';

                if (hasOutsourcing) {
                    outsourcingSection = '<hr class="history-card-divider"><div class="outsourcing-section">';
                    outsourcingSection += '<h5 style="color: #007bff; margin-bottom: 10px;"><i class="fas fa-external-link-alt"></i> اطلاعات برون‌سپاری</h5>';

                    if (task.exit_date) {
                        outsourcingSection += `<p><strong>تاریخ خروج:</strong> ${task.exit_date_shamsi || task.exit_date}</p>`;
                        if (task.exit_where) {
                            outsourcingSection += `<p><strong>مقصد:</strong> ${task.exit_where}</p>`;
                        }
                        if (task.exit_desc) {
                            outsourcingSection += `<p><strong>توضیحات خروج:</strong><br>${task.exit_desc.replace(/\n/g, '<br>')}</p>`;
                        }
                    }

                    if (task.back_date) {
                        outsourcingSection += `<p><strong>تاریخ بازگشت:</strong> ${task.back_date_shamsi || task.back_date}</p>`;
                        if (task.back_desc) {
                            outsourcingSection += `<p><strong>توضیحات بازگشت:</strong><br>${task.back_desc.replace(/\n/g, '<br>')}</p>`;
                        }
                        if (task.back_pay) {
                            // تبدیل اعداد انگلیسی به فارسی
                            const persianAmount = task.back_pay.toString().replace(/[0-9]/g, function(w) {
                                return ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'][w];
                            });
                            outsourcingSection += `<p><strong>مبلغ فاکتور:</strong> ${persianAmount} تومان</p>`;
                        }
                    } else if (task.exit_date) {
                        outsourcingSection += '<p><strong>وضعیت:</strong> <span style="color: #ffc107;">هنوز بازگشت داده نشده است</span></p>';
                    }

                    outsourcingSection += '</div>';
                }
                cardsHtml += `
                    <div class="history-card">
                        <div class="history-card-main">
                            <div class="history-card-summary">
                                <div class="history-card-title-line">
                                    <strong>${task.title}</strong>
                                    <div class="status-container">
                                        <span class="status-badge ${statusClass}">${task.status}</span>
                                        ${hasOutsourcing ? '<i class="fas fa-external-link-alt outsourcing-icon" title="شامل برون‌سپاری"></i>' : ''}
                                    </div>
                                </div>
                                <p><small>${startDate} - ${endDate}</small></p>
                            </div>
                            <button class="history-card-toggle"><i class="fas fa-chevron-down"></i></button>
                        </div>
                        <div class="history-card-collapsible-content">
                            <hr class="history-card-divider">
                            <div class="history-card-details">
                                <p><strong>شرح اقدامات:</strong><br>${notes}</p>
                                ${delayReason}
                                <p><strong>نفرات:</strong> ${labor}</p>
                                <p><strong>قطعات مصرفی:</strong><br>${parts}</p>
                                <p><strong>تحویل به:</strong> ${verifier}</p>
                            </div>
                            ${outsourcingSection}
                        </div>
                    </div>
                `;
            });
            deviceHistoryContainer.innerHTML = cardsHtml;
        })
        .catch(() => {
            deviceHistoryContainer.innerHTML = '<p class="no-tasks">خطای ارتباط با سرور.</p>';
        });
}
deviceHistoryContainer.addEventListener('click', function(e) {
    const header = e.target.closest('.history-card-main');
    if (header) {
        header.parentElement.classList.toggle('active');
    }
});
</script>
</body>
</html>
