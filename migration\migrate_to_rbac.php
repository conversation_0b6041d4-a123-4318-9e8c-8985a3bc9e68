<?php
/**
 * اسکریپت مایگریشن سیستم RBAC
 * این اسکریپت داده‌های موجود را از سیستم قدیمی به سیستم جدید RBAC منتقل می‌کند
 */

// اگر از setup_database.php فراخوانی شده، از $pdo موجود استفاده کن
if (!isset($pdo)) {
    require_once __DIR__ . '/../db_connection.php';
    $pdo = db_connect();
}

// تنظیم منطقه زمانی
date_default_timezone_set('Asia/Tehran');

echo "<h2>شروع مایگریشن سیستم RBAC</h2>\n";

try {
    // شروع تراکنش
    $pdo->beginTransaction();
    
    echo "<h3>مرحله 1: بررسی وجود جداول جدید</h3>\n";
    
    // بررسی وجود جداول RBAC
    $tables = ['roles', 'system_pages', 'permissions', 'role_permissions'];
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if (!$stmt->fetch()) {
            throw new Exception("جدول $table وجود ندارد. لطفاً ابتدا اسکریپت database.sql را اجرا کنید.");
        }
        echo "✓ جدول $table موجود است<br>\n";
    }
    
    echo "<h3>مرحله 2: بررسی ستون‌های جدید در جدول users</h3>\n";
    
    // بررسی وجود ستون‌های جدید در جدول users
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('role_id', $columns)) {
        echo "افزودن ستون role_id به جدول users...<br>\n";
        $pdo->exec("ALTER TABLE users ADD COLUMN role_id INT DEFAULT NULL");
        $pdo->exec("ALTER TABLE users ADD FOREIGN KEY (role_id) REFERENCES roles(id)");
    } else {
        echo "✓ ستون role_id موجود است<br>\n";
    }
    
    if (!in_array('is_active', $columns)) {
        echo "افزودن ستون is_active به جدول users...<br>\n";
        $pdo->exec("ALTER TABLE users ADD COLUMN is_active TINYINT(1) DEFAULT 1");
    } else {
        echo "✓ ستون is_active موجود است<br>\n";
    }
    
    echo "<h3>مرحله 3: انتقال کاربران به سیستم جدید</h3>\n";
    
    // دریافت تمام کاربران
    $users = $pdo->query("SELECT * FROM users")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        // اگر role_id خالی است، بر اساس role قدیمی تنظیم کن
        if (empty($user['role_id'])) {
            $role_name = strtolower($user['role'] ?? 'user');
            
            // پیدا کردن role_id مناسب
            $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = ?");
            $stmt->execute([$role_name]);
            $role = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($role) {
                $role_id = $role['id'];
            } else {
                // اگر نقش پیدا نشد، نقش user را تنظیم کن
                $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'user'");
                $stmt->execute();
                $role_id = $stmt->fetchColumn();
            }
            
            // به‌روزرسانی کاربر
            $stmt = $pdo->prepare("UPDATE users SET role_id = ?, is_active = 1 WHERE id = ?");
            $stmt->execute([$role_id, $user['id']]);
            
            echo "✓ کاربر {$user['username']} به نقش $role_name منتقل شد<br>\n";
        } else {
            echo "✓ کاربر {$user['username']} قبلاً منتقل شده است<br>\n";
        }
    }
    
    echo "<h3>مرحله 4: بررسی و تکمیل داده‌های اولیه</h3>\n";
    
    // بررسی وجود نقش‌های پایه
    $base_roles = [
        ['name' => 'admin', 'display_name' => 'مدیر سیستم', 'description' => 'دسترسی کامل به تمام بخش‌های سیستم'],
        ['name' => 'user', 'display_name' => 'کاربر عادی', 'description' => 'دسترسی محدود به عملیات پایه'],
        ['name' => 'outsource', 'display_name' => 'پیمانکار', 'description' => 'دسترسی محدود برای پیمانکاران خارجی']
    ];
    
    foreach ($base_roles as $role) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM roles WHERE name = ?");
        $stmt->execute([$role['name']]);
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("INSERT INTO roles (name, display_name, description, is_active) VALUES (?, ?, ?, 1)");
            $stmt->execute([$role['name'], $role['display_name'], $role['description']]);
            echo "✓ نقش {$role['display_name']} اضافه شد<br>\n";
        } else {
            echo "✓ نقش {$role['display_name']} موجود است<br>\n";
        }
    }
    
    // بررسی وجود صفحات سیستم
    $system_pages = [
        ['name' => 'dashboard', 'display_name' => 'داشبورد', 'file_path' => 'dashboard.php', 'icon' => 'fas fa-tachometer-alt'],
        ['name' => 'devices', 'display_name' => 'مدیریت دستگاه‌ها', 'file_path' => 'devices.php', 'icon' => 'fas fa-desktop'],
        ['name' => 'activities', 'display_name' => 'مدیریت فعالیت‌ها', 'file_path' => 'activities.php', 'icon' => 'fas fa-tasks'],
        ['name' => 'work_order', 'display_name' => 'دستور کار', 'file_path' => 'work_order.php', 'icon' => 'fas fa-clipboard-list'],
        ['name' => 'reports_list', 'display_name' => 'گزارش‌های خرابی', 'file_path' => 'reports_list.php', 'icon' => 'fas fa-exclamation-triangle'],
        ['name' => 'my_tasks', 'display_name' => 'وظایف من', 'file_path' => 'my_tasks.php', 'icon' => 'fas fa-user-check'],
        ['name' => 'user_management', 'display_name' => 'مدیریت کاربران', 'file_path' => 'user_management.php', 'icon' => 'fas fa-users-cog']
    ];
    
    foreach ($system_pages as $page) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_pages WHERE name = ?");
        $stmt->execute([$page['name']]);
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("INSERT INTO system_pages (name, display_name, file_path, icon, is_active) VALUES (?, ?, ?, ?, 1)");
            $stmt->execute([$page['name'], $page['display_name'], $page['file_path'], $page['icon']]);
            echo "✓ صفحه {$page['display_name']} اضافه شد<br>\n";
        } else {
            echo "✓ صفحه {$page['display_name']} موجود است<br>\n";
        }
    }
    
    echo "<h3>مرحله 5: تنظیم مجوزهای پیش‌فرض</h3>\n";
    
    // دریافت ID نقش admin
    $admin_role = $pdo->query("SELECT id FROM roles WHERE name = 'admin'")->fetch(PDO::FETCH_ASSOC);
    $user_role = $pdo->query("SELECT id FROM roles WHERE name = 'user'")->fetch(PDO::FETCH_ASSOC);
    
    if ($admin_role && $user_role) {
        // دریافت تمام مجوزها
        $permissions = $pdo->query("SELECT id FROM permissions")->fetchAll(PDO::FETCH_COLUMN);
        
        // اعطای تمام مجوزها به admin
        foreach ($permissions as $permission_id) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
            $stmt->execute([$admin_role['id'], $permission_id]);
        }
        echo "✓ تمام مجوزها به نقش مدیر اعطا شد<br>\n";
        
        // اعطای مجوزهای محدود به user
        $user_permissions = $pdo->query("
            SELECT p.id 
            FROM permissions p 
            JOIN system_pages sp ON p.page_id = sp.id 
            WHERE sp.name IN ('dashboard', 'devices', 'activities', 'work_order', 'reports_list', 'my_tasks') 
            AND p.name IN ('view', 'create', 'edit')
        ")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($user_permissions as $permission_id) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
            $stmt->execute([$user_role['id'], $permission_id]);
        }
        echo "✓ مجوزهای محدود به نقش کاربر عادی اعطا شد<br>\n";
    }
    
    echo "<h3>مرحله 6: بررسی نهایی</h3>\n";
    
    // بررسی تعداد کاربران منتقل شده
    $migrated_users = $pdo->query("SELECT COUNT(*) FROM users WHERE role_id IS NOT NULL")->fetchColumn();
    $total_users = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    
    echo "✓ $migrated_users از $total_users کاربر با موفقیت منتقل شدند<br>\n";
    
    // تایید تراکنش
    $pdo->commit();
    
    echo "<h2 style='color: green;'>✅ مایگریشن با موفقیت تکمیل شد!</h2>\n";
    echo "<p>سیستم RBAC آماده استفاده است. می‌توانید با حساب مدیر وارد شوید و تنظیمات را بررسی کنید.</p>\n";
    
} catch (Exception $e) {
    // بازگشت تراکنش در صورت خطا
    $pdo->rollBack();
    echo "<h2 style='color: red;'>❌ خطا در مایگریشن: " . $e->getMessage() . "</h2>\n";
    echo "<p>لطفاً مشکل را برطرف کرده و دوباره تلاش کنید.</p>\n";
}
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مایگریشن سیستم RBAC</title>
    <style>
        body {
            font-family: 'Tahoma', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        h2, h3 {
            color: #333;
        }
        
        .success {
            color: green;
        }
        
        .error {
            color: red;
        }
        
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="info">
        <strong>نکته:</strong> این اسکریپت فقط یک بار باید اجرا شود. پس از اجرای موفق، آن را حذف کنید یا از دسترسی عمومی خارج کنید.
    </div>
</body>
</html>
