<?php
require_once 'config/db.php';

try {
    // درج قالب‌های گزارش پیش‌فرض
    $templates = [
        [
            'name' => 'گزارش وضعیت دستگاه‌ها',
            'description' => 'نمایش وضعیت کلی دستگاه‌ها',
            'config' => json_encode([
                'tables' => ['devices'],
                'fields' => ['devices.name', 'devices.status', 'devices.location'],
                'display_type' => 'table',
                'chart_type' => null
            ])
        ],
        [
            'name' => 'گزارش دستورات کار فعال',
            'description' => 'نمایش دستورات کار در حال انجام',
            'config' => json_encode([
                'tables' => ['work_orders', 'devices', 'users'],
                'fields' => ['work_orders.workorder_id', 'devices.name', 'work_orders.title', 'work_orders.status', 'work_orders.priority', 'users.name'],
                'joins' => [
                    ['table1' => 'work_orders', 'field1' => 'device_id', 'table2' => 'devices', 'field2' => 'id'],
                    ['table1' => 'work_orders', 'field1' => 'requester_id', 'table2' => 'users', 'field2' => 'id']
                ],
                'display_type' => 'table',
                'chart_type' => null
            ])
        ],
        [
            'name' => 'آمار خرابی‌ها بر اساس دستگاه',
            'description' => 'نمودار تعداد خرابی‌ها برای هر دستگاه',
            'config' => json_encode([
                'tables' => ['breakdown_reports', 'devices'],
                'fields' => ['devices.name', 'COUNT(breakdown_reports.id) as تعداد_خرابی'],
                'joins' => [
                    ['table1' => 'breakdown_reports', 'field1' => 'device_id', 'table2' => 'devices', 'field2' => 'id']
                ],
                'group_by' => ['devices.name'],
                'display_type' => 'chart',
                'chart_type' => 'bar'
            ])
        ],
        [
            'name' => 'گزارش عملکرد کاربران',
            'description' => 'نمایش تعداد دستورات کار تکمیل شده توسط هر کاربر',
            'config' => json_encode([
                'tables' => ['work_orders', 'users'],
                'fields' => ['users.name', 'COUNT(work_orders.id) as تعداد_دستورکار'],
                'joins' => [
                    ['table1' => 'work_orders', 'field1' => 'requester_id', 'table2' => 'users', 'field2' => 'id']
                ],
                'group_by' => ['users.name'],
                'display_type' => 'chart',
                'chart_type' => 'pie'
            ])
        ],
        [
            'name' => 'گزارش فعالیت‌های سررسید',
            'description' => 'نمایش فعالیت‌هایی که نزدیک سررسید هستند',
            'config' => json_encode([
                'tables' => ['activities', 'devices'],
                'fields' => ['devices.name', 'activities.activity_name', 'activities.next_service_date'],
                'joins' => [
                    ['table1' => 'activities', 'field1' => 'device_id', 'table2' => 'devices', 'field2' => 'id']
                ],
                'filters' => [
                    ['field' => 'activities.next_service_date', 'operator' => '<=', 'value' => 'DATE_ADD(CURDATE(), INTERVAL 30 DAY)']
                ],
                'display_type' => 'table',
                'chart_type' => null
            ])
        ]
    ];

    foreach ($templates as $template) {
        $stmt = $pdo->prepare("
            INSERT INTO report_templates (name, description, config, is_default, created_by) 
            VALUES (?, ?, ?, 1, 1)
            ON DUPLICATE KEY UPDATE 
            description = VALUES(description),
            config = VALUES(config)
        ");
        $stmt->execute([
            $template['name'],
            $template['description'],
            $template['config']
        ]);
    }

    echo "قالب‌های پیش‌فرض با موفقیت اضافه شدند.\n";

} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
