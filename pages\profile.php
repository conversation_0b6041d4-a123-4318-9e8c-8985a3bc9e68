<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// بررسی دسترسی به صفحه پروفایل
require_page_access('profile', 'view');

$pdo = db_connect();
$current_user_id = current_user_id();

// پردازش فرم‌ها
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_profile':
                try {
                    $name = trim($_POST['name']);
                    $email = trim($_POST['email']);
                    $phone = trim($_POST['phone']);
                    
                    if (empty($name)) {
                        throw new Exception('نام نمی‌تواند خالی باشد.');
                    }
                    
                    // بررسی یکتا بودن ایمیل
                    if (!empty($email)) {
                        $email_check = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                        $email_check->execute([$email, $current_user_id]);
                        if ($email_check->fetchColumn()) {
                            throw new Exception('این ایمیل قبلاً استفاده شده است.');
                        }
                    }
                    
                    $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ?, phone = ? WHERE id = ?");
                    $stmt->execute([$name, $email, $phone, $current_user_id]);
                    
                    $_SESSION['user']['name'] = $name;
                    $_SESSION['toast_message'] = 'اطلاعات پروفایل با موفقیت به‌روزرسانی شد.';
                    $_SESSION['toast_type'] = 'success';
                    
                } catch (Exception $e) {
                    $_SESSION['toast_message'] = 'خطا: ' . $e->getMessage();
                    $_SESSION['toast_type'] = 'danger';
                }
                break;
                
            case 'upload_avatar':
                try {
                    if (!isset($_FILES['avatar']) || $_FILES['avatar']['error'] !== UPLOAD_ERR_OK) {
                        throw new Exception('خطا در آپلود فایل.');
                    }
                    
                    $file = $_FILES['avatar'];
                    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                    $maxSize = 2 * 1024 * 1024; // 2MB
                    
                    // بررسی نوع فایل
                    if (!in_array($file['type'], $allowedTypes)) {
                        throw new Exception('فقط فایل‌های تصویری (JPEG, PNG, GIF, WebP) مجاز هستند.');
                    }
                    
                    // بررسی اندازه فایل
                    if ($file['size'] > $maxSize) {
                        throw new Exception('اندازه فایل نباید بیشتر از 2 مگابایت باشد.');
                    }
                    
                    // ایجاد نام فایل یکتا
                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $filename = 'avatar_' . $current_user_id . '_' . time() . '.' . $extension;
                    $uploadPath = '../uploads/avatars/' . $filename;
                    $relativePath = 'uploads/avatars/' . $filename;
                    
                    // ایجاد پوشه در صورت عدم وجود
                    $uploadDir = dirname($uploadPath);
                    if (!is_dir($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }
                    
                    // آپلود فایل
                    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
                        throw new Exception('خطا در ذخیره فایل.');
                    }
                    
                    // حذف آواتار قبلی در صورت وجود
                    $old_avatar_stmt = $pdo->prepare("SELECT avatar FROM users WHERE id = ?");
                    $old_avatar_stmt->execute([$current_user_id]);
                    $old_avatar = $old_avatar_stmt->fetchColumn();
                    
                    if ($old_avatar && file_exists('../' . $old_avatar)) {
                        unlink('../' . $old_avatar);
                    }
                    
                    // به‌روزرسانی پایگاه داده
                    $stmt = $pdo->prepare("UPDATE users SET avatar = ? WHERE id = ?");
                    $stmt->execute([$relativePath, $current_user_id]);
                    
                    $_SESSION['toast_message'] = 'تصویر پروفایل با موفقیت به‌روزرسانی شد.';
                    $_SESSION['toast_type'] = 'success';
                    
                } catch (Exception $e) {
                    $_SESSION['toast_message'] = 'خطا: ' . $e->getMessage();
                    $_SESSION['toast_type'] = 'danger';
                }
                break;
                
            case 'change_password':
                try {
                    $current_password = $_POST['current_password'];
                    $new_password = $_POST['new_password'];
                    $confirm_password = $_POST['confirm_password'];
                    
                    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                        throw new Exception('همه فیلدهای رمز عبور باید پر شوند.');
                    }
                    
                    if ($new_password !== $confirm_password) {
                        throw new Exception('رمز عبور جدید و تکرار آن مطابقت ندارند.');
                    }
                    
                    if (strlen($new_password) < 6) {
                        throw new Exception('رمز عبور باید حداقل 6 کاراکتر باشد.');
                    }
                    
                    // دریافت رمز عبور فعلی کاربر
                    $user_stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                    $user_stmt->execute([$current_user_id]);
                    $stored_password = $user_stmt->fetchColumn();
                    
                    // بررسی رمز عبور فعلی
                    $password_valid = false;
                    if (password_verify($current_password, $stored_password)) {
                        $password_valid = true;
                    } elseif (md5($current_password) === $stored_password) {
                        $password_valid = true;
                    } elseif ($current_password === $stored_password) {
                        $password_valid = true;
                    }
                    
                    if (!$password_valid) {
                        throw new Exception('رمز عبور فعلی اشتباه است.');
                    }
                    
                    // هش کردن رمز عبور جدید
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    
                    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $stmt->execute([$hashed_password, $current_user_id]);
                    
                    $_SESSION['toast_message'] = 'رمز عبور با موفقیت تغییر کرد.';
                    $_SESSION['toast_type'] = 'success';
                    
                } catch (Exception $e) {
                    $_SESSION['toast_message'] = 'خطا: ' . $e->getMessage();
                    $_SESSION['toast_type'] = 'danger';
                }
                break;
        }
        
        header('Location: profile.php');
        exit;
    }
}

// دریافت اطلاعات کاربر
$user_stmt = $pdo->prepare("
    SELECT u.*, r.display_name as role_display_name, r.name as role_name
    FROM users u
    LEFT JOIN roles r ON u.role_id = r.id
    WHERE u.id = ?
");
$user_stmt->execute([$current_user_id]);
$user = $user_stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    $_SESSION['toast_message'] = 'کاربر یافت نشد.';
    $_SESSION['toast_type'] = 'danger';
    header('Location: dashboard.php');
    exit;
}

// دریافت آمار کاربر
$stats = [];

// تعداد دستورکارهای تخصیص یافته
$wo_count = $pdo->prepare("
    SELECT COUNT(*) 
    FROM work_order_assignees woa
    JOIN work_orders wo ON woa.work_order_id = wo.id
    WHERE woa.user_id = ?
");
$wo_count->execute([$current_user_id]);
$stats['work_orders'] = $wo_count->fetchColumn();

// تعداد گزارش‌های ثبت شده
$reports_count = $pdo->prepare("SELECT COUNT(*) FROM breakdown_reports WHERE reported_by_id = ?");
$reports_count->execute([$current_user_id]);
$stats['reports'] = $reports_count->fetchColumn();

// تعداد فعالیت‌های ثبت شده
$activities_count = $pdo->prepare("SELECT COUNT(*) FROM logs WHERE user_id = ?");
$activities_count->execute([$current_user_id]);
$stats['activities'] = $activities_count->fetchColumn();

// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}

include '../includes/header.php';
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>پروفایل کاربری</title>
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-light: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --border-radius: 0.5rem;
            --border-radius-lg: 0.75rem;
            --border-radius-xl: 1rem;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .profile-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 1.5rem;
            color: white;
            padding: 1.5rem 1rem;
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .page-title i {
            font-size: 1.5rem;
        }

        .page-subtitle {
            font-size: 0.95rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .profile-header {
            display: grid;
            grid-template-columns: auto 1fr auto;
            align-items: center;
            gap: 2rem;
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            color: var(--gray-800);
            padding: 1.5rem;
            border-radius: var(--border-radius-xl);
            margin-bottom: 2rem;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light), var(--success-color));
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-lg);
            position: relative;
            flex-shrink: 0;
            cursor: pointer;
            transition: all 0.3s ease;
            
        }

        .profile-avatar:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-xl);
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .profile-avatar .avatar-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 50%;
            color: white;
            font-size: 0.9rem;
        }

        .profile-avatar:hover .avatar-overlay {
            opacity: 1;
        }

        .profile-avatar .avatar-edit-icon {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 24px;
            height: 24px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.7rem;
            box-shadow: var(--shadow-md);
            border: 2px solid white;
        }

        .profile-avatar::after {
            content: '';
            position: absolute;
            inset: -4px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            z-index: -1;
            opacity: 0.3;
        }

        .profile-name {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .profile-info {
            display: flex;   
            justify-content: space-between;
        }
        
        .profile-name {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .profile-role {
            font-size: 1rem;
            color: var(--primary-color);
            font-weight: 600;
            background: var(--gray-100);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            display: inline-block;
            margin-bottom: 0.75rem;
            width: fit-content;
        }
        
        .profile-inline-stats {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
        }
        
        .inline-stat {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.1rem;
            color: var(--gray-600);
        }
        
        .inline-stat-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            color: white;
        }
        
        .inline-stat-icon.work-orders {
            background: var(--primary-color);
        }
        
        .inline-stat-icon.reports {
            background: var(--warning-color);
        }
        
        .inline-stat-icon.activities {
            background: var(--success-color);
        }
        
        .inline-stat-number {
            font-weight: 700;
            color: var(--gray-800);
            font-size: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            padding: 2rem;
            border-radius: var(--border-radius-xl);
            text-align: center;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.work-orders {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .stat-icon.reports {
            background: linear-gradient(135deg, var(--warning-color), #fbbf24);
        }

        .stat-icon.activities {
            background: linear-gradient(135deg, var(--success-color), #34d399);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }

        .stat-label {
            font-size: 1rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        .main-content {
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
        }

        .tabs-container {
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .tabs {
            display: flex;
            overflow-x: auto;
        }

        .tab {
            flex: 1;
            min-width: 200px;
            padding: 1.25rem 1.5rem;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-600);
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab:hover {
            background: var(--gray-100);
            color: var(--primary-color);
        }

        .tab.active {
            color: var(--primary-color);
            background: white;
        }

        .tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .tab-content {
            display: none;
            padding: 2.5rem;
        }

        .tab-content.active {
            display: block;
        }

        .content-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--gray-100);
        }

        .content-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .info-item {
            background: var(--gray-50);
            padding: 1.5rem;
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
            position: relative;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .info-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--border-radius) 0 0 var(--border-radius);
        }

        .info-label {
            font-weight: 600;
            color: var(--gray-600);
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 1.1rem;
            color: var(--gray-800);
            font-weight: 500;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--gray-50);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-group input:disabled {
            background: var(--gray-100);
            color: var(--gray-500);
            cursor: not-allowed;
        }


        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-badge.active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-badge.inactive {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .form-help {
            font-size: 0.8rem;
            color: var(--gray-500);
            margin-top: 0.5rem;
        }

        /* واکنش‌گرایی */
        @media (max-width: 1024px) {
            .profile-container {
                padding: 1.5rem 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }

            .page-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 768px) {
            .profile-container {
                padding: 1rem 0.5rem;
            }

            .tabs {
                flex-direction: column;
            }

            .tab {
                min-width: auto;
                padding: 1rem;
            }

            .tab-content {
                padding: 1.5rem;
            }

            .content-header {
                flex-direction: column;
                align-items: flex-start;
                text-align: center;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .page-title {
                font-size: 1.75rem;
            }

            .profile-name {
                font-size: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .profile-header {
                padding: 1.5rem;
            }

            .profile-avatar {
                width: 80px;
                height: 80px;
                font-size: 2.5rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>

<div class="profile-container">
    <!-- هدر پروفایل با آمار -->
    <div class="profile-header">
        <div class="profile-avatar" onclick="document.getElementById('avatarInput').click();">
            <?php if ($user['avatar'] && file_exists('../' . $user['avatar'])): ?>
                <img src="<?= '../' . htmlspecialchars($user['avatar']) ?>" alt="آواتار کاربر">
                <div class="avatar-overlay">
                   
                    تغییر تصویر
                </div>
            <?php else: ?>
                <i class="fas fa-user"></i>
                <div class="avatar-overlay">
                    افزودن تصویر
                </div>
            <?php endif; ?>
            <div class="avatar-edit-icon">
                <i class="fas fa-camera"></i>
            </div>
        </div>
        <div class="profile-info">
            <div class="profile-name"><?= htmlspecialchars($user['name']) ?></div>
            <div class="profile-inline-stats">
                <div class="inline-stat">
                    <div class="inline-stat-icon work-orders">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div>
                        <span class="inline-stat-number"><?= $stats['work_orders'] ?></span>
                        <span>دستورکار</span>
                    </div>
                </div>
                <div class="inline-stat">
                    <div class="inline-stat-icon reports">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <span class="inline-stat-number"><?= $stats['reports'] ?></span>
                        <span>گزارش</span>
                    </div>
                </div>
                <div class="inline-stat">
                    <div class="inline-stat-icon activities">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div>
                        <span class="inline-stat-number"><?= $stats['activities'] ?></span>
                        <span>فعالیت</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- محتوای اصلی -->
    <div class="main-content">
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab active" onclick="showTab('info')">
                    <i class="fas fa-info-circle"></i>
                    اطلاعات کاربری
                </button>
                <button class="tab" onclick="showTab('edit')">
                    <i class="fas fa-edit"></i>
                    ویرایش پروفایل
                </button>
                <button class="tab" onclick="showTab('password')">
                    <i class="fas fa-lock"></i>
                    تغییر رمز عبور
                </button>
            </div>
        </div>

        <!-- محتوای تب اطلاعات کاربری -->
        <div id="info-tab" class="tab-content active">
            <div class="content-header">
                <i class="fas fa-info-circle" style="color: var(--primary-color); font-size: 1.5rem;"></i>
                <h2 class="content-title">اطلاعات کاربری</h2>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">نام کاربری</div>
                    <div class="info-value"><?= htmlspecialchars($user['username']) ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">نام و نام خانوادگی</div>
                    <div class="info-value"><?= htmlspecialchars($user['name']) ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">ایمیل</div>
                    <div class="info-value"><?= htmlspecialchars($user['email'] ?: 'تعریف نشده') ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">شماره تماس</div>
                    <div class="info-value"><?= htmlspecialchars($user['phone'] ?: 'تعریف نشده') ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">نقش کاربری</div>
                    <div class="info-value"><?= htmlspecialchars($user['role_display_name']) ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">تاریخ عضویت</div>
                    <div class="info-value"><?= to_shamsi($user['created_at']) ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">وضعیت حساب</div>
                    <div class="info-value">
                        <?php if ($user['is_active']): ?>
                            <span class="status-badge active">
                                <i class="fas fa-check-circle"></i>
                                فعال
                            </span>
                        <?php else: ?>
                            <span class="status-badge inactive">
                                <i class="fas fa-times-circle"></i>
                                غیرفعال
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- محتوای تب ویرایش پروفایل -->
        <div id="edit-tab" class="tab-content">
            <div class="content-header">
                <i class="fas fa-edit" style="color: var(--primary-color); font-size: 1.5rem;"></i>
                <h2 class="content-title">ویرایش اطلاعات شخصی</h2>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="update_profile">

                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">نام و نام خانوادگی </label>
                        <input type="text" id="name" name="name" value="<?= htmlspecialchars($user['name']) ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="username">نام کاربری</label>
                        <input type="text" id="username" name="username" value="<?= htmlspecialchars($user['username']) ?>" disabled>
                        <div class="form-help">نام کاربری قابل تغییر نیست</div>
                    </div>

                    <div class="form-group">
                        <label for="email">ایمیل</label>
                        <input type="email" id="email" name="email" value="<?= htmlspecialchars($user['email']) ?>" placeholder="<EMAIL>">
                        <div class="form-help">ایمیل برای بازیابی رمز عبور استفاده می‌شود</div>
                    </div>

                    <div class="form-group">
                        <label for="phone">شماره تماس</label>
                        <input type="text" id="phone" name="phone" value="<?= htmlspecialchars($user['phone']) ?>" placeholder="09123456789">
                        <div class="form-help">شماره تماس برای ارتباط اضطراری</div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    ذخیره تغییرات
                </button>
            </form>
        </div>

        <!-- محتوای تب تغییر رمز عبور -->
        <div id="password-tab" class="tab-content">
            <div class="content-header">
                <i class="fas fa-lock" style="color: var(--primary-color); font-size: 1.5rem;"></i>
                <h2 class="content-title">تغییر رمز عبور</h2>
            </div>
            <form method="POST" id="passwordForm">
                <input type="hidden" name="action" value="change_password">

                <div class="form-grid">
                    <div class="form-group">
                        <label for="current_password">رمز عبور فعلی </label>
                        <input type="password" id="current_password" name="current_password" required>
                        <div class="form-help">رمز عبور فعلی خود را وارد کنید</div>
                    </div>

                    <div class="form-group">
                        <label for="new_password">رمز عبور جدید </label>
                        <input type="password" id="new_password" name="new_password" required minlength="6">
                        <div class="form-help">حداقل 6 کاراکتر - ترکیبی از حروف و اعداد توصیه می‌شود</div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">تکرار رمز عبور جدید </label>
                        <input type="password" id="confirm_password" name="confirm_password" required minlength="6">
                        <div class="form-help">رمز عبور جدید را مجدداً وارد کنید</div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-key"></i>
                    تغییر رمز عبور
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Hidden Avatar Upload Form -->
<form id="avatarForm" method="POST" enctype="multipart/form-data" style="display: none;">
    <input type="hidden" name="action" value="upload_avatar">
    <input type="file" id="avatarInput" name="avatar" accept="image/*" onchange="uploadAvatar()">
</form>

<!-- Toast Container -->
<div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

<script>
// Global Toast Message Functions
function showToast(message, type = 'info', duration = 4000) {
    const container = document.getElementById('toast-container');
    if (!container) return;
    
    const toast = document.createElement('div');
    toast.style.cssText = `
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 1rem 1.5rem;
        margin-bottom: 10px;
        border-right: 4px solid;
        min-width: 300px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        opacity: 0;
        position: relative;
    `;
    
    const title = type === 'success' ? 'موفقیت' : 
                  type === 'warning' ? 'هشدار' : 
                  type === 'danger' ? 'خطا' : 'اطلاعات';

    // Set border and text color based on type
    let borderColor, textColor;
    switch(type) {
        case 'success':
            borderColor = '#28a745';
            textColor = '#155724';
            break;
        case 'warning':
            borderColor = '#ffc107';
            textColor = '#856404';
            break;
        case 'danger':
            borderColor = '#dc3545';
            textColor = '#721c24';
            break;
        default:
            borderColor = '#17a2b8';
            textColor = '#0c5460';
    }
    
    toast.style.borderColor = borderColor;
    toast.style.color = textColor;
    
    toast.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
            <span style="font-weight: 600; font-size: 0.9rem;">${title}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 1.2rem; cursor: pointer; color: #666; padding: 0; line-height: 1;">&times;</button>
        </div>
        <div style="font-size: 0.9rem; line-height: 1.4;">${message}</div>
    `;
    
    container.appendChild(toast);
    
    // Show animation
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
        toast.style.opacity = '1';
    }, 100);
    
    // Auto remove after duration
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 300);
    }, duration);
}

function showTab(tabName) {
    // مخفی کردن همه تب‌ها
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => tab.classList.remove('active'));

    // حذف کلاس active از همه دکمه‌ها
    const buttons = document.querySelectorAll('.tab');
    buttons.forEach(btn => btn.classList.remove('active'));

    // نمایش تب انتخاب شده
    document.getElementById(tabName + '-tab').classList.add('active');

    // اضافه کردن کلاس active به دکمه انتخاب شده
    event.target.classList.add('active');
}

// Show toast on page load if message exists in session
document.addEventListener('DOMContentLoaded', function() {
    // جلوگیری از نمایش دوباره پیام در footer
    window.skipFooterToast = true;

    // Check if there's a toast message from PHP session
    const toastMessage = document.querySelector('meta[name="toast-message"]');
    const toastType = document.querySelector('meta[name="toast-type"]');

    if (toastMessage && toastMessage.content) {
        showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
    }
    
    // اعتبارسنجی فرم تغییر رمز عبور
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (!currentPassword.trim()) {
                e.preventDefault();
                showAlert('رمز عبور فعلی را وارد کنید.', 'warning');
                return false;
            }

            if (newPassword !== confirmPassword) {
                e.preventDefault();
                showAlert('رمز عبور جدید و تکرار آن مطابقت ندارند.', 'danger');
                return false;
            }

            if (newPassword.length < 6) {
                e.preventDefault();
                showAlert('رمز عبور باید حداقل 6 کاراکتر باشد.', 'warning');
                return false;
            }

            if (newPassword === currentPassword) {
                e.preventDefault();
                showAlert('رمز عبور جدید نمی‌تواند مشابه رمز عبور فعلی باشد.', 'warning');
                return false;
            }
        });
    }

    // اعتبارسنجی فرم ویرایش پروفایل
    const editForm = document.querySelector('form[action=""][method="POST"]:not(#passwordForm)');
    if (editForm) {
        editForm.addEventListener('submit', function(e) {
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;

            if (!name.trim()) {
                e.preventDefault();
                showAlert('نام و نام خانوادگی الزامی است.', 'warning');
                return false;
            }

            if (email && !isValidEmail(email)) {
                e.preventDefault();
                showAlert('فرمت ایمیل صحیح نیست.', 'warning');
                return false;
            }

            if (phone && !isValidPhone(phone)) {
                e.preventDefault();
                showAlert('فرمت شماره تماس صحیح نیست.', 'warning');
                return false;
            }
        });
    }
});

// توابع کمکی
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^09\d{9}$/;
    return phoneRegex.test(phone);
}

function showAlert(message, type = 'info') {
    // ایجاد المان alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        color: white;
        font-weight: 600;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    // تنظیم رنگ بر اساس نوع
    switch(type) {
        case 'success':
            alertDiv.style.background = 'linear-gradient(135deg, #10b981, #34d399)';
            break;
        case 'warning':
            alertDiv.style.background = 'linear-gradient(135deg, #f59e0b, #fbbf24)';
            break;
        case 'danger':
            alertDiv.style.background = 'linear-gradient(135deg, #ef4444, #f87171)';
            break;
        default:
            alertDiv.style.background = 'linear-gradient(135deg, #2563eb, #3b82f6)';
    }

    alertDiv.textContent = message;
    document.body.appendChild(alertDiv);

    // نمایش alert
    setTimeout(() => {
        alertDiv.style.transform = 'translateX(0)';
    }, 100);

    // حذف alert بعد از 5 ثانیه
    setTimeout(() => {
        alertDiv.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(alertDiv);
        }, 300);
    }, 5000);
}

// Avatar upload function
function uploadAvatar() {
    const fileInput = document.getElementById('avatarInput');
    const file = fileInput.files[0];
    
    if (!file) {
        return;
    }
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showAlert('فقط فایل‌های تصویری (JPEG, PNG, GIF, WebP) مجاز هستند.', 'warning');
        fileInput.value = '';
        return;
    }
    
    // Validate file size (2MB)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
        showAlert('اندازه فایل نباید بیشتر از 2 مگابایت باشد.', 'warning');
        fileInput.value = '';
        return;
    }
    
    // Show confirmation
    if (confirm('آیا می‌خواهید تصویر پروفایل خود را تغییر دهید؟')) {
        // Show loading toast
        showToast('در حال آپلود تصویر...', 'info', 10000);
        
        // Submit the form
        document.getElementById('avatarForm').submit();
    } else {
        // Clear the file input if user cancels
        fileInput.value = '';
    }
}
</script>

<?php include '../includes/footer.php'; ?>
</body>
</html>
