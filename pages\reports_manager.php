<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'report_builder', 'view')) {
    header('Location: ../pages/dashboard.php?error=access_denied');
    exit;
}

$page_title = "مدیریت گزارش‌ها";
include '../includes/header.php';

// دریافت گزارش‌های ذخیره شده
$stmt = $pdo->prepare("
    SELECT sr.*, rt.name as template_name, u.name as creator_name
    FROM saved_reports sr
    LEFT JOIN report_templates rt ON sr.template_id = rt.id
    LEFT JOIN users u ON sr.created_by = u.id
    WHERE sr.created_by = ? OR ? IN (SELECT id FROM users WHERE role = 'admin')
    ORDER BY sr.created_at DESC
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
$savedReports = $stmt->fetchAll(PDO::FETCH_ASSOC);

// دریافت قالب‌های گزارش
$stmt = $pdo->prepare("
    SELECT rt.*, u.name as creator_name
    FROM report_templates rt
    LEFT JOIN users u ON rt.created_by = u.id
    WHERE rt.created_by = ? OR rt.is_default = 1 OR ? IN (SELECT id FROM users WHERE role = 'admin')
    ORDER BY rt.is_default DESC, rt.created_at DESC
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="main-content">
    <div class="page-header">
        <h1><i class="fas fa-chart-bar"></i> مدیریت گزارش‌ها</h1>
        <div class="page-actions">
            <a href="report_builder.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> گزارش جدید
            </a>
        </div>
    </div>

    <!-- تب‌ها -->
    <ul class="nav nav-tabs" id="reportTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="saved-reports-tab" data-bs-toggle="tab" 
                    data-bs-target="#saved-reports" type="button" role="tab">
                <i class="fas fa-save"></i> گزارش‌های ذخیره شده
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="templates-tab" data-bs-toggle="tab" 
                    data-bs-target="#templates" type="button" role="tab">
                <i class="fas fa-template"></i> قالب‌های گزارش
            </button>
        </li>
    </ul>

    <div class="tab-content" id="reportTabsContent">
        <!-- گزارش‌های ذخیره شده -->
        <div class="tab-pane fade show active" id="saved-reports" role="tabpanel">
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> گزارش‌های ذخیره شده</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($savedReports)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <p class="text-muted">هیچ گزارش ذخیره شده‌ای وجود ندارد</p>
                            <a href="report_builder.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> ایجاد اولین گزارش
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>نام گزارش</th>
                                        <th>قالب</th>
                                        <th>ایجاد کننده</th>
                                        <th>تاریخ ایجاد</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($savedReports as $report): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($report['name']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge badge-info">
                                                    <?= htmlspecialchars($report['template_name'] ?? 'نامشخص') ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($report['creator_name'] ?? 'نامشخص') ?></td>
                                            <td>
                                                <?= date('Y/m/d H:i', strtotime($report['created_at'])) ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-primary view-report" 
                                                            data-report-id="<?= $report['id'] ?>">
                                                        <i class="fas fa-eye"></i> مشاهده
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-success add-to-dashboard" 
                                                            data-report-id="<?= $report['id'] ?>">
                                                        <i class="fas fa-plus-square"></i> افزودن به داشبورد
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-warning edit-report" 
                                                            data-report-id="<?= $report['id'] ?>">
                                                        <i class="fas fa-edit"></i> ویرایش
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger delete-report" 
                                                            data-report-id="<?= $report['id'] ?>">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- قالب‌های گزارش -->
        <div class="tab-pane fade" id="templates" role="tabpanel">
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-template"></i> قالب‌های گزارش</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($templates as $template): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card template-card">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <?= htmlspecialchars($template['name']) ?>
                                            <?php if ($template['is_default']): ?>
                                                <span class="badge badge-success">پیش‌فرض</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="card-text text-muted small">
                                            <?= htmlspecialchars($template['description'] ?? '') ?>
                                        </p>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                ایجاد شده توسط: <?= htmlspecialchars($template['creator_name'] ?? 'سیستم') ?>
                                            </small>
                                        </p>
                                        <div class="btn-group w-100" role="group">
                                            <button type="button" class="btn btn-sm btn-primary use-template" 
                                                    data-template-id="<?= $template['id'] ?>">
                                                <i class="fas fa-play"></i> استفاده
                                            </button>
                                            <?php if (!$template['is_default']): ?>
                                                <button type="button" class="btn btn-sm btn-danger delete-template" 
                                                        data-template-id="<?= $template['id'] ?>">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال نمایش گزارش -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">نمایش گزارش</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modalReportContent">
                    <!-- محتوای گزارش اینجا بارگذاری می‌شود -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال افزودن به داشبورد -->
<div class="modal fade" id="dashboardModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">افزودن به داشبورد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="dashboardForm">
                    <input type="hidden" id="dashboardReportId">
                    <div class="mb-3">
                        <label class="form-label">نوع ویجت:</label>
                        <select class="form-control" id="widgetType">
                            <option value="table">جدول</option>
                            <option value="chart">نمودار</option>
                            <option value="card">کارت</option>
                            <option value="kpi">شاخص کلیدی</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">عرض (1-12):</label>
                        <input type="number" class="form-control" id="widgetWidth" min="1" max="12" value="6">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ارتفاع (1-12):</label>
                        <input type="number" class="form-control" id="widgetHeight" min="1" max="12" value="4">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-primary" id="saveToDashboard">افزودن</button>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/reports-manager.js"></script>

<?php include '../includes/footer.php'; ?>
