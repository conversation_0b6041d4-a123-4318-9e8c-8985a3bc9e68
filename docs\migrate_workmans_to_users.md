# مایگریشن از جدول workmans به users

## مشکل قبلی

در صفحه فعالیت‌ها، اطلاعات مسئول اجرا از جدول `workmans` خوانده می‌شد در حالی که باید از جدول `users` خوانده شود. این باعث عدم یکپارچگی در سیستم می‌شد.

## تغییرات انجام شده

### 1. اصلاح فایل database.sql

#### حذف جدول workmans:
```sql
-- قبل:
CREATE TABLE IF NOT EXISTS workmans (
  id INT AUTO_INCREMENT PRIMARY KEY,
  full_name VARCHAR(50) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_persian_ci;

-- بعد:
-- جدول تکنسین‌ها حذف شد - از جدول users استفاده می‌شود
```

#### اصلاح foreign key در activity_assignees:
```sql
-- قبل:
FOREIG<PERSON> KEY (user_id) REFERENCES workmans(id) ON DELETE CASCADE

-- بعد:
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
```

### 2. اصلاح فایل pages/activities.php

#### تابع get_users():
```php
// قبل:
function get_users() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, full_name AS name FROM workmans ORDER BY full_name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// بعد:
function get_users() {
    global $pdo;
    $stmt = $pdo->query("SELECT id, name FROM users WHERE role != 'outsource' ORDER BY name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
```

#### تابع get_assignees_for_activity():
```php
// قبل:
function get_assignees_for_activity($activity_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT u.id, u.full_name AS name 
                            FROM workmans u 
                            JOIN activity_assignees aa ON u.id = aa.user_id 
                            WHERE aa.activity_id = ?");
    $stmt->execute([$activity_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// بعد:
function get_assignees_for_activity($activity_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT u.id, u.name 
                            FROM users u 
                            JOIN activity_assignees aa ON u.id = aa.user_id 
                            WHERE aa.activity_id = ? AND u.role != 'outsource'");
    $stmt->execute([$activity_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
```

### 3. اسکریپت مایگریشن (migrate_workmans_to_users.php)

اسکریپت مایگریشن شامل مراحل زیر است:

1. **بررسی وجود جدول workmans**
2. **انتقال داده‌ها**: 
   - خواندن تمام رکوردهای workmans
   - بررسی وجود کاربر با همان نام در users
   - اضافه کردن کاربر جدید (اگر وجود نداشته باشد)
   - به‌روزرسانی activity_assignees با ID جدید
3. **حذف foreign key قدیمی** به جدول workmans
4. **اضافه کردن foreign key جدید** به جدول users
5. **حذف جدول workmans**

### 4. ویژگی‌های اضافه شده

#### فیلتر کردن کاربر outsource:
```sql
WHERE role != 'outsource'
```
این باعث می‌شود کاربر برون‌سپاری در لیست مسئولین فعالیت نمایش داده نشود.

#### مدیریت خطا:
- بررسی وجود جدول قبل از مایگریشن
- مدیریت foreign key های موجود
- catch کردن exception ها

## مزایای تغییرات

### ✅ یکپارچگی سیستم
- تمام کاربران در یک جدول (users) نگهداری می‌شوند
- عدم تکرار اطلاعات کاربران

### ✅ مدیریت بهتر دسترسی‌ها
- امکان تعریف نقش‌های مختلف برای کاربران
- فیلتر کردن کاربران بر اساس نقش

### ✅ حذف پیچیدگی
- عدم نیاز به مدیریت دو جدول جداگانه
- کاهش پیچیدگی کوئری‌ها

### ✅ انعطاف‌پذیری
- امکان اضافه کردن فیلدهای جدید به جدول users
- مدیریت متمرکز اطلاعات کاربران

## نتیجه

### قبل از تغییر:
- ❌ استفاده از دو جدول جداگانه (users و workmans)
- ❌ عدم یکپارچگی در مدیریت کاربران
- ❌ پیچیدگی غیرضروری در کوئری‌ها

### بعد از تغییر:
- ✅ استفاده از یک جدول یکپارچه (users)
- ✅ مدیریت متمرکز کاربران
- ✅ فیلتر کردن کاربران بر اساس نقش
- ✅ کوئری‌های ساده‌تر و بهینه‌تر
- ✅ حفظ تمام داده‌های موجود
- ✅ مایگریشن ایمن و قابل اعتماد

## فایل‌های تغییر یافته

1. **database.sql**: حذف جدول workmans و اصلاح foreign key
2. **pages/activities.php**: اصلاح کوئری‌ها برای استفاده از جدول users
3. **migrate_workmans_to_users.php**: اسکریپت مایگریشن ایمن

## دستورالعمل اجرا

1. اجرای اسکریپت مایگریشن: `http://localhost/net_dombaz/migrate_workmans_to_users.php`
2. بررسی عملکرد صفحه فعالیت‌ها
3. حذف فایل مایگریشن پس از اطمینان از صحت عملکرد
