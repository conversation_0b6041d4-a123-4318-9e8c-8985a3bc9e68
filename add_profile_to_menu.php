<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== اضافه کردن صفحه پروفایل کاربری به منو ===\n\n";
    
    // اضافه کردن صفحه به system_pages
    $insert_page = $pdo->prepare("
        INSERT INTO system_pages (name, display_name, file_path, icon, is_active) 
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
            display_name = VALUES(display_name),
            file_path = VALUES(file_path),
            icon = VALUES(icon)
    ");
    
    $insert_page->execute([
        'profile',
        'پروفایل کاربری',
        'profile.php',
        'fas fa-user-circle',
        1
    ]);
    
    echo "✅ صفحه profile به جدول system_pages اضافه شد\n";
    
    // دریافت ID صفحه
    $page_id = $pdo->query("SELECT id FROM system_pages WHERE name = 'profile'")->fetchColumn();
    
    // اضافه کردن مجوزهای پایه
    $permissions = [
        ['view', 'مشاهده'],
        ['edit', 'ویرایش']
    ];
    
    foreach ($permissions as $perm) {
        $insert_perm = $pdo->prepare("
            INSERT INTO permissions (page_id, name, display_name) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
                display_name = VALUES(display_name)
        ");
        
        $insert_perm->execute([$page_id, $perm[0], $perm[1]]);
        echo "✅ مجوز '{$perm[0]}' ({$perm[1]}) اضافه شد\n";
    }
    
    // اضافه کردن مجوزها به همه نقش‌ها (چون همه باید به پروفایل خود دسترسی داشته باشند)
    $roles = $pdo->query("SELECT id, name FROM roles WHERE is_active = 1")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($roles as $role) {
        $perm_ids = $pdo->prepare("SELECT id FROM permissions WHERE page_id = ?");
        $perm_ids->execute([$page_id]);
        
        while ($perm_id = $perm_ids->fetchColumn()) {
            $check_existing = $pdo->prepare("SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission_id = ?");
            $check_existing->execute([$role['id'], $perm_id]);
            
            if ($check_existing->fetchColumn() == 0) {
                $insert_role_perm = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                $insert_role_perm->execute([$role['id'], $perm_id]);
            }
        }
        echo "✅ مجوزها به نقش {$role['name']} اضافه شد\n";
    }
    
    echo "\n=== نتیجه نهایی ===\n";
    echo "صفحه 'پروفایل کاربری' با موفقیت به منو اضافه شد\n";
    echo "مجوزهای تعریف شده:\n";
    echo "- view: مشاهده پروفایل\n";
    echo "- edit: ویرایش پروفایل\n";
    echo "\nهمه نقش‌ها دسترسی کامل به پروفایل دارند\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
