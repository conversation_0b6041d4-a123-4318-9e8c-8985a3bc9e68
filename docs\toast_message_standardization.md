# استانداردسازی سیستم نمایش پیام‌های Toast

## مشکل اصلی
در پروژه دو سیستم مجزا برای نمایش پیام‌های toast وجود داشت که باعث نمایش دوباره پیام‌ها می‌شد:

1. **سیستم عمومی در footer.php** - برای همه صفحات
2. **سیستم‌های محلی در صفحات خاص** - مثل profile.php, login.php, users.php

## راه‌حل پیاده‌سازی شده

### 1. سیستم عمومی (footer.php)
```javascript
// Show toast on page load if message exists in session
document.addEventListener('DOMContentLoaded', function() {
    // بررسی اینکه آیا صفحه خودش پیام را مدیریت می‌کند
    if (window.skipFooterToast) {
        return;
    }

    // Check if there's a toast message from PHP session
    const toastMessage = document.querySelector('meta[name="toast-message"]');
    const toastType = document.querySelector('meta[name="toast-type"]');

    if (toastMessage && toastMessage.content) {
        showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
    }
});
```

### 2. صفحات با سیستم محلی
صفحاتی که سیستم toast محلی دارند باید از `window.skipFooterToast = true` استفاده کنند:

```javascript
document.addEventListener('DOMContentLoaded', function() {
    // جلوگیری از نمایش دوباره پیام در footer
    window.skipFooterToast = true;
    
    // Check if there's a toast message from PHP session
    const toastMessage = document.querySelector('meta[name="toast-message"]');
    const toastType = document.querySelector('meta[name="toast-type"]');
    
    if (toastMessage && toastMessage.content) {
        showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
    }
});
```

## صفحات اصلاح شده

### ✅ صفحات با سیستم محلی (اصلاح شده)
- `pages/profile.php` - ✅ اضافه شد `skipFooterToast`
- `pages/login.php` - ✅ اضافه شد `skipFooterToast`
- `pages/users.php` - ✅ اضافه شد `skipFooterToast`
- `pages/work_order_edit.php` - ✅ قبلاً داشت `skipFooterToast`

### ✅ صفحات با سیستم عمومی (صحیح)
- `pages/submit_report.php` - ✅ فقط از footer استفاده می‌کند
- `pages/devices.php` - ✅ فقط از footer استفاده می‌کند
- `pages/dashboard.php` - ✅ فقط از footer استفاده می‌کند
- `pages/my_tasks.php` - ✅ فقط از footer استفاده می‌کند
- `pages/reports_list.php` - ✅ فقط از footer استفاده می‌کند
- `pages/repair_approvals.php` - ✅ فقط از footer استفاده می‌کند
- `pages/work_order.php` - ✅ فقط از footer استفاده می‌کند

## قوانین استاندارد

### 1. برای صفحات جدید
- **اولویت اول**: استفاده از سیستم عمومی footer.php
- فقط meta tag ها را تنظیم کنید و footer خودکار پیام را نمایش می‌دهد

### 2. اگر نیاز به سیستم محلی دارید
- تابع `showToast` محلی تعریف کنید
- حتماً `window.skipFooterToast = true` اضافه کنید
- از همان ساختار meta tag ها استفاده کنید

### 3. ساختار PHP برای تنظیم پیام
```php
// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}
```

### 4. ساختار HTML برای meta tag ها
```php
<?php if ($toast_message): ?>
    <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
    <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
<?php endif; ?>
```

## نتیجه
✅ مشکل نمایش دوباره پیام‌ها در profile.php و سایر صفحات حل شد
✅ سیستم استاندارد و یکپارچه برای همه صفحات
✅ قابلیت انعطاف برای صفحات خاص که نیاز به سیستم محلی دارند
