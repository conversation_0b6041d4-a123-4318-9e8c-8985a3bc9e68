# پیاده‌سازی تب‌های جزئیات دستورکار و تایید نهایی

## تغییرات انجام شده

### 1. به‌روزرسانی دیتابیس

#### اضافه کردن وضعیت "تایید نهایی و بسته شد":

**فایل `database.sql`:**
```sql
-- جدول work_orders
status ENUM('پیش‌نویس', 'دستورکار صادر شد', 'در دست اجرا', 'انجام و تایید شده', 'منتظر تایید', 'تایید و بسته شد', 'در حال انجام', 'برگشت جهت اصلاح', 'لغو شده', 'انجام شده', 'برون سپاری شد', 'پایان برون سپاری', 'پایان تعمیر', 'تایید نهایی و بسته شد') NOT NULL DEFAULT 'دستورکار صادر شد',

-- جدول breakdown_reports  
status ENUM('گزارش شده', 'در دست بررسی', 'تبدیل به دستورکار شد', 'رد شده', 'انجام و تایید شده', 'تایید و بسته شد', 'منتظر تایید', 'در حال انجام', 'برگشت جهت اصلاح', 'باز', 'برون سپاری شد', 'پایان برون سپاری', 'پایان تعمیر', 'تایید نهایی و بسته شد') NOT NULL DEFAULT 'گزارش شده',
```

### 2. تغییر ساختار مودال جزئیات دستورکار

#### قبل (ساختار ساده):
```html
<div id="detailsModal" class="modal">
    <div id="detailsModalBody" class="modal-body">
        <span class="close-btn" onclick="closeModal('#detailsModal')">&times;</span>
        <div id="modalContent">...در حال بارگذاری...</div>
    </div>
</div>
```

#### بعد (ساختار تب‌دار):
```html
<div id="detailsModal" class="modal">
    <div id="detailsModalBody" class="modal-body" style="max-width: 900px;">
        <div class="modal-header">
            <h4 id="modalTitle">جزئیات دستور کار</h4>
            <div>
                <button id="finalApprovalBtn" class="btn btn-success me-2" onclick="finalApproveWorkOrder()">
                    تایید نهایی و بسته شد
                </button>
                <span class="close-btn" onclick="closeModal('#detailsModal')">&times;</span>
            </div>
        </div>
        
        <!-- تب‌ها -->
        <div class="tab-container">
            <ul class="nav nav-tabs" id="detailsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="details-tab">جزئیات</button>
                </li>
                <li class="nav-item" role="presentation" id="repair-tab-li" style="display: none;">
                    <button class="nav-link" id="repair-tab">گزارش تعمیر</button>
                </li>
                <li class="nav-item" role="presentation" id="outsourcing-tab-li" style="display: none;">
                    <button class="nav-link" id="outsourcing-tab">گزارش برون‌سپاری</button>
                </li>
            </ul>
            
            <div class="tab-content" id="detailsTabContent">
                <div class="tab-pane fade show active" id="details-content">
                    <div id="modalContent">...در حال بارگذاری...</div>
                </div>
                <div class="tab-pane fade" id="repair-content">
                    <div id="repairContent">گزارش تعمیری یافت نشد.</div>
                </div>
                <div class="tab-pane fade" id="outsourcing-content">
                    <div id="outsourcingContent">اطلاعات برون‌سپاری یافت نشد.</div>
                </div>
            </div>
        </div>
    </div>
</div>
```

### 3. CSS برای تب‌ها

```css
/* استایل تب‌ها */
.tab-container {
    margin-top: 15px;
}

.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    color: #495057;
    background-color: #f8f9fa;
    margin-left: 2px;
    padding: 10px 20px;
    font-weight: 500;
    cursor: pointer;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    background-color: #e9ecef;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    border-bottom: 2px solid #fff;
    margin-bottom: -2px;
}

.tab-content {
    padding: 20px 0;
}

.modal-header {
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
}
```

### 4. JavaScript برای مدیریت تب‌ها

#### مدیریت نمایش تب‌ها:
```javascript
function showWorkOrderDetails(id) {
    currentWorkOrderId = id;
    
    // Reset تب‌ها
    document.getElementById('details-tab').classList.add('active');
    document.getElementById('repair-tab').classList.remove('active');
    document.getElementById('outsourcing-tab').classList.remove('active');
    
    // مخفی کردن تب‌های اضافی
    document.getElementById('repair-tab-li').style.display = 'none';
    document.getElementById('outsourcing-tab-li').style.display = 'none';
    document.getElementById('finalApprovalBtn').style.display = 'none';
    
    // نمایش تب‌ها بر اساس داده‌های موجود
    if (currentRepairReportData) {
        document.getElementById('repair-tab-li').style.display = 'block';
        showRepairReportInTab();
    }
    
    if (currentRepairReportData && (currentRepairReportData.exit_date || currentRepairReportData.back_date)) {
        document.getElementById('outsourcing-tab-li').style.display = 'block';
        showOutsourcingInTab();
    }
    
    // نمایش دکمه تایید نهایی
    if (wo.status !== 'تایید نهایی و بسته شد' && wo.status !== 'لغو شده') {
        document.getElementById('finalApprovalBtn').style.display = 'inline-block';
    }
}
```

#### مدیریت کلیک روی تب‌ها:
```javascript
$(document).on('click', '.nav-link', function(e) {
    e.preventDefault();
    const target = $(this).data('bs-target');
    
    // حذف active از همه تب‌ها
    $('.nav-link').removeClass('active');
    $('.tab-pane').removeClass('show active');
    
    // اضافه کردن active به تب انتخاب شده
    $(this).addClass('active');
    $(target).addClass('show active');
});
```

### 5. توابع نمایش محتوا در تب‌ها

#### تب گزارش تعمیر:
```javascript
function showRepairReportInTab() {
    if (!currentRepairReportData) return;
    const rep = currentRepairReportData;
    const content = document.getElementById('repairContent');

    let html = `<div class="details-grid">`;
    html += `<div class="detail-item detail-full-width"><strong>شروع:</strong><span>${rep.start_display || '-'}</span></div>`;
    html += `<div class="detail-item detail-full-width"><strong>پایان:</strong><span>${rep.end_display || '-'}</span></div>`;
    // ... سایر فیلدها
    html += `</div>`;

    content.innerHTML = html;
}
```

#### تب برون‌سپاری:
```javascript
function showOutsourcingInTab() {
    if (!currentRepairReportData) return;
    const rep = currentRepairReportData;
    const content = document.getElementById('outsourcingContent');
    
    let html = `<div class="details-grid">`;
    
    // اطلاعات خروج و بازگشت دستگاه
    if (rep.exit_date) {
        html += `<div class="detail-item"><strong>تاریخ خروج:</strong><span>${rep.exit_date_shamsi || rep.exit_date}</span></div>`;
        // ... سایر فیلدها
    }
    
    html += `</div>`;
    content.innerHTML = html;
}
```

### 6. تایید نهایی دستور کار

#### JavaScript:
```javascript
function finalApproveWorkOrder() {
    if (!currentWorkOrderId) return;
    
    if (!confirm('آیا از تایید نهایی و بستن این دستور کار مطمئن هستید؟ این عمل غیرقابل بازگشت است.')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'final_approve_work_order');
    formData.append('work_order_id', currentWorkOrderId);
    
    fetch('work_order.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            closeModal('#detailsModal');
            location.reload();
        } else {
            showToast(data.message, 'error');
        }
    });
}
```

#### PHP:
```php
else if ($action === 'final_approve_work_order') {
    $work_order_id = $_POST['work_order_id'] ?? 0;
    if (!$work_order_id) {
        json_response(['success' => false, 'message' => 'شناسه دستور کار نامعتبر است.'], 400);
    }
    
    try {
        $db->beginTransaction();
        
        // به‌روزرسانی وضعیت دستور کار
        $stmt = $db->prepare("UPDATE work_orders SET status = 'تایید نهایی و بسته شد' WHERE id = ?");
        $stmt->execute([$work_order_id]);
        
        // به‌روزرسانی وضعیت گزارش خرابی مرتبط
        $stmt = $db->prepare("UPDATE breakdown_reports SET status = 'تایید نهایی و بسته شد' WHERE converted_to_wo_id = ?");
        $stmt->execute([$work_order_id]);
        
        $db->commit();
        json_response(['success' => true, 'message' => 'دستور کار با موفقیت تایید نهایی و بسته شد.']);
        
    } catch (Exception $e) {
        $db->rollBack();
        json_response(['success' => false, 'message' => 'خطا در تایید نهایی: ' . $e->getMessage()], 500);
    }
}
```

## ویژگی‌های پیاده‌سازی شده

### 1. تب‌های هوشمند
- **تب جزئیات**: همیشه نمایش داده می‌شود
- **تب گزارش تعمیر**: فقط اگر گزارش تعمیر وجود داشته باشد
- **تب برون‌سپاری**: فقط اگر اطلاعات برون‌سپاری وجود داشته باشد

### 2. دکمه تایید نهایی
- فقط برای دستورکارهایی که هنوز بسته نشده‌اند نمایش داده می‌شود
- تایید کاربر قبل از اجرا
- به‌روزرسانی همزمان دستور کار و گزارش خرابی مرتبط

### 3. تجربه کاربری بهبود یافته
- نمایش اطلاعات در تب‌های منطقی
- دسترسی آسان به اطلاعات مختلف
- عدم نیاز به باز کردن مودال‌های جداگانه

## نتیجه

### قبل از تغییر:
- ❌ دکمه‌های جداگانه برای مشاهده گزارش تعمیر و برون‌سپاری
- ❌ باز شدن مودال‌های متعدد
- ❌ عدم امکان تایید نهایی دستور کار

### بعد از تغییر:
- ✅ نمایش تمام اطلاعات در یک مودال با تب‌های مختلف
- ✅ دسترسی آسان به اطلاعات مختلف
- ✅ امکان تایید نهایی و بستن دستور کار
- ✅ به‌روزرسانی همزمان گزارش خرابی مرتبط
- ✅ تجربه کاربری بهبود یافته
