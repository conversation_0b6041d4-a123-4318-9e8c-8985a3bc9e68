<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
$pdo = db_connect();
include '../includes/header.php';

if (!is_admin()) {
    echo "<p>فقط مدیر سیستم اجازه دسترسی دارد.</p></body></html>";
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
    $stmt = $pdo->prepare("INSERT INTO users (username, password, role, name) VALUES (?, ?, ?, ?)");
    $stmt->execute([
        $_POST['username'],
        password_hash($_POST['password'], PASSWORD_DEFAULT),
        $_POST['role'],
        $_POST['name']
    ]);
        
        $_SESSION['toast_message'] = 'کاربر با موفقیت افزوده شد.';
        $_SESSION['toast_type'] = 'success';
        header('Location: users.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['toast_message'] = 'خطا در افزودن کاربر: ' . $e->getMessage();
        $_SESSION['toast_type'] = 'danger';
        header('Location: users.php');
        exit;
    }
}

require_once '../includes/user_helper.php';
$users = $pdo->query("SELECT * FROM users ORDER BY CASE WHEN id = 99999 THEN 1 ELSE 0 END, name")->fetchAll();

// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدیریت کاربران</title>
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>
    <style>
        /* Toast Message Styles - Global */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 1rem 1.5rem;
            margin-bottom: 10px;
            border-right: 4px solid;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            opacity: 0;
        }
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        .toast-success {
            border-color: #28a745;
            color: #155724;
        }
        .toast-warning {
            border-color: #ffc107;
            color: #856404;
        }
        .toast-danger {
            border-color: #dc3545;
            color: #721c24;
        }
        .toast-info {
            border-color: #17a2b8;
            color: #0c5460;
        }
        .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .toast-title {
            font-weight: 600;
            font-size: var(--fs-sm);
        }
        .toast-close {
            background: none;
            border: none;
            font-size: var(--fs-xl);
            cursor: pointer;
            color: #666;
            padding: 0;
            line-height: 1;
        }
        .toast-message {
            font-size: var(--fs-sm);
            line-height: 1.4;
        }
    </style>
</head>
<body>

<h3>مدیریت کاربران</h3>
<form method="post">
    <input name="name" placeholder="نام کامل" required><br>
    <input name="username" placeholder="نام کاربری" required><br>
    <input name="password" type="password" placeholder="رمز عبور" required><br>
    <select name="role">
        <option value="user">کاربر عادی</option>
        <option value="admin">مدیر</option>
    </select><br>
    <button type="submit">افزودن کاربر</button>
</form>

<h4>لیست کاربران</h4>
<ul>
<?php foreach ($users as $u): ?>
    <li>
        <?= htmlspecialchars($u['name']) ?>
        (<?= htmlspecialchars($u['username']) ?> | <?= $u['role'] ?>)
        <?php if (isOutsourceUser($u['id'])): ?>
            <span style="color: #dc3545; font-weight: bold;"> - کاربر سیستمی (برون سپاری)</span>
        <?php endif; ?>
    </li>
<?php endforeach; ?>
</ul>
    <div id="toast-container" class="toast-container"></div>
    
    <script>
    // Global Toast Message Functions
    function showToast(message, type = 'info', duration = 4000) {
        const container = document.getElementById('toast-container');
        if (!container) return;
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        const title = type === 'success' ? 'موفقیت' : 
                      type === 'warning' ? 'هشدار' : 
                      type === 'danger' ? 'خطا' : 'اطلاعات';
        
        toast.innerHTML = `
            <div class="toast-header">
                <span class="toast-title">${title}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
            <div class="toast-message">${message}</div>
        `;
        
        container.appendChild(toast);
        
        // Show animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // Auto remove after duration
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 300);
        }, duration);
    }

    // Show toast on page load if message exists in session
    document.addEventListener('DOMContentLoaded', function() {
        // جلوگیری از نمایش دوباره پیام در footer
        window.skipFooterToast = true;

        // Check if there's a toast message from PHP session
        const toastMessage = document.querySelector('meta[name="toast-message"]');
        const toastType = document.querySelector('meta[name="toast-type"]');

        if (toastMessage && toastMessage.content) {
            showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
        }
    });
    </script>
</body>
</html>
