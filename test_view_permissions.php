<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== تست مجوزهای مشاهده محدود ===\n\n";
    
    // تست کاربر usertest (نقش user)
    echo "1. تست کاربر usertest (نقش user):\n";
    echo "===================================\n";
    
    $user_stmt = $pdo->prepare("
        SELECT u.id, u.username, r.name as role_name
        FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.username = 'usertest'
    ");
    $user_stmt->execute();
    $user = $user_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "کاربر: {$user['username']} (نقش: {$user['role_name']})\n";
        
        // بررسی مجوزهای work_order
        $work_order_perms = $pdo->prepare("
            SELECT p.name
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.id
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE rp.role_id = (SELECT role_id FROM users WHERE username = 'usertest')
            AND sp.name = 'work_order'
        ");
        $work_order_perms->execute();
        $wo_perms = $work_order_perms->fetchAll(PDO::FETCH_COLUMN);
        
        echo "مجوزهای work_order: " . implode(', ', $wo_perms) . "\n";
        
        // بررسی مجوزهای reports_list
        $reports_perms = $pdo->prepare("
            SELECT p.name
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.id
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE rp.role_id = (SELECT role_id FROM users WHERE username = 'usertest')
            AND sp.name = 'reports_list'
        ");
        $reports_perms->execute();
        $rep_perms = $reports_perms->fetchAll(PDO::FETCH_COLUMN);
        
        echo "مجوزهای reports_list: " . implode(', ', $rep_perms) . "\n";
        
        // بررسی مجوزهای dashboard
        $dashboard_perms = $pdo->prepare("
            SELECT p.name
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.id
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE rp.role_id = (SELECT role_id FROM users WHERE username = 'usertest')
            AND sp.name = 'dashboard'
        ");
        $dashboard_perms->execute();
        $dash_perms = $dashboard_perms->fetchAll(PDO::FETCH_COLUMN);
        
        echo "مجوزهای dashboard: " . implode(', ', $dash_perms) . "\n";
        
        // بررسی مجوزهای user_management
        $user_mgmt_perms = $pdo->prepare("
            SELECT p.name
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.id
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE rp.role_id = (SELECT role_id FROM users WHERE username = 'usertest')
            AND sp.name = 'user_management'
        ");
        $user_mgmt_perms->execute();
        $um_perms = $user_mgmt_perms->fetchAll(PDO::FETCH_COLUMN);
        
        echo "مجوزهای user_management: " . implode(', ', $um_perms) . "\n\n";
        
        // نتیجه‌گیری
        echo "نتیجه‌گیری:\n";
        echo "- work_order: " . (in_array('view_own', $wo_perms) ? "✅ فقط دستورکارهای خود" : (in_array('view_all', $wo_perms) ? "⚠️ همه دستورکارها" : "❌ هیچ دسترسی")) . "\n";
        echo "- reports_list: " . (in_array('view_own', $rep_perms) ? "✅ فقط گزارش‌های خود" : (in_array('view_all', $rep_perms) ? "⚠️ همه گزارش‌ها" : "❌ هیچ دسترسی")) . "\n";
        echo "- dashboard: " . (in_array('view_statistics', $dash_perms) ? "⚠️ مشاهده آمار" : "✅ بدون آمار") . "\n";
        echo "- user_management: " . (in_array('view_all_profiles', $um_perms) ? "⚠️ همه پروفایل‌ها" : "✅ فقط پروفایل خود") . "\n";
        
    } else {
        echo "کاربر usertest یافت نشد!\n";
    }
    
    echo "\n2. تست کاربر admin:\n";
    echo "====================\n";
    
    $admin_stmt = $pdo->prepare("
        SELECT u.id, u.username, r.name as role_name
        FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.username = 'admin'
    ");
    $admin_stmt->execute();
    $admin = $admin_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "کاربر: {$admin['username']} (نقش: {$admin['role_name']})\n";
        echo "نتیجه: ✅ دسترسی کامل به همه بخش‌ها\n";
    } else {
        echo "کاربر admin یافت نشد!\n";
    }
    
    echo "\n=== خلاصه تغییرات اعمال شده ===\n";
    echo "1. ✅ work_order.php: فیلتر بر اساس view_all/view_own\n";
    echo "2. ✅ reports_list.php: فیلتر بر اساس view_all/view_own\n";
    echo "3. ✅ user_management.php: فیلتر بر اساس view_all_profiles و edit_all_profiles/edit_own_profile\n";
    echo "4. ✅ dashboard.php: کنترل نمایش آمار و فعالیت‌ها\n";
    echo "5. ✅ my_tasks.php: از قبل درست کار می‌کرد\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
