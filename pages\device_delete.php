<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';

// بررسی مجوز حذف دستگاه
require_page_access('devices', 'delete');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['id'])) {
    $_SESSION['toast_message'] = 'درخواست نامعتبر است';
    $_SESSION['toast_type'] = 'danger';
    header('Location: devices.php');
    exit;
}

$id = (int)$_POST['id'];
$pdo = db_connect();

// **اصلاح**: انتخاب ستون images برای حذف فایل‌ها
$stmt = $pdo->prepare("SELECT images FROM devices WHERE id = ?");
$stmt->execute([$id]);
$device = $stmt->fetch();

if (!$device) {
    $_SESSION['toast_message'] = 'دستگاه یافت نشد';
    $_SESSION['toast_type'] = 'danger';
    header('Location: devices.php');
    exit;
}

// **اصلاح**: حذف چندین فایل تصویر (در صورت وجود)
if (!empty($device['images'])) {
    $images = json_decode($device['images'], true);
    if (is_array($images)) {
        foreach ($images as $imageName) {
            $filePath = "../uploads/devices/" . $imageName;
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }
}

// حذف رکورد دستگاه
$stmt = $pdo->prepare("DELETE FROM devices WHERE id = ?");
$success = $stmt->execute([$id]);

if ($success) {
    $_SESSION['toast_message'] = 'دستگاه با موفقیت حذف شد';
    $_SESSION['toast_type'] = 'success';
} else {
    $_SESSION['toast_message'] = 'خطا در حذف دستگاه';
    $_SESSION['toast_type'] = 'danger';
}

header('Location: devices.php');
exit;
