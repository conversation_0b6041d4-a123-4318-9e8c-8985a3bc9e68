<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== تست آیتم‌های جدید منو ===\n\n";
    
    // بررسی صفحات جدید در system_pages
    $new_pages = $pdo->query("
        SELECT name, display_name, file_path, icon, is_active
        FROM system_pages 
        WHERE name IN ('repair_approvals', 'profile')
        ORDER BY name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "صفحات جدید اضافه شده:\n";
    echo "========================\n";
    
    foreach ($new_pages as $page) {
        echo "📄 {$page['display_name']} ({$page['name']})\n";
        echo "   - فایل: {$page['file_path']}\n";
        echo "   - آیکون: {$page['icon']}\n";
        echo "   - وضعیت: " . ($page['is_active'] ? 'فعال' : 'غیرفعال') . "\n";
        
        // بررسی مجوزهای هر صفحه
        $page_perms = $pdo->prepare("
            SELECT p.name, p.display_name
            FROM permissions p
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE sp.name = ?
            ORDER BY p.name
        ");
        $page_perms->execute([$page['name']]);
        $perms = $page_perms->fetchAll(PDO::FETCH_ASSOC);
        
        echo "   - مجوزها: ";
        if ($perms) {
            $perm_list = array_map(function($p) { return $p['name'] . ' (' . $p['display_name'] . ')'; }, $perms);
            echo implode(', ', $perm_list);
        } else {
            echo "هیچ مجوزی تعریف نشده";
        }
        echo "\n\n";
    }
    
    // بررسی دسترسی کاربران
    echo "دسترسی کاربران:\n";
    echo "================\n";
    
    $users_access = $pdo->query("
        SELECT DISTINCT u.username, r.display_name as role_name, sp.display_name as page_name
        FROM users u
        JOIN roles r ON u.role_id = r.id
        JOIN role_permissions rp ON r.id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        JOIN system_pages sp ON p.page_id = sp.id
        WHERE sp.name IN ('repair_approvals', 'profile') AND p.name = 'view'
        ORDER BY u.username, sp.name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    $current_user = '';
    foreach ($users_access as $access) {
        if ($current_user !== $access['username']) {
            $current_user = $access['username'];
            echo "\n👤 {$access['username']} (نقش: {$access['role_name']}):\n";
        }
        echo "   ✅ {$access['page_name']}\n";
    }
    
    // بررسی کل منو
    echo "\n=== منوی کامل سیستم ===\n";
    
    $all_pages = $pdo->query("
        SELECT name, display_name, icon
        FROM system_pages 
        WHERE is_active = 1
        ORDER BY id
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($all_pages as $page) {
        $icon = $page['icon'] ? $page['icon'] : 'fas fa-file';
        echo "🔗 {$page['display_name']} ({$page['name']})\n";
    }
    
    echo "\n✅ همه چیز آماده است! صفحات جدید در منو نمایش داده خواهند شد.\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
