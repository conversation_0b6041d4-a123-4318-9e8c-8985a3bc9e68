// assets/js/datepicker-init.js

function initializeDatepicker(selector) {
    if (!window.jQuery) {
        console.error("jQuery is not loaded.");
        return;
    }

    if (typeof $.fn.persianDatepicker !== 'function') {
        console.error("PersianDatepicker plugin is not loaded.");
        return;
    }

    if ($(selector).length) {
        $(selector).persianDatepicker({
            format: 'YYYY/MM/DD',
            initialValue: false,
            initialValueType: 'persian',
            observer: true,
            autoClose: true,
            calendarType: 'persian',
            timePicker: {
                enabled: false
            }
        });
    } else {
        console.warn(`عنصر ${selector} در صفحه یافت نشد.`);
    }
}
