<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// بررسی مجوز ایجاد گزارش خرابی
require_page_access('reports_list', 'create');

$pdo = db_connect();

// **تغییر ۱**: دریافت اطلاعات بیشتر از دستگاه‌ها برای نمایش در فرم
$devices = $pdo->query("
    SELECT d.id, d.name, d.serial_number, l.location_name 
    FROM devices d
    LEFT JOIN locations l ON d.location = l.id
    WHERE d.status = 'فعال' ORDER BY d.name ASC
")->fetchAll(PDO::FETCH_ASSOC);

// پردازش فرم در صورت ارسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $device_id = $_POST['device_id'];
    $problem_description = $_POST['problem_description'];
    $urgency = $_POST['urgency'];
    $reported_by_id = current_user_id();

    // **تغییر ۲**: دریافت تاریخ و زمان‌های جدید از فرم
    // تبدیل تاریخ شمسی به میلادی
    $breakdown_date = !empty($_POST['breakdown_date']) ? to_miladi($_POST['breakdown_date']) : date('Y-m-d');
    $breakdown_time = !empty($_POST['breakdown_time']) ? $_POST['breakdown_time'] : date('H:i');
    $breakdown_datetime = $breakdown_date . ' ' . $breakdown_time;

    $line_stoppage_datetime = null;
    if (isset($_POST['line_stopped']) && !empty($_POST['stop_date']) && !empty($_POST['stop_time'])) {
        $stop_date = to_miladi($_POST['stop_date']);
        $stop_time = $_POST['stop_time'];
        $line_stoppage_datetime = $stop_date . ' ' . $stop_time;
    }

    if (empty($device_id) || empty($problem_description)) {
        $_SESSION['toast_message'] = 'لطفاً دستگاه و شرح مشکل را وارد کنید.';
        $_SESSION['toast_type'] = 'warning';
        header('Location: submit_report.php');
        exit;
    }

    try {
		$pdo->beginTransaction();
        // **تغییر ۳**: ذخیره فیلدهای جدید در دیتابیس
        $stmt = $pdo->prepare(
            "INSERT INTO breakdown_reports (device_id, reported_by_id, problem_description, urgency, breakdown_datetime, line_stoppage_datetime, report_datetime) 
             VALUES (:device_id, :reported_by_id, :problem_description, :urgency, :breakdown_datetime, :line_stoppage_datetime, NOW())"
        );

        $stmt->execute([
            ':device_id' => $device_id,
            ':reported_by_id' => $reported_by_id,
            ':problem_description' => $problem_description,
            ':urgency' => $urgency,
            ':breakdown_datetime' => $breakdown_datetime,
            ':line_stoppage_datetime' => $line_stoppage_datetime
        ]);

        
        $reportId = $pdo->lastInsertId(); // دریافت شناسه گزارش ثبت شده

        // **بخش جدید: پردازش آپلود چند عکس**
        if (isset($_FILES['images'])) {
            $uploadDir = '../uploads/breakdowns/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $imageStmt = $pdo->prepare("INSERT INTO breakdown_report_images (report_id, image_path) VALUES (?, ?)");

            foreach ($_FILES['images']['tmp_name'] as $key => $tmpName) {
                if ($_FILES['images']['error'][$key] === UPLOAD_ERR_OK) {
                    $fileName = uniqid() . '-' . basename($_FILES['images']['name'][$key]);
                    $targetPath = $uploadDir . $fileName;

                    if (move_uploaded_file($tmpName, $targetPath)) {
                        $imageStmt->execute([$reportId, $targetPath]);
                    } else {
                        error_log("Failed to move uploaded file: $tmpName to $targetPath");
                    }
                } else {
                    error_log("Upload error for file $key: " . $_FILES['images']['error'][$key]);
                }
            }
        } else {
            error_log("No images found in \$_FILES");
        }

        $pdo->commit(); // تایید تمام تغییرات

        // ارسال پاسخ JSON برای نمایش پیام و انتقال
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'گزارش خرابی با موفقیت ثبت شد.',
            'redirect' => 'reports_list.php'
        ]);
        exit;

    } catch (Exception $e) {
		if ($pdo->inTransaction()) {
        $pdo->rollBack(); // بازگرداندن تغییرات در صورت بروز خطا
		}
        error_log("Breakdown report submission failed: " . $e->getMessage());

        // ارسال پاسخ JSON برای خطا
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'خطایی در ثبت گزارش رخ داد: ' . $e->getMessage()
        ]);
        exit;
    }
}

// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}

?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>گزارش خرابی جدید</title>
    <link rel="stylesheet" href="../assets/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css" />
    <link rel="stylesheet" href="../assets/css/buttons.css" />
    <link rel="stylesheet" href="../assets/css/select2.min.css" />
    <link rel="stylesheet" href="../assets/css/persian-datepicker.min.css" />
    <link rel="stylesheet" href="../assets/css/file_uploader.css" />
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>
    <style>
        /* Toast Message Styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 1rem 1.5rem;
            margin-bottom: 10px;
            border-right: 4px solid;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            opacity: 0;
        }
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        .toast-success { border-color: #28a745; color: #155724; }
        .toast-warning { border-color: #ffc107; color: #856404; }
        .toast-danger { border-color: #dc3545; color: #721c24; }
        .toast-info { border-color: #17a2b8; color: #0c5460; }
        .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .toast-title { font-weight: 600; font-size: 0.9rem; }
        .toast-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #666;
            padding: 0;
            line-height: 1;
        }
        .toast-message { font-size: 0.85rem; line-height: 1.4; }
    </style>
</head>
<body id="page-submit-report">
<div id="toast-container" class="toast-container"></div>

    <div class="report-container">
        <div class="report-card">
            
            <div class="report-header">
                <span><strong>درخواست کننده:</strong> <?= htmlspecialchars(current_user_name()) ?></span>
                <span><strong>تاریخ ارسال:</strong> <?= jdate('Y/m/d') ?></span>
            </div>

            <h2 class="report-title"><i class="fas fa-exclamation-triangle"></i> ثبت گزارش خرابی</h2>
            
            <form id="reportForm" method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="device_id" class="form-label">کدام دستگاه خراب شده است؟</label>
                    <select id="device_id" name="device_id" class="form-control" required>
                        <option value="">یک دستگاه را انتخاب کنید...</option>
                        <?php foreach ($devices as $device): ?>
                            <option value="<?= $device['id'] ?>" 
                                    data-location="<?= htmlspecialchars($device['location_name'] ?: '-') ?>"
                                    data-serial="<?= htmlspecialchars($device['serial_number'] ?: '-') ?>">
                                <?= htmlspecialchars($device['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div id="recent-reports-container"></div>
                </div>

                <div class="form-group">
                    <label for="breakdown_date" class="form-label">تاریخ و ساعت دقیق خرابی</label>
                    <div style="display: flex; gap: 1rem;">
    <input type="text" class="form-control persian-datepicker" name="breakdown_date" id="breakdown_date" placeholder="تاریخ خرابی" required autocomplete="off">
    <div class="time-input-container">
        <input type="text" class="form-control" id="breakdown_minute" placeholder="دقیقه" maxlength="2">
        <span class="time-colon">:</span>
		<input type="text" class="form-control" id="breakdown_hour" placeholder="ساعت" maxlength="2">
        <input type="hidden" name="breakdown_time" id="breakdown_time">
    </div>
</div>
                </div>

                <div class="form-group">
                    <label for="problem_description" class="form-label">شرح مشکل چیست؟</label>
                    <textarea id="problem_description" name="problem_description" rows="5" placeholder="لطفاً مشکل را با جزئیات کامل توضیح دهید..." class="form-control" required></textarea>
                </div>

                <div class="form-group">
                    <?php
                    require_once '../includes/file_uploader.php';
                    echo render_file_uploader([
                        'id' => 'reportImages',
                        'name' => 'images[]',
                        'accept' => 'image/*',
                        'max_size' => 2,
                        'label' => 'ضمیمه کردن عکس (اختیاری)',
                        'description' => 'فایل‌های عکس - حداکثر 2 مگابایت',
                        'show_existing' => false
                    ]);
                    ?>
                </div>

                <div class="form-group">
                    <label class="form-label">فوریت چقدر است؟</label>
                    <div class="urgency-group">
                        <input type="radio" id="urgency_normal" name="urgency" value="عادی" checked>
                        <label for="urgency_normal">عادی</label>
                        <input type="radio" id="urgency_urgent" name="urgency" value="فوری">
                        <label for="urgency_urgent">فوری ⚡️</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" name="line_stopped" id="line_stopped" class="form-check-input">
                        <label for="line_stopped" class="form-check-label">آیا خط تولید متوقف شده است؟</label>
                    </div>
                </div>

                <div class="form-group hidden-section" id="stop-time-section">
                    <label for="stop_date" class="form-label">تاریخ و ساعت توقف خط</label>
               <div style="display: flex; gap: 1rem;">
    <input type="text" class="form-control persian-datepicker" name="stop_date" id="stop_date" placeholder="تاریخ توقف" autocomplete="off">
    <div class="time-input-container">
        <input type="text" class="form-control" id="stop_minute" placeholder="دقیقه" maxlength="2">
        <span class="time-colon">:</span>
        <input type="text" class="form-control" id="stop_hour" placeholder="ساعت" maxlength="2">
        <input type="hidden" name="stop_time" id="stop_time">
    </div>
</div>    
                </div>

                <button type="submit" class="submit-btn" id="submitBtn">ارسال گزارش</button>
            </form>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    <script>
    $(document).ready(function() {
		
        // ۱. راه‌اندازی Select2 برای نمایش اطلاعات بیشتر دستگاه
        $('#device_id').select2({
            placeholder: 'جستجو یا انتخاب دستگاه...',
            templateResult: formatDevice,
            templateSelection: formatDeviceSelection
        });

        function formatDevice(device) {
            if (!device.id) { return device.text; }
            var location = $(device.element).data('location');
            var serial = $(device.element).data('serial');
            var displayText = device.text + 
                              ' <span style="color: #888;">|</span> ' +
                              '<span style="color: #777;">محل: ' + location + '</span> ' +
                              '<span style="color: #888;">|</span> ' +
                              '<span style="color: #777;">سریال: ' + serial + '</span>';
            return $(`<span>${displayText}</span>`);
        }
        
        function formatDeviceSelection(device) {
            return device.text;
        }

        // ۲. راه‌اندازی Datepickerها
        // تقویم‌ها از طریق footer.php به صورت خودکار راه‌اندازی می‌شوند

        // ۳. **کد اصلاح شده**: کنترل چک‌باکس توقف خط (به اینجا منتقل شد)
        $('#line_stopped').on('change', function() {
            const stopSection = $('#stop-time-section');
            if (this.checked) {
                stopSection.removeClass('hidden-section');
            } else {
                stopSection.addClass('hidden-section');
            }
        });

        // ۴. نمایش گزارش‌های اخیر هنگام انتخاب دستگاه
        $('#device_id').on('change', function() {
            const deviceId = $(this).val();
            const container = $('#recent-reports-container');
            
            if (!deviceId) {
                container.hide().html('');
                return;
            }

            container.show().html('در حال بررسی گزارش‌های اخیر...');
            fetch(`get_recent_reports.php?device_id=${deviceId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.reports.length > 0) {
                        let html = '<h6><i class="fa fa-history"></i> گزارش‌های اخیر این دستگاه:</h6>';
                        data.reports.forEach(report => {
                            html += `<div class="recent-report-item"><strong>${report.problem_description.substring(0, 50)}...</strong><br><small>گزارش شده در: ${to_shamsi_js(report.report_datetime, 'YYYY/MM/DD HH:mm')} - وضعیت: ${report.status}</small></div>`;
                        });
                        container.html(html);
                    } else {
                        container.hide().html('');
                    }
                })
                .catch(() => container.hide().html(''));
        });

        // پیش‌نمایش عکس‌ها از طریق کامپوننت file uploader مدیریت می‌شود
		// تابع کمکی برای راه‌اندازی یک مجموعه از فیلدهای زمان
    function initializeTimeInput(hourId, minuteId, hiddenId) {
        const hourInput = $(`#${hourId}`);
        const minuteInput = $(`#${minuteId}`);
        const hiddenInput = $(`#${hiddenId}`);

        function updateHiddenTime() {
            const hour = (hourInput.val() || '00').padStart(2, '0');
            const minute = (minuteInput.val() || '00').padStart(2, '0');
            hiddenInput.val(`${hour}:${minute}`);
        }

        hourInput.on('input', updateHiddenTime);
        minuteInput.on('input', updateHiddenTime);

        // پرش خودکار از ساعت به دقیقه
        hourInput.on('keyup', function() {
            if ($(this).val().length >= 2) {
                minuteInput.focus();
            }
        });
        
        // مقداردهی اولیه
        updateHiddenTime(); 
    }

    // راه‌اندازی تابع برای هر دو بخش
    initializeTimeInput('breakdown_hour', 'breakdown_minute', 'breakdown_time');
    initializeTimeInput('stop_hour', 'stop_minute', 'stop_time');
        // ۶. ارسال فرم با AJAX
        $('#reportForm').on('submit', function(event) {
           event.preventDefault(); // جلوگیری از ارسال معمولی فرم

           const form = this;
           const submitBtn = $('#submitBtn');

           // بررسی validation
           if (!form.checkValidity()) {
               showToast('لطفاً تمام فیلدهای اجباری را پر کنید.', 'warning');
               return;
           }

           submitBtn.prop('disabled', true).text('در حال ارسال...');

           // ارسال فرم با AJAX
           const formData = new FormData(form);

           fetch('submit_report.php', {
               method: 'POST',
               body: formData
           })
           .then(response => response.json())
           .then(result => {
               if (result.success) {
                   showToast(result.message, 'success');
                   // انتقال پس از 2 ثانیه
                   setTimeout(() => {
                       window.location.href = result.redirect;
                   }, 2000);
               } else {
                   showToast(result.message, 'error');
                   submitBtn.prop('disabled', false).text('ارسال گزارش');
               }
           })
           .catch(error => {
               console.error('Error:', error);
               showToast('خطا در ارتباط با سرور.', 'error');
               submitBtn.prop('disabled', false).text('ارسال گزارش');
           });
        });
    });

    // تابع کمکی برای تبدیل تاریخ در جاوااسکریپت
    function to_shamsi_js(gregorian_date_str, format = 'YYYY/MM/DD') {
        if (!gregorian_date_str) return '-';
        return new persianDate(new Date(gregorian_date_str)).format(format);
    }
</script>

<script src="../assets/js/jquery.min.js"></script>
<script src="../assets/js/modal_manager.js"></script>
<script src="../assets/js/file_uploader.js"></script>
<script src="../assets/js/select2.min.js"></script>
<script src="../assets/js/persian-date.min.js"></script>
<script src="../assets/js/persian-datepicker.min.js"></script>
<script src="../assets/js/datepicker-init.js"></script>

<script>
// Global Toast Message Functions
function showToast(message, type = 'info', duration = 4000) {
    const container = document.getElementById('toast-container');
    if (!container) return;

    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;

    const title = type === 'success' ? 'موفقیت' :
                  type === 'warning' ? 'هشدار' :
                  type === 'danger' ? 'خطا' : 'اطلاعات';

    toast.innerHTML = `
        <div class="toast-header">
            <span class="toast-title">${title}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
        <div class="toast-message">${message}</div>
    `;

    container.appendChild(toast);

    // Show animation
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Auto remove after duration
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 300);
    }, duration);
}

// Show toast on page load if message exists in session
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a toast message from PHP session
    const toastMessage = document.querySelector('meta[name="toast-message"]');
    const toastType = document.querySelector('meta[name="toast-type"]');

    if (toastMessage && toastMessage.content) {
        showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
    }
});

// راه‌اندازی عمومی datepicker فقط روی .persian-datepicker
$(document).ready(function() {
    if ($.fn.persianDatepicker) {
        $('.persian-datepicker').each(function() {
            if (!$(this).data('has-datepicker')) {
                $(this).persianDatepicker({
                    format: 'YYYY/MM/DD',
                    autoClose: true,
                    initialValue: false
                });
                $(this).data('has-datepicker', true);
            }
        });
    }
});
</script>

</body>
</html>