/**
 * کلاس FileUploader برای مدیریت آپلود فایل‌ها
 */
class FileUploader {
    constructor(id, config = {}) {
        this.id = id;
        this.config = {
            maxSize: 2 * 1024 * 1024, // 2MB
            maxFiles: 10,
            allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'],
            allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
            ...config
        };
        
        this.selectedFiles = [];
        this.existingFiles = [];
        
        this.initElements();
        this.bindEvents();
        this.loadExistingFiles();
    }
    
    initElements() {
        this.container = document.getElementById(`${this.id}Container`);
        this.dropArea = document.getElementById(`${this.id}DropArea`);
        this.fileInput = document.getElementById(`${this.id}Input`);
        this.selectBtn = document.getElementById(`${this.id}SelectBtn`);
        this.preview = document.getElementById(`${this.id}Preview`);
        this.existingGrid = document.getElementById(`${this.id}ExistingFiles`);
    }
    
    bindEvents() {
        if (!this.container || !this.dropArea || !this.fileInput) {
            console.error('FileUploader: Required elements not found for', this.id);
            return;
        }

        // کلیک روی دکمه انتخاب
        if (this.selectBtn) {
            this.selectBtn.addEventListener('click', () => {
                this.fileInput.click();
            });
        }

        // کلیک روی منطقه drop
        this.dropArea.addEventListener('click', (e) => {
            if (e.target === this.dropArea || e.target.closest('.drop-area-content')) {
                this.fileInput.click();
            }
        });

        // تغییر فایل input
        this.fileInput.addEventListener('change', (e) => {
            this.handleFiles(Array.from(e.target.files));
            // نباید input را پاک کنیم تا فایل‌ها هنگام ارسال فرم موجود باشند
        });

        // Drag and Drop
        this.dropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.dropArea.classList.add('dragover');
        });

        this.dropArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            this.dropArea.classList.remove('dragover');
        });

        this.dropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.dropArea.classList.remove('dragover');
            this.handleFiles(Array.from(e.dataTransfer.files));
        });

        // حذف فایل‌ها
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('.btn-remove-file')) {
                this.removeFile(e.target.closest('.btn-remove-file'));
            }
        });

        // نمایش تصویر بزرگ
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('thumbnail-image')) {
                this.showImageModal(e.target.src);
            }
        });
    }
    
    loadExistingFiles() {
        if (this.existingGrid) {
            const existingItems = this.existingGrid.querySelectorAll('.file-item');
            existingItems.forEach(item => {
                const fileName = item.dataset.fileName;
                const filePath = item.querySelector('input[name="existing_files[]"]')?.value;
                if (fileName && filePath) {
                    this.existingFiles.push({
                        name: fileName,
                        path: filePath,
                        element: item
                    });
                }
            });
        }
    }

    handleFiles(files) {
        files.forEach(file => {
            if (this.validateFile(file)) {
                // بررسی تکراری نبودن
                if (!this.selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    this.selectedFiles.push(file);
                }
            }
        });
        
        this.renderPreview();
        this.updateFileInput();
    }
    
    validateFile(file) {
        // بررسی نوع فایل
        if (!this.config.allowedTypes.includes(file.type)) {
            const ext = file.name.split('.').pop().toLowerCase();
            if (!this.config.allowedExtensions.includes(ext)) {
                this.showError(`نوع فایل ${file.name} مجاز نیست.`);
                return false;
            }
        }
        
        // بررسی حجم فایل
        if (file.size > this.config.maxSize) {
            this.showError(`حجم فایل ${file.name} بیش از حد مجاز است.`);
            return false;
        }
        
        // بررسی تعداد فایل‌ها
        if (this.selectedFiles.length >= this.config.maxFiles) {
            this.showError(`حداکثر ${this.config.maxFiles} فایل مجاز است.`);
            return false;
        }
        
        return true;
    }
    
    renderPreview() {
        if (!this.preview) return;

        this.preview.innerHTML = '';

        this.selectedFiles.forEach((file, index) => {
            const fileItem = this.createFileItem(file, index, false);
            this.preview.appendChild(fileItem);
        });
    }

    createFileItem(file, index, isExisting) {
        const div = document.createElement('div');
        div.className = `file-item ${isExisting ? 'existing-file' : 'new-file'}`;
        div.dataset.fileIndex = index;

        const isImage = file.type?.startsWith('image/') || /\.(jpg|jpeg|png|gif)$/i.test(file.name);

        let thumbnailHtml = '';
        if (isImage) {
            if (isExisting) {
                thumbnailHtml = `<img src="${file.path || file.url}" alt="${file.name}" class="thumbnail-image">`;
            } else {
                // برای فایل‌های جدید، از FileReader استفاده می‌کنیم
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = div.querySelector('.thumbnail-image');
                    if (img) img.src = e.target.result;
                };
                reader.readAsDataURL(file);
                thumbnailHtml = `<img src="" alt="${file.name}" class="thumbnail-image">`;
            }
        } else {
            thumbnailHtml = `<div class="file-icon"><i class="fas fa-file-pdf"></i></div>`;
        }

        const fileName = file.name.length > 15 ? file.name.substring(0, 12) + '...' : file.name;
        const fileSize = this.formatFileSize(file.size);

        div.innerHTML = `
            <div class="file-thumbnail">
                ${thumbnailHtml}
            </div>
            <div class="file-info">
                <div class="file-name" title="${file.name}">${fileName}</div>
                <div class="file-size">${fileSize}</div>
            </div>
            <button type="button" class="btn-remove-file" title="حذف فایل">
                <i class="fas fa-times"></i>
            </button>
        `;

        return div;
    }

    removeFile(button) {
        const fileItem = button.closest('.file-item');

        if (fileItem.classList.contains('existing-file')) {
            // حذف فایل موجود - استفاده از تابع deleteAttachment اگر موجود است
            const fileId = fileItem.dataset.fileId;
            if (fileId && typeof deleteAttachment === 'function') {
                deleteAttachment(fileId);
            } else {
                // حذف از رابط کاربری فقط
                const fileName = fileItem.dataset.fileName;
                this.existingFiles = this.existingFiles.filter(f => f.name !== fileName);
                fileItem.remove();
            }
        } else {
            // حذف فایل جدید
            const index = parseInt(fileItem.dataset.fileIndex);
            this.selectedFiles.splice(index, 1);
            this.renderPreview();
            this.updateFileInput();
        }
    }

    updateFileInput() {
        if (!this.fileInput) return;

        const dt = new DataTransfer();
        this.selectedFiles.forEach(file => {
            dt.items.add(file);
        });
        this.fileInput.files = dt.files;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';

        const units = ['B', 'KB', 'MB', 'GB'];
        const factor = Math.floor(Math.log(bytes) / Math.log(1024));

        return `${(bytes / Math.pow(1024, factor)).toFixed(1)} ${units[factor]}`;
    }

    showError(message) {
        // نمایش پیام خطا - می‌توان از toast یا alert استفاده کرد
        if (typeof showToast === 'function') {
            showToast(message, 'error');
        } else {
            alert(message);
        }
    }

    showImageModal(src) {
        // نمایش تصویر در مودال
        let modal = document.getElementById('imageViewerModal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'imageViewerModal';
            modal.className = 'image-modal';
            modal.innerHTML = `
                <span class="image-modal-close">&times;</span>
                <img class="image-modal-content" id="modalImageView">
            `;
            document.body.appendChild(modal);

            // بستن مودال
            const closeModal = () => {
                modal.style.display = 'none';
                document.body.classList.remove('modal-open');
                document.body.style.top = '';
            };

            modal.querySelector('.image-modal-close').addEventListener('click', closeModal);

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // بستن با کلید Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && modal.style.display === 'block') {
                    closeModal();
                }
            });
        }

        // ذخیره موقعیت اسکرول فعلی
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        document.body.style.top = `-${scrollTop}px`;
        document.body.classList.add('modal-open');

        modal.querySelector('#modalImageView').src = src;
        modal.style.display = 'block';
    }

    // متدهای عمومی
    getSelectedFiles() {
        return this.selectedFiles;
    }

    getExistingFiles() {
        return this.existingFiles;
    }

    clearFiles() {
        this.selectedFiles = [];
        this.renderPreview();
        this.updateFileInput();
    }

    addFiles(files) {
        this.handleFiles(Array.isArray(files) ? files : [files]);
    }
}

// اطمینان از در دسترس بودن کلاس در سطح global
window.FileUploader = FileUploader;
