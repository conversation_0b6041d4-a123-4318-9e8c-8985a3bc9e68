<?php
require_once 'config/db.php';

try {
    // اضافه کردن مجوزها برای سازنده گزارش
    $reportBuilderPermissions = [
        ['name' => 'view', 'display_name' => 'مشاهده سازنده گزارش', 'description' => 'دسترسی به صفحه سازنده گزارش'],
        ['name' => 'create', 'display_name' => 'ایجاد گزارش', 'description' => 'ایجاد گزارش و قالب جدید'],
        ['name' => 'edit', 'display_name' => 'ویرایش گزارش', 'description' => 'ویرایش گزارش‌ها و قالب‌ها'],
        ['name' => 'delete', 'display_name' => 'حذف گزارش', 'description' => 'حذف گزارش‌ها و قالب‌ها']
    ];

    foreach ($reportBuilderPermissions as $perm) {
        $stmt = $pdo->prepare("
            INSERT INTO permissions (page_id, name, display_name, description)
            VALUES (10, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            display_name = VALUES(display_name),
            description = VALUES(description)
        ");
        $stmt->execute([$perm['name'], $perm['display_name'], $perm['description']]);
    }

    // اضافه کردن مجوزها برای مدیریت گزارش‌ها
    $reportsManagerPermissions = [
        ['name' => 'view', 'display_name' => 'مشاهده گزارش‌ها', 'description' => 'مشاهده لیست گزارش‌های ذخیره شده'],
        ['name' => 'manage', 'display_name' => 'مدیریت گزارش‌ها', 'description' => 'مدیریت و تنظیم گزارش‌ها در داشبورد']
    ];

    foreach ($reportsManagerPermissions as $perm) {
        $stmt = $pdo->prepare("
            INSERT INTO permissions (page_id, name, display_name, description)
            VALUES (11, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            display_name = VALUES(display_name),
            description = VALUES(description)
        ");
        $stmt->execute([$perm['name'], $perm['display_name'], $perm['description']]);
    }

    // تخصیص مجوزها به نقش admin
    $stmt = $pdo->prepare("
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r
        CROSS JOIN permissions p
        JOIN system_pages sp ON p.page_id = sp.id
        WHERE r.name = 'admin' AND sp.name IN ('report_builder', 'reports_manager')
        ON DUPLICATE KEY UPDATE created_at = VALUES(created_at)
    ");
    $stmt->execute();

    // تخصیص مجوزهای محدود به نقش user
    $stmt = $pdo->prepare("
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r
        JOIN permissions p ON p.name IN ('view', 'create')
        JOIN system_pages sp ON p.page_id = sp.id
        WHERE r.name = 'user' AND sp.name IN ('report_builder', 'reports_manager')
        ON DUPLICATE KEY UPDATE created_at = VALUES(created_at)
    ");
    $stmt->execute();

    echo "مجوزها با موفقیت اضافه شدند.\n";

} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
