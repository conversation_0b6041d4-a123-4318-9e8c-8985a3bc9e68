<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'dashboard', 'view')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'عدم دسترسی']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'متد نامعتبر']);
    exit;
}

$widgetId = $_POST['widget_id'] ?? null;

if (!$widgetId) {
    echo json_encode(['success' => false, 'message' => 'شناسه ویجت الزامی است']);
    exit;
}

try {
    // بررسی مالکیت ویجت
    $stmt = $pdo->prepare("
        SELECT id FROM dashboard_widgets 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$widgetId, $_SESSION['user_id']]);
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'ویجت یافت نشد یا شما مالک آن نیستید']);
        exit;
    }
    
    // حذف ویجت
    $stmt = $pdo->prepare("DELETE FROM dashboard_widgets WHERE id = ? AND user_id = ?");
    $stmt->execute([$widgetId, $_SESSION['user_id']]);
    
    echo json_encode([
        'success' => true,
        'message' => 'ویجت با موفقیت حذف شد'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطا در حذف ویجت: ' . $e->getMessage()
    ]);
}
?>
