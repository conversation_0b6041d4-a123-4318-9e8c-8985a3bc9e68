<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "اضافه کردن مجوزهای لازم...\n\n";
    
    // اضافه کردن مجوز view_history برای devices
    $page_stmt = $pdo->prepare("SELECT id FROM system_pages WHERE name = ?");
    $page_stmt->execute(['devices']);
    $devices_page_id = $page_stmt->fetchColumn();
    
    if ($devices_page_id) {
        // بررسی اینکه مجوز وجود دارد یا نه
        $check_stmt = $pdo->prepare("SELECT id FROM permissions WHERE page_id = ? AND name = ?");
        $check_stmt->execute([$devices_page_id, 'view_history']);
        
        if (!$check_stmt->fetchColumn()) {
            // اضافه کردن مجوز view_history
            $insert_stmt = $pdo->prepare("INSERT INTO permissions (page_id, name, display_name, description) VALUES (?, ?, ?, ?)");
            $insert_stmt->execute([
                $devices_page_id,
                'view_history',
                'مشاهده تاریخچه',
                'مجوز مشاهده سوابق تعمیرات دستگاه‌ها'
            ]);
            
            $permission_id = $pdo->lastInsertId();
            
            // تخصیص مجوز به نقش admin
            $admin_role_stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'admin'");
            $admin_role_stmt->execute();
            $admin_role_id = $admin_role_stmt->fetchColumn();
            
            if ($admin_role_id) {
                $role_perm_stmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                $role_perm_stmt->execute([$admin_role_id, $permission_id]);
                echo "✓ مجوز 'view_history' برای devices اضافه شد و به نقش admin تخصیص یافت\n";
            } else {
                echo "✓ مجوز 'view_history' برای devices اضافه شد اما نقش admin یافت نشد\n";
            }
        } else {
            echo "- مجوز 'view_history' برای devices از قبل موجود است\n";
        }
    } else {
        echo "- صفحه devices یافت نشد\n";
    }
    
    echo "\nعملیات با موفقیت انجام شد!\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
