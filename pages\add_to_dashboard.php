<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'dashboard', 'view')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'عدم دسترسی']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'متد نامعتبر']);
    exit;
}

$reportId = $_POST['report_id'] ?? null;
$widgetType = $_POST['widget_type'] ?? 'table';
$width = intval($_POST['width'] ?? 6);
$height = intval($_POST['height'] ?? 4);

if (!$reportId) {
    echo json_encode(['success' => false, 'message' => 'شناسه گزارش الزامی است']);
    exit;
}

// اعتبارسنجی مقادیر
$allowedWidgetTypes = ['table', 'chart', 'card', 'kpi'];
if (!in_array($widgetType, $allowedWidgetTypes)) {
    $widgetType = 'table';
}

$width = max(1, min(12, $width));
$height = max(1, min(12, $height));

try {
    // بررسی وجود گزارش
    $stmt = $pdo->prepare("
        SELECT id FROM saved_reports 
        WHERE id = ? AND (created_by = ? OR ? IN (SELECT id FROM users WHERE role = 'admin'))
    ");
    $stmt->execute([$reportId, $_SESSION['user_id'], $_SESSION['user_id']]);
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'گزارش یافت نشد']);
        exit;
    }
    
    // پیدا کردن موقعیت مناسب برای ویجت جدید
    $stmt = $pdo->prepare("
        SELECT MAX(position_y + height) as max_y 
        FROM dashboard_widgets 
        WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $positionY = ($result['max_y'] ?? 0);
    
    // افزودن ویجت به داشبورد
    $stmt = $pdo->prepare("
        INSERT INTO dashboard_widgets (user_id, report_id, widget_type, position_x, position_y, width, height) 
        VALUES (?, ?, ?, 0, ?, ?, ?)
    ");
    $stmt->execute([$_SESSION['user_id'], $reportId, $widgetType, $positionY, $width, $height]);
    
    echo json_encode([
        'success' => true,
        'message' => 'ویجت با موفقیت به داشبورد اضافه شد',
        'widget_id' => $pdo->lastInsertId()
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطا در افزودن به داشبورد: ' . $e->getMessage()
    ]);
}
?>
