<?php
// work_order_details.php

// تنظیم هدر برای اطمینان از خروجی صحیح JSON
header('Content-Type: application/json; charset=utf-8');

require_once '../includes/auth.php';
require_once '../db_connection.php';

// بررسی مجوز مشاهده دستور کار
require_page_access('work_order', 'view');

// اتصال به پایگاه داده
$db = db_connect();

// بررسی اولیه و معتبرسنجی شناسه ورودی
if (!isset($_GET['id']) || !filter_var($_GET['id'], FILTER_VALIDATE_INT)) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'شناسه دستور کار نامعتبر یا ارسال نشده است.']);
    exit;
}

$id = (int)$_GET['id'];

try {
    // 1. کوئری اصلی برای دریافت اطلاعات دستور کار
    $stmt = $db->prepare("
        SELECT
            wo.*,
            d.name AS device_name,
            u_req.name AS requester_name,
            act.activity_name AS scheduled_activity_name
        FROM work_orders wo
        LEFT JOIN devices d ON wo.device_id = d.id
        LEFT JOIN users u_req ON wo.requester_id = u_req.id
        LEFT JOIN activities act ON wo.scheduled_activity_id = act.id
        WHERE wo.id = :id
    ");
    $stmt->execute([':id' => $id]);
    $work_order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$work_order) {
        http_response_code(404); // Not Found
        echo json_encode(['success' => false, 'message' => 'دستور کاری با این شناسه یافت نشد.']);
        exit;
    }
    
    // 2. دریافت لیست مسئول(ان) اجرا
    $stmt_assignees = $db->prepare("
        SELECT u.name
        FROM work_order_assignees wa
        JOIN users u ON wa.user_id = u.id
        WHERE wa.work_order_id = :id
    ");
    $stmt_assignees->execute([':id' => $id]);
    $assignees = $stmt_assignees->fetchAll(PDO::FETCH_ASSOC);

    // ✅ مرحله جدید: واکشی مسیر فایل‌های پیوست از جدول work_order_attachments
    $stmt_attachments = $db->prepare("
        SELECT file_path FROM work_order_attachments WHERE work_order_id = :id
    ");
    $stmt_attachments->execute([':id' => $id]);
    // متد fetchAll با PDO::FETCH_COLUMN فقط ستون file_path را در یک آرایه ساده برمی‌گرداند
    $attachments = $stmt_attachments->fetchAll(PDO::FETCH_COLUMN, 0);


    // واکشی اطلاعات برون سپاری
    $stmt_outsourcing = $db->prepare("SELECT * FROM outsourcing_details WHERE work_order_id = :id");
    $stmt_outsourcing->execute([':id' => $id]);
    $outsourcing_details = $stmt_outsourcing->fetchAll(PDO::FETCH_ASSOC);

    // ✅ ارسال پاسخ موفقیت‌آمیز به همراه تمام داده‌ها (شامل پیوست‌ها و اطلاعات برون‌سپاری)
    echo json_encode([
        'success' => true,
        'work_order' => $work_order,
        'assignees' => $assignees,
        'attachments' => $attachments,
        'outsourcing_details' => $outsourcing_details // اطلاعات برون سپاری
    ]);

} catch (PDOException $e) {
    error_log('Database error in work_order_details.php: ' . $e->getMessage());
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'خطایی در سمت سرور رخ داده است.']);
}
?>