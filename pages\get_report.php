<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'report_builder', 'view')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'عدم دسترسی']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

$reportId = $_GET['id'] ?? null;

if (!$reportId) {
    echo json_encode(['success' => false, 'message' => 'شناسه گزارش الزامی است']);
    exit;
}

try {
    // دریافت اطلاعات گزارش
    $stmt = $pdo->prepare("
        SELECT sr.*, rt.config
        FROM saved_reports sr
        LEFT JOIN report_templates rt ON sr.template_id = rt.id
        WHERE sr.id = ? AND (sr.created_by = ? OR ? IN (SELECT id FROM users WHERE role = 'admin'))
    ");
    $stmt->execute([$reportId, $_SESSION['user_id'], $_SESSION['user_id']]);
    $report = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$report) {
        echo json_encode(['success' => false, 'message' => 'گزارش یافت نشد']);
        exit;
    }
    
    // تولید داده‌های گزارش
    require_once 'report_preview.php';
    
    $config = json_decode($report['config'], true);
    $generator = new ReportGenerator($pdo);
    $result = $generator->generateReport($config);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'data' => $result['data'],
            'config' => $config,
            'report_info' => $report
        ]);
    } else {
        echo json_encode($result);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطا در بارگذاری گزارش: ' . $e->getMessage()
    ]);
}
?>
