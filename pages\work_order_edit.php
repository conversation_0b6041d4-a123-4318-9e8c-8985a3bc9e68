<?php
// تنظیم منطقه زمانی
date_default_timezone_set('Asia/Tehran');

// فایل‌های ضروری و اتصال به دیتابیس
require_once '../includes/auth.php';
require_once '../db_connection.php';
$pdo = db_connect();
require_once '../includes/date_helper.php';

// بررسی مجوز ویرایش دستور کار
require_page_access('work_order', 'edit');

// مدیریت درخواست AJAX برای حذف فایل
if (isset($_POST['action']) && $_POST['action'] === 'delete_attachment') {
    header('Content-Type: application/json');

    try {
        $attachment_id = filter_input(INPUT_POST, 'attachment_id', FILTER_VALIDATE_INT);
        if (!$attachment_id) {
            throw new Exception('شناسه فایل نامعتبر است.');
        }

        // دریافت اطلاعات فایل
        $stmt = $pdo->prepare("SELECT file_path FROM work_order_attachments WHERE id = ?");
        $stmt->execute([$attachment_id]);
        $attachment = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$attachment) {
            throw new Exception('فایل یافت نشد.');
        }

        // حذف فایل از دیتابیس
        $stmt = $pdo->prepare("DELETE FROM work_order_attachments WHERE id = ?");
        $stmt->execute([$attachment_id]);

        // حذف فایل از سرور
        if (file_exists($attachment['file_path'])) {
            unlink($attachment['file_path']);
        }

        echo json_encode(['success' => true, 'message' => 'فایل با موفقیت حذف شد.']);
        exit;

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// پردازش فرم در صورت ارسال با متد POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $pdo->beginTransaction();
    try {
        // اعتبارسنجی ورودی‌ها
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) {
            throw new Exception('شناسه دستور کار نامعتبر است.');
        }

        $title = trim($_POST['title'] ?? '');
        $type = $_POST['type'] ?? '';
        $device_id = filter_input(INPUT_POST, 'device_id', FILTER_VALIDATE_INT);
        $scheduled_activity_id = !empty($_POST['scheduled_activity_id']) ? filter_input(INPUT_POST, 'scheduled_activity_id', FILTER_VALIDATE_INT) : null;
        $description = trim($_POST['description'] ?? '');
        $assignee_ids = $_POST['assignee_id'] ?? [];
        $priority = $_POST['priority'] ?? '';
        $status = $_POST['status'] ?? '';
        $line_stopped = isset($_POST['line_stopped']) ? 1 : 0;
        $stop_time = !empty($_POST['stop_time']) ? $_POST['stop_time'] : null;
        $due_date = !empty($_POST['due_date']) ? to_miladi($_POST['due_date']) : null;
        $stop_date = !empty($_POST['stop_date']) ? to_miladi($_POST['stop_date']) : null;
        $stop_datetime = ($line_stopped && $stop_date && $stop_time) ? "$stop_date $stop_time" : null;

        // اعتبارسنجی فیلدهای اجباری
        if (empty($title)) throw new Exception('عنوان دستور کار الزامی است.');
        if (!$device_id) throw new Exception('انتخاب دستگاه الزامی است.');
        if (empty($description)) throw new Exception('شرح فعالیت الزامی است.');
        if (empty($assignee_ids)) throw new Exception('انتخاب مسئول اجرا الزامی است.');

        // مدیریت آپلود فایل‌های چندگانه
        $uploaded_files = [];
        if (isset($_FILES['attachments']) && is_array($_FILES['attachments']['name']) && !empty($_FILES['attachments']['name'][0])) {
            $upload_dir = '../uploads/work_orders/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
            $max_file_size = 2 * 1024 * 1024; // 2MB

            for ($i = 0; $i < count($_FILES['attachments']['name']); $i++) {
                if ($_FILES['attachments']['error'][$i] === UPLOAD_ERR_OK) {
                    $file_name = $_FILES['attachments']['name'][$i];
                    $file_tmp = $_FILES['attachments']['tmp_name'][$i];
                    $file_size = $_FILES['attachments']['size'][$i];
                    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

                    // بررسی نوع فایل
                    if (!in_array($file_ext, $allowed_types)) {
                        throw new Exception("نوع فایل {$file_name} مجاز نیست.");
                    }

                    // بررسی اندازه فایل
                    if ($file_size > $max_file_size) {
                        throw new Exception("اندازه فایل {$file_name} بیش از حد مجاز است.");
                    }

                    $new_filename = 'wo_' . $id . '_' . uniqid() . '_' . time() . '.' . $file_ext;
                    $file_path = $upload_dir . $new_filename;

                    if (move_uploaded_file($file_tmp, $file_path)) {
                        $uploaded_files[] = $file_path;
                    }
                }
            }
        }

        // به‌روزرسانی دستور کار
        $stmt = $pdo->prepare("
            UPDATE work_orders SET
                title = :title, type = :type, device_id = :device_id,
                scheduled_activity_id = :scheduled_activity_id, description = :description,
                priority = :priority, due_date = :due_date, status = :status,
                line_stopped = :line_stopped, stop_datetime = :stop_datetime, updated_at = NOW()
            WHERE id = :id
        ");

        $stmt->execute([
            ':title' => $title, ':type' => $type, ':device_id' => $device_id,
            ':scheduled_activity_id' => $scheduled_activity_id, ':description' => $description,
            ':priority' => $priority, ':due_date' => $due_date, ':status' => $status,
            ':line_stopped' => $line_stopped, ':stop_datetime' => $stop_datetime, ':id' => $id
        ]);

        // به‌روزرسانی وضعیت گزارش خرابی مرتبط (اگر وجود دارد)
        $breakdown_stmt = $pdo->prepare("UPDATE breakdown_reports SET status = ? WHERE converted_to_wo_id = ?");
        $breakdown_stmt->execute([$status, $id]);

        // به‌روزرسانی مسئولین
        $pdo->prepare("DELETE FROM work_order_assignees WHERE work_order_id = ?")->execute([$id]);
        if (!empty($assignee_ids)) {
            $assignee_stmt = $pdo->prepare("INSERT INTO work_order_assignees (work_order_id, user_id) VALUES (?, ?)");
            foreach ($assignee_ids as $assignee_id) {
                if (filter_var($assignee_id, FILTER_VALIDATE_INT)) {
                    $assignee_stmt->execute([$id, $assignee_id]);
                }
            }
        }

        // ذخیره فایل‌های آپلود شده
        if (!empty($uploaded_files)) {
            $attachment_stmt = $pdo->prepare("INSERT INTO work_order_attachments (work_order_id, file_path) VALUES (?, ?)");
            foreach ($uploaded_files as $file_path) {
                $attachment_stmt->execute([$id, $file_path]);
            }
        }

        $pdo->commit();

        // ارسال پاسخ JSON برای نمایش پیام و سپس redirect
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'تغییرات با موفقیت ذخیره شد.',
            'redirect' => 'work_order.php'
        ]);
        exit;

    } catch (Exception $e) {
        $pdo->rollBack();
        // حذف فایل‌های آپلود شده در صورت خطا
        if (!empty($uploaded_files)) {
            foreach ($uploaded_files as $file_path) {
                if (file_exists($file_path)) {
                    unlink($file_path);
                }
            }
        }
        // ارسال پاسخ JSON برای خطا
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'خطا در به‌روزرسانی: ' . $e->getMessage()
        ]);
        exit;
    }
}

// خواندن اطلاعات برای نمایش در فرم (درخواست GET)
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['toast_message'] = 'شناسه دستور کار نامعتبر است.';
    $_SESSION['toast_type'] = 'danger';
    header('Location: work_order.php');
    exit;
}

$id = (int)$_GET['id'];

try {
    $stmt = $pdo->prepare("SELECT wo.*, u.name as requester_name FROM work_orders wo LEFT JOIN users u ON wo.requester_id = u.id WHERE wo.id = ?");
    $stmt->execute([$id]);
    $wo = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$wo) {
        $_SESSION['toast_message'] = 'دستور کاری با این شناسه یافت نشد.';
        $_SESSION['toast_type'] = 'danger';
        header('Location: work_order.php');
        exit;
    }

    require_once '../includes/user_helper.php';

    $devices = $pdo->query("SELECT d.id, d.name, d.serial_number, l.location_name FROM devices d LEFT JOIN locations l ON d.location = l.id ORDER BY d.name")->fetchAll(PDO::FETCH_ASSOC);
    $scheduled_activities = $pdo->query("SELECT id, activity_name, device_id FROM activities ORDER BY activity_name")->fetchAll(PDO::FETCH_ASSOC);
    $assigned_user_ids = $pdo->query("SELECT user_id FROM work_order_assignees WHERE work_order_id = $id")->fetchAll(PDO::FETCH_COLUMN);
    $users = getUsersForSelectWithSelected($pdo, $assigned_user_ids);

    // دریافت فایل‌های ضمیمه موجود
    $existing_attachments = $pdo->query("SELECT id, file_path FROM work_order_attachments WHERE work_order_id = $id")->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    $_SESSION['toast_message'] = 'خطا در بارگذاری اطلاعات: ' . $e->getMessage();
    $_SESSION['toast_type'] = 'danger';
    header('Location: work_order.php');
    exit;
}

// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}

include '../includes/header.php';
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ویرایش دستور کار</title>
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>
    <style>
        * {
            box-sizing: border-box;
        }

        .select2-container--default .select2-selection--single {
            height: 38px;
            padding: 5px 12px;
        }
        .select2-container--default .select2-selection--multiple {
            min-height: 38px;
        }

        .hidden-section {
            display: none;
        }

        .current-attachment {
            margin-top: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            margin: 5px 0;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .attachment-item img {
            max-width: 50px;
            max-height: 50px;
            object-fit: cover;
            border-radius: 3px;
        }

        .attachment-info {
            flex-grow: 1;
            margin: 0 10px;
        }

        .attachment-size {
            font-size: 0.9em;
            color: #666;
        }

        .btn-remove-attachment {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .btn-remove-attachment:hover {
            background-color: #c82333;
        }

        .file-upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background-color: #fafafa;
            transition: border-color 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: #007bff;
        }

        .file-upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }

        .container {
        max-width: 750px;
        margin: 20px auto;
        background: #fff;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0px 4px 29px 4px #dfd9d9;
        }

        @media (max-width: 768px) {
            .container {
            margin: 13px;
            }

            .row .col-md-4,
            .row .col-md-6 {
                margin-bottom: 15px;
            }

            .attachment-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .attachment-info {
                margin: 10px 0;
            }
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        .d-flex.justify-content-between.mt-4 {
         margin-top: 40px;
        }

        /* مودال نمایش تصویر */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: rgba(0,0,0,0.9);
            align-items: center;
            justify-content: center;
        }

        .image-modal-content {
            max-width: 90vw;
            max-height: 90vh;
            object-fit: contain;
        }

        .image-modal-close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            transition: 0.3s;
            cursor: pointer;
        }

        .image-modal-close:hover,
        .image-modal-close:focus {
            color: #bbb;
            text-decoration: none;
        }

        /* مدیریت اسکرول هنگام باز بودن مودال */
        body.modal-open {
            position: fixed;
            width: 100%;
            overflow: hidden;
        }

        /* بهبود نمایش تصاویر کوچک */
        .attachment-item img {
            max-width: 60px;
            max-height: 60px;
            object-fit: cover;
            border-radius: 5px;
            border: 2px solid #ddd;
            transition: border-color 0.3s ease;
        }

        .attachment-item img:hover {
            border-color: #007bff;
        }

    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3>ویرایش دستور کار: <?= htmlspecialchars($wo['workorder_id']) ?></h3>
        </div>

        <form id="edit-wo-form" method="post" enctype="multipart/form-data">
            <input type="hidden" name="id" value="<?= $wo['id'] ?>">

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">ایجاد کننده:</label>
                    <input type="text" value="<?= htmlspecialchars($wo['requester_name']) ?>" class="form-control" readonly />
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">تاریخ ایجاد:</label>
                    <input type="text" value="<?= to_shamsi($wo['request_date']) ?>" class="form-control" readonly />
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">وضعیت:</label>
                    <select name="status" class="form-select" required>
                        <option value="پیش‌نویس" <?= ($wo['status'] == 'پیش‌نویس') ? 'selected' : '' ?>>پیش‌نویس</option>
                        <option value="دستورکار صادر شد" <?= ($wo['status'] == 'دستورکار صادر شد') ? 'selected' : '' ?>>دستورکار صادر شد</option>
                        <option value="در حال انجام" <?= ($wo['status'] == 'در حال انجام') ? 'selected' : '' ?>>در حال انجام</option>
                        <option value="پایان تعمیر" <?= ($wo['status'] == 'پایان تعمیر') ? 'selected' : '' ?>>پایان تعمیر</option>
                        <option value="منتظر تایید" <?= ($wo['status'] == 'منتظر تایید') ? 'selected' : '' ?>>منتظر تایید</option>
                        <option value="انجام و تایید شده" <?= ($wo['status'] == 'انجام و تایید شده') ? 'selected' : '' ?>>انجام و تایید شده</option>
                        <option value="تایید نهایی و بسته شد" <?= ($wo['status'] == 'تایید نهایی و بسته شد') ? 'selected' : '' ?>>تایید نهایی و بسته شد</option>
                        <option value="برگشت جهت اصلاح" <?= ($wo['status'] == 'برگشت جهت اصلاح') ? 'selected' : '' ?>>برگشت جهت اصلاح</option>
                        <option value="برون سپاری شد" <?= ($wo['status'] == 'برون سپاری شد') ? 'selected' : '' ?>>برون سپاری شد</option>
                        <option value="پایان برون سپاری" <?= ($wo['status'] == 'پایان برون سپاری') ? 'selected' : '' ?>>پایان برون سپاری</option>
                        <option value="لغو شده" <?= ($wo['status'] == 'لغو شده') ? 'selected' : '' ?>>لغو شده</option>
                    </select>
                </div>
            </div>
			
            <div class="mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">اطلاعات اصلی</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">عنوان دستور کار:</label>
                        <input type="text" name="title" class="form-control" value="<?= htmlspecialchars($wo['title']) ?>" required />
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع دستور کار:</label>
                            <select name="type" class="form-select" required>
                                <option value="پیشگیرانه" <?= ($wo['type'] == 'پیشگیرانه') ? 'selected' : '' ?>>پیشگیرانه</option>
                                <option value="اصلاحی" <?= ($wo['type'] == 'اصلاحی') ? 'selected' : '' ?>>اصلاحی</option>
                                <option value="اضطراری" <?= ($wo['type'] == 'اضطراری') ? 'selected' : '' ?>>اضطراری</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اولویت:</label>
                            <select name="priority" class="form-select" required>
                                <option value="متوسط" <?= ($wo['priority'] == 'متوسط') ? 'selected' : '' ?>>متوسط</option>
                                <option value="پایین" <?= ($wo['priority'] == 'پایین') ? 'selected' : '' ?>>پایین</option>
                                <option value="بالا" <?= ($wo['priority'] == 'بالا') ? 'selected' : '' ?>>بالا</option>
                                <option value="بحرانی" <?= ($wo['priority'] == 'بحرانی') ? 'selected' : '' ?>>بحرانی</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">دستگاه مربوطه:</label>
                        <select name="device_id" id="device-select" class="form-select" required>
                            <option value="">انتخاب دستگاه...</option>
                            <?php foreach ($devices as $device): ?>
                                <option value="<?= $device['id'] ?>" <?= ($device['id'] == $wo['device_id']) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($device['name']) ?>
                                    <?php if (!empty($device['location_name'])): ?>
                                        | <?= htmlspecialchars($device['location_name']) ?>
                                    <?php endif; ?>
                                    <?php if (!empty($device['serial_number'])): ?>
                                        | <?= htmlspecialchars($device['serial_number']) ?>
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">فعالیت برنامه‌ریزی‌شده:</label>
                        <select name="scheduled_activity_id" id="activity-select" class="form-select">
                            <option value="">-- بدون ارتباط --</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">شرح فعالیت / مشکل:</label>
                        <textarea name="description" rows="4" class="form-control" required><?= htmlspecialchars($wo['description']) ?></textarea>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">مسئولین و زمان‌بندی</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">مسئول اجرا:</label>
                        <select name="assignee_id[]" class="form-select" multiple required>
                             <?php foreach ($users as $user): ?>
                                <option value="<?= $user['id'] ?>" <?= in_array($user['id'], $assigned_user_ids) ? 'selected' : '' ?>><?= htmlspecialchars($user['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <small class="form-text text-muted">می‌توانید چند نفر را انتخاب کنید</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">تاریخ سررسید:</label>
                        <input type="text" id="due-date" name="due_date" class="form-control persian-datepicker" value="<?= $wo['due_date'] ? to_shamsi($wo['due_date']) : '' ?>" autocomplete="off" />
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" name="line_stopped" id="line-stopped" class="form-check-input" <?= ($wo['line_stopped'] == 1) ? 'checked' : '' ?> />
                        <label for="line-stopped" class="form-check-label">خط متوقف شده است</label>
                    </div>

                    <div id="stop-time-section" class="mb-3 <?= ($wo['line_stopped'] != 1) ? 'hidden-section' : '' ?>">
                        <label class="form-label">تاریخ و زمان توقف خط:</label>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" id="stop-date" name="stop_date" class="form-control persian-datepicker" value="<?= $wo['stop_datetime'] ? to_shamsi(substr($wo['stop_datetime'], 0, 10)) : '' ?>" autocomplete="off" placeholder="تاریخ" />
                            </div>
                            <div class="col-md-6">
                                <div class="time-input-container">
                                    <input type="text" id="stop_minute" placeholder="دقیقه" maxlength="2" class="form-control" value="<?= $wo['stop_datetime'] ? substr($wo['stop_datetime'], 14, 2) : '' ?>">
                                    <span class="time-colon">:</span>
                                    <input type="text" id="stop_hour" placeholder="ساعت" maxlength="2" class="form-control" value="<?= $wo['stop_datetime'] ? substr($wo['stop_datetime'], 11, 2) : '' ?>">

                                    <input type="hidden" id="stop_time" name="stop_time">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">فایل‌های ضمیمه</h5>
                </div>
                <div class="card-body">


                    <div class="mb-3">
                        <?php
                        require_once '../includes/file_uploader.php';

                        // تبدیل فایل‌های موجود به فرمت مورد نیاز
                        $existing_files = [];
                        foreach ($existing_attachments as $attachment) {
                            $existing_files[] = [
                                'id' => $attachment['id'],
                                'path' => $attachment['file_path'],
                                'name' => basename($attachment['file_path']),
                                'size' => file_exists($attachment['file_path']) ? filesize($attachment['file_path']) : 0
                            ];
                        }

                        echo render_file_uploader([
                            'id' => 'workOrderEditAttachments',
                            'name' => 'attachments[]',
                            'accept' => 'image/*,.pdf',
                            'max_size' => 2,
                            'label' => 'فایل‌های ضمیمه',
                            'description' => 'فایل‌های عکس و PDF - حداکثر 2 مگابایت',
                            'existing_files' => $existing_files,
                            'show_existing' => true
                        ]);
                        ?>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <button type="submit" id="save-changes-btn" class="btn btn-primary">
                    <span id="button-text">ذخیره تغییرات</span>
                </button>
                <a href="work_order.php" class="btn btn-secondary">انصراف</a>
            </div>
        </form>
    </div>

    <!-- مودال نمایش تصویر -->
    <div id="imageViewerModal" class="image-modal">
        <span class="image-modal-close">&times;</span>
        <img class="image-modal-content" id="modalImageView">
    </div>

<?php include '../includes/footer.php'; ?>

<script>
    const all_activities = <?= json_encode($scheduled_activities, JSON_UNESCAPED_UNICODE) ?>;


    $(document).ready(function() {
        // راه‌اندازی datepicker
        if ($.fn.persianDatepicker) {
            $('.persian-datepicker').persianDatepicker({
                format: 'YYYY/MM/DD',
                autoClose: true,
                initialValue: false
            });
        }

        // مدیریت فعالیت‌های مرتبط با دستگاه
        function filterActivitiesByDevice(deviceId) {
            let $activitySelect = $('#activity-select');
            const currentActivity = "<?= $wo['scheduled_activity_id'] ?? '' ?>";

            $activitySelect.empty().append(new Option('-- بدون ارتباط --', ''));

            if (deviceId) {
                let filtered = all_activities.filter(act => act.device_id == deviceId);
                filtered.forEach(act => {
                    let isSelected = (act.id == currentActivity);
                    let option = new Option(act.activity_name, act.id, isSelected, isSelected);
                    $activitySelect.append(option);
                });

                $activitySelect.prop('disabled', filtered.length === 0);
                if (filtered.length > 0 && $activitySelect.hasClass('select2-hidden-accessible')) {
                    $activitySelect.trigger('change.select2');
                }
            } else {
                $activitySelect.prop('disabled', true);
            }
        }

        $('#device-select').on('change', function() {
            const deviceId = $(this).val();
            filterActivitiesByDevice(deviceId);
        });

        // مدیریت نمایش بخش توقف خط
        $('#line-stopped').change(function() {
            $('#stop-time-section').toggleClass('hidden-section', !this.checked);
        });

        // مدیریت ورودی زمان
        $('#stop_hour, #stop_minute').on('input', function() {
            const hour = $('#stop_hour').val().padStart(2, '0');
            const minute = $('#stop_minute').val().padStart(2, '0');
            if (hour && minute) {
                $('#stop_time').val(hour + ':' + minute);
            }
        });

        // راه‌اندازی اولیه
        const initialDeviceId = $('#device-select').val();
        if (initialDeviceId) {
            filterActivitiesByDevice(initialDeviceId);
        }

        // فایل‌ها از طریق کامپوننت file uploader مدیریت می‌شوند
    });

    // تابع حذف فایل ضمیمه
    function deleteAttachment(attachmentId) {
        if (!confirm('آیا از حذف این فایل مطمئن هستید؟')) {
            return;
        }

        const formData = new FormData();
        formData.append('action', 'delete_attachment');
        formData.append('attachment_id', attachmentId);

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // حذف المنت از DOM
                const attachmentElement = document.querySelector(`[data-id="${attachmentId}"]`);
                if (attachmentElement) {
                    attachmentElement.remove();
                }
                showToast(data.message, 'success');
            } else {
                showToast(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('خطا در حذف فایل', 'danger');
        });
    }

    // تابع reset کردن دکمه submit
    function resetSubmitButton() {
        const submitButton = document.getElementById('save-changes-btn');
        const buttonText = document.getElementById('button-text');
        const form = document.getElementById('edit-wo-form');

        if (submitButton && buttonText && form) {
            submitButton.disabled = false;
            buttonText.innerHTML = 'ذخیره تغییرات';
            form.classList.remove('loading');
        }
    }

    // مدیریت ارسال فرم با AJAX
    document.getElementById('edit-wo-form').addEventListener('submit', function(event) {
        event.preventDefault(); // جلوگیری از ارسال معمولی فرم

        const form = event.target;
        const submitButton = document.getElementById('save-changes-btn');
        const buttonText = document.getElementById('button-text');

        // بررسی validation
        if (!form.checkValidity()) {
            showToast('لطفاً تمام فیلدهای اجباری را پر کنید.', 'warning');
            return;
        }

        // نمایش loading با spinner
        submitButton.disabled = true;
        buttonText.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>در حال ذخیره...';
        form.classList.add('loading');

        // ارسال فرم با AJAX
        const formData = new FormData(form);

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // نمایش پیام موفقیت
                showToast(data.message, 'success');

                // redirect بعد از 2 ثانیه
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            } else {
                // نمایش پیام خطا و reset دکمه
                showToast(data.message, 'danger');
                resetSubmitButton();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('خطا در ارسال اطلاعات', 'danger');
            resetSubmitButton();
        });
    });

    // راه‌اندازی اولیه صفحه
    document.addEventListener('DOMContentLoaded', function() {
        // جلوگیری از نمایش دوباره پیام در footer
        window.skipFooterToast = true;

        // reset کردن دکمه در صورت بارگذاری مجدد صفحه
        resetSubmitButton();

        // نمایش پیام‌های session در صورت وجود (برای خطاهای سرور)
        const toastMessage = document.querySelector('meta[name="toast-message"]');
        const toastType = document.querySelector('meta[name="toast-type"]');

        if (toastMessage && toastMessage.content) {
            showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
        }
    });

    // متغیر global برای ذخیره موقعیت اسکرول
    let savedScrollPosition = 0;

    // مدیریت مودال تصویر (مشابه work_order.php)
    function openImageViewer(src) {
        const modal = document.getElementById('imageViewerModal');
        const modalImg = document.getElementById('modalImageView');

        // ذخیره موقعیت اسکرول فعلی
        savedScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        document.body.style.top = `-${savedScrollPosition}px`;
        document.body.classList.add('modal-open');

        modalImg.src = src;
        modal.style.display = 'flex';
    }

    function closeImageViewer() {
        const modal = document.getElementById('imageViewerModal');
        modal.style.display = 'none';

        // بازگرداندن اسکرول صفحه
        document.body.classList.remove('modal-open');
        document.body.style.top = '';
        window.scrollTo(0, savedScrollPosition);
    }

    // Event listeners برای مودال تصویر
    $(document).ready(function() {
        const imageModal = $('#imageViewerModal');

        // کلیک روی تصاویر کوچک
        $(document).on('click', '.thumbnail-image', function() {
            openImageViewer($(this).attr('src'));
        });

        // بستن مودال
        imageModal.find('.image-modal-close').on('click', closeImageViewer);
        imageModal.on('click', function(e) {
            if (e.target === this) closeImageViewer();
        });

        // بستن با کلید Escape
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && imageModal.is(':visible')) {
                closeImageViewer();
            }
        });
    });
</script>
</body>
</html>