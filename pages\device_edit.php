<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';
include '../includes/header.php';

// بررسی مجوز ویرایش دستگاه
require_page_access('devices', 'edit');

$pdo = db_connect();

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo "شناسه دستگاه معتبر نیست.";
    exit;
}

$id = (int)$_GET['id'];

// پردازش فرم به‌روزرسانی
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $stmt = $pdo->prepare("SELECT images FROM devices WHERE id = ?");
    $stmt->execute([$id]);
    $current_device = $stmt->fetch(PDO::FETCH_ASSOC);
    $current_images = !empty($current_device['images']) ? json_decode($current_device['images'], true) : [];

    // دریافت فایل‌های موجود که باید نگه داشته شوند
    $existing_files = $_POST['existing_files'] ?? [];
    $existing_images = [];

    // استخراج نام فایل از مسیر کامل
    foreach ($existing_files as $file_path) {
        if (strpos($file_path, '../uploads/devices/') === 0) {
            $existing_images[] = str_replace('../uploads/devices/', '', $file_path);
        }
    }

    // حذف تصاویری که در فرم نگه داشته نشده‌اند
    $images_to_delete = array_diff($current_images, $existing_images);
    foreach ($images_to_delete as $img) {
        if (file_exists("../uploads/devices/" . $img)) {
            unlink("../uploads/devices/" . $img);
        }
    }

    // آپلود تصاویر جدید
    $new_uploaded_files = [];
    if (isset($_FILES['new_images']) && !empty($_FILES['new_images']['name'][0])) {
        $targetDir = "../uploads/devices/";
        if (!is_dir($targetDir)) mkdir($targetDir, 0755, true);

        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        $max_file_size = 2 * 1024 * 1024; // 2MB

        foreach ($_FILES['new_images']['name'] as $key => $name) {
            if ($_FILES['new_images']['error'][$key] === UPLOAD_ERR_OK) {
                $file_ext = strtolower(pathinfo($name, PATHINFO_EXTENSION));
                $file_size = $_FILES['new_images']['size'][$key];

                // بررسی نوع و حجم فایل
                if (in_array($file_ext, $allowed_types) && $file_size <= $max_file_size) {
                    $filename = uniqid() . "_" . basename($name);
                    if (move_uploaded_file($_FILES['new_images']['tmp_name'][$key], $targetDir . $filename)) {
                        $new_uploaded_files[] = $filename;
                    }
                }
            }
        }
    }

    // ایجاد لیست نهایی تصاویر و تبدیل به JSON
    $final_images_list = array_merge($existing_images, $new_uploaded_files);
    $images_json = !empty($final_images_list) ? json_encode($final_images_list) : null;

    // تبدیل تاریخ شمسی به میلادی
    $purchase_date = !empty($_POST['purchase_date']) ? to_miladi($_POST['purchase_date']) : null;

    // به‌روزرسانی اطلاعات دستگاه
    $stmt = $pdo->prepare("UPDATE devices SET 
        name = ?, serial_number = ?, description = ?, status = ?, 
        purchase_date = ?, vendor_name = ?, vendor_phone = ?, 
        images = ?, updated_at = NOW()
        WHERE id = ?");
    $stmt->execute([
        $_POST['name'], $_POST['serial_number'], $_POST['description'], $_POST['status'],
        $purchase_date, $_POST['vendor_name'], $_POST['vendor_phone'],
        $images_json, $id
    ]);

    // حذف و درج مجدد قطعات
    $pdo->prepare("DELETE FROM device_parts WHERE device_id = ?")->execute([$id]);
    if (!empty($_POST['parts_name']) && is_array($_POST['parts_name'])) {
        $insertPartStmt = $pdo->prepare("INSERT INTO device_parts (device_id, part_name, quantity, unit, description) VALUES (?, ?, ?, ?, ?)");
        foreach ($_POST['parts_name'] as $index => $partName) {
            if (trim($partName) !== '') {
                $quantity = (int)($_POST['parts_quantity'][$index] ?? 1);
                $unit = trim($_POST['parts_unit'][$index] ?? 'عدد');
                $desc = $_POST['parts_description'][$index] ?? null;
                $insertPartStmt->execute([$id, trim($partName), $quantity, $unit, $desc]);
            }
        }
    }

    $_SESSION['toast_message'] = 'دستگاه با موفقیت ویرایش شد.';
    $_SESSION['toast_type'] = 'success';
    header("Location: devices.php");
    exit;
}

// دریافت اطلاعات فعلی دستگاه برای نمایش در فرم
$stmt = $pdo->prepare("SELECT * FROM devices WHERE id = ?");
$stmt->execute([$id]);
$device = $stmt->fetch();

if (!$device) {
    echo "دستگاه یافت نشد.";
    exit;
}

$device['purchase_date'] = !empty($device['purchase_date']) ? to_shamsi($device['purchase_date'], 'Y/m/d') : '';
$device_images = !empty($device['images']) ? json_decode($device['images'], true) : [];
// اطمینان از اینکه $device_images یک آرایه است
if (!is_array($device_images)) {
    $device_images = [];
}

$partStmt = $pdo->prepare("SELECT part_name, quantity, unit, description FROM device_parts WHERE device_id = ?");
$partStmt->execute([$id]);
$parts = $partStmt->fetchAll(PDO::FETCH_ASSOC);

// دریافت پیام از session
$toast_message = null;
$toast_type = null;

if (isset($_SESSION['toast_message'])) {
    $toast_message = $_SESSION['toast_message'];
    $toast_type = $_SESSION['toast_type'] ?? 'info';
    // حذف پیام از session تا فقط یک بار نمایش داده شود
    unset($_SESSION['toast_message']);
    unset($_SESSION['toast_type']);
}
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ویرایش دستگاه</title>
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>
    <style>
        /* استایل‌های مشابه صفحه اصلی برای مدیریت تصاویر */
        #imagePreviewContainer { display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px; padding: 10px; border: 1px dashed #ccc; border-radius: 5px; min-height: 80px; }
        .image-preview { position: relative; width: 100px; height: 100px; }
        .image-preview img { width: 100%; height: 100%; object-fit: cover; border-radius: 5px; }
        .delete-image-btn { position: absolute; top: -8px; right: -8px; background-color: #ff4d4d; color: white; border: 2px solid white; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-weight: bold; display: flex; align-items: center; justify-content: center; padding: 0; font-size: 14px; line-height: 1; }
    </style>
</head>
<body>
<div class="device_edit_div">
    <h3>ویرایش دستگاه</h3>
    <form method="post" enctype="multipart/form-data" id="editDeviceForm">
        <div class="form-grid">
            <div><label>نام دستگاه:</label><input type="text" name="name" required value="<?= htmlspecialchars($device['name']) ?>"></div>
            <div><label>شماره سریال:</label><input type="text" name="serial_number" value="<?= htmlspecialchars($device['serial_number']) ?>"></div>
            <div class="full-width"><label>توضیحات:</label><textarea name="description"><?= htmlspecialchars($device['description']) ?></textarea></div>
            <div><label>وضعیت:</label><select name="status" required><option value="فعال" <?= $device['status'] === 'فعال' ? 'selected' : '' ?>>فعال</option><option value="غیرفعال" <?= $device['status'] === 'غیرفعال' ? 'selected' : '' ?>>غیرفعال</option><option value="در حال تعمیر" <?= $device['status'] === 'در حال تعمیر' ? 'selected' : '' ?>>در حال تعمیر</option></select></div>
            <div><label>تاریخ خرید:</label><input type="text" class="persian-datepicker" name="purchase_date" value="<?= htmlspecialchars($device['purchase_date']) ?>"></div>
            <div><label>فروشنده:</label><input type="text" name="vendor_name" value="<?= htmlspecialchars($device['vendor_name']) ?>"></div>
            <div><label>شماره تماس فروشنده:</label><input type="tel" name="vendor_phone" value="<?= htmlspecialchars($device['vendor_phone']) ?>"></div>

            <div class="full-width">
                <?php
                require_once '../includes/file_uploader.php';

                // تبدیل آرایه تصاویر به فرمت مورد نیاز کامپوننت
                $existing_files = [];
                foreach ($device_images as $img) {
                    $existing_files[] = [
                        'path' => "../uploads/devices/" . $img,
                        'name' => $img,
                        'size' => file_exists("../uploads/devices/" . $img) ? filesize("../uploads/devices/" . $img) : 0
                    ];
                }

                echo render_file_uploader([
                    'id' => 'deviceImages',
                    'name' => 'new_images[]',
                    'accept' => 'image/*',
                    'max_size' => 2,
                    'label' => 'تصاویر دستگاه',
                    'description' => 'فایل‌های عکس - حداکثر 2 مگابایت',
                    'existing_files' => $existing_files,
                    'show_existing' => true
                ]);
                ?>
            </div>

            <div class="full-width">
                <label>قطعات مصرفی:</label>
                <div id="partsContainer">
                    <?php foreach ($parts as $part): ?>
                        <div class="part-row">
                            <input type="text" name="parts_name[]" value="<?= htmlspecialchars($part['part_name']) ?>" placeholder="نام قطعه" required>
                            <input type="number" name="parts_quantity[]" value="<?= (int)$part['quantity'] ?>" placeholder="تعداد" min="1" required>
                            <input type="text" name="parts_unit[]" value="<?= htmlspecialchars($part['unit'] ?? 'عدد') ?>" placeholder="واحد شمارش" required>
                            <input type="text" name="parts_description[]" value="<?= htmlspecialchars($part['description']) ?>" placeholder="توضیحات">
                            <button type="button" onclick="removePartRow(this)">− حذف</button>
                        </div>
                    <?php endforeach; ?>
                </div>
                <button type="button" onclick="addPartRow()">+ ردیف جدید</button>
            </div>
            <div class="full-width"><button type="submit">ذخیره تغییرات</button><button class="cancel-btn" type="button" onclick="window.location.href='devices.php'">لغو</button></div>
        </div>
    </form>
</div>
<?php include '../includes/footer.php'; ?>
<script>
// مدیریت فرم با کامپوننت جدید آپلود فایل

// فرم به صورت معمولی ارسال می‌شود
// فایل‌ها از طریق کامپوننت file uploader مدیریت می‌شوند


// توابع مربوط به قطعات (بدون تغییر)
function addPartRow() {
    const container = document.getElementById('partsContainer');
    const div = document.createElement('div');
    div.className = 'part-row';
    div.innerHTML = `<input type="text" name="parts_name[]" placeholder="نام قطعه" required><input type="number" name="parts_quantity[]" min="1" placeholder="تعداد" value="1" required><input type="text" name="parts_unit[]" placeholder="واحد شمارش" value="عدد" required><input type="text" name="parts_description[]" placeholder="توضیحات (اختیاری)"><button type="button" onclick="removePartRow(this)">− حذف</button>`;
    container.appendChild(div);
}
function removePartRow(button) {
    button.closest('.part-row').remove();
}
</script>
</body>
</html>
