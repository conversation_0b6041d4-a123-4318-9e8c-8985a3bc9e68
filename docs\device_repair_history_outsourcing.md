# اضافه کردن اطلاعات برون‌سپاری به سوابق تعمیرات دستگاه

## تغییرات انجام شده

### 1. اصلاح کوئری PHP

#### قبل:
```sql
SELECT
    wo.id, wo.title, wo.status,
    exec.actual_start_datetime,
    exec.actual_end_datetime,
    exec.completion_notes,
    exec.delay_reason,
    u_verifier.name as verifier_name,
    (SELECT GROUP_CONCAT(u.name) FROM work_order_labor wol JOIN users u ON wol.user_id=u.id WHERE wol.work_order_id = wo.id) as labor_users,
    (SELECT GROUP_CONCAT(wop.part_name, ' (', wop.quantity_used, ' ', wop.unit, ')') FROM work_order_parts wop WHERE wop.work_order_id = wo.id) as parts_used
FROM work_orders wo
LEFT JOIN work_order_execution exec ON wo.id = exec.work_order_id
LEFT JOIN users u_verifier ON wo.verifier_id = u_verifier.id
WHERE wo.device_id = ?
ORDER BY exec.actual_end_datetime DESC LIMIT 100
```

#### بعد:
```sql
SELECT
    wo.id, wo.title, wo.status,
    exec.actual_start_datetime,
    exec.actual_end_datetime,
    exec.completion_notes,
    exec.delay_reason,
    exec.exit_date,
    exec.exit_where,
    exec.exit_desc,
    exec.back_date,
    exec.back_desc,
    exec.back_pay,
    u_verifier.name as verifier_name,
    (SELECT GROUP_CONCAT(u.name) FROM work_order_labor wol JOIN users u ON wol.user_id=u.id WHERE wol.work_order_id = wo.id) as labor_users,
    (SELECT GROUP_CONCAT(wop.part_name, ' (', wop.quantity_used, ' ', wop.unit, ')') FROM work_order_parts wop WHERE wop.work_order_id = wo.id) as parts_used
FROM work_orders wo
LEFT JOIN work_order_execution exec ON wo.id = exec.work_order_id
LEFT JOIN users u_verifier ON wo.verifier_id = u_verifier.id
WHERE wo.device_id = ?
ORDER BY exec.actual_end_datetime DESC LIMIT 100
```

### 2. اضافه کردن تبدیل تاریخ‌های برون‌سپاری

```php
foreach ($history as &$task) {
    $task['actual_start_datetime_shamsi'] = format_shamsi_datetime($task['actual_start_datetime']);
    $task['actual_end_datetime_shamsi'] = format_shamsi_datetime($task['actual_end_datetime']);
    $task['exit_date_shamsi'] = $task['exit_date'] ? to_shamsi($task['exit_date']) : null;
    $task['back_date_shamsi'] = $task['back_date'] ? to_shamsi($task['back_date']) : null;
}
```

### 3. اصلاح JavaScript برای نمایش اطلاعات برون‌سپاری

#### بررسی وجود اطلاعات برون‌سپاری:
```javascript
// بررسی وجود اطلاعات برون‌سپاری
const hasOutsourcing = task.exit_date || task.back_date;
let outsourcingSection = '';

if (hasOutsourcing) {
    outsourcingSection = '<hr class="history-card-divider"><div class="outsourcing-section">';
    outsourcingSection += '<h5 style="color: #007bff; margin-bottom: 10px;"><i class="fas fa-external-link-alt"></i> اطلاعات برون‌سپاری</h5>';
    
    // اطلاعات خروج
    if (task.exit_date) {
        outsourcingSection += `<p><strong>تاریخ خروج:</strong> ${task.exit_date_shamsi || task.exit_date}</p>`;
        if (task.exit_where) {
            outsourcingSection += `<p><strong>مقصد:</strong> ${task.exit_where}</p>`;
        }
        if (task.exit_desc) {
            outsourcingSection += `<p><strong>توضیحات خروج:</strong><br>${task.exit_desc.replace(/\n/g, '<br>')}</p>`;
        }
    }
    
    // اطلاعات بازگشت
    if (task.back_date) {
        outsourcingSection += `<p><strong>تاریخ بازگشت:</strong> ${task.back_date_shamsi || task.back_date}</p>`;
        if (task.back_desc) {
            outsourcingSection += `<p><strong>توضیحات بازگشت:</strong><br>${task.back_desc.replace(/\n/g, '<br>')}</p>`;
        }
        if (task.back_pay) {
            // تبدیل اعداد انگلیسی به فارسی
            const persianAmount = task.back_pay.toString().replace(/[0-9]/g, function(w) {
                return ['۰','۱','۲','۳','۴','۵','۶','۷','۸','۹'][w];
            });
            outsourcingSection += `<p><strong>مبلغ فاکتور:</strong> ${persianAmount} تومان</p>`;
        }
    } else if (task.exit_date) {
        outsourcingSection += '<p><strong>وضعیت:</strong> <span style="color: #ffc107;">هنوز بازگشت داده نشده است</span></p>';
    }
    
    outsourcingSection += '</div>';
}
```

### 4. اصلاح HTML کارت‌ها

#### اضافه کردن نشانگر برون‌سپاری در هدر:
```javascript
<div class="history-card-title-line">
    <strong>${task.title}</strong>
    <span class="status-badge ${statusClass}">${task.status}</span>
    ${hasOutsourcing ? '<i class="fas fa-external-link-alt" style="color: #007bff; margin-right: 5px;" title="شامل برون‌سپاری"></i>' : ''}
</div>
```

#### اضافه کردن بخش برون‌سپاری به محتوای کارت:
```javascript
<div class="history-card-details">
    <p><strong>شرح اقدامات:</strong><br>${notes}</p>
    ${delayReason}
    <p><strong>نفرات:</strong> ${labor}</p>
    <p><strong>قطعات مصرفی:</strong><br>${parts}</p>
    <p><strong>تحویل به:</strong> ${verifier}</p>
</div>
${outsourcingSection}
```

### 5. CSS برای بخش برون‌سپاری

```css
/* استایل‌های بخش برون‌سپاری */
#device-history-modal .outsourcing-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid #007bff;
}

#device-history-modal .outsourcing-section h5 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    font-weight: 600;
}

#device-history-modal .outsourcing-section p {
    margin: 5px 0;
    font-size: 0.9rem;
}

#device-history-modal .outsourcing-section p strong {
    color: #495057;
}
```

## ویژگی‌های پیاده‌سازی شده

### 1. نشانگر بصری
- آیکون برون‌سپاری در هدر کارت برای تعمیراتی که شامل برون‌سپاری هستند
- رنگ آبی برای تمایز بخش برون‌سپاری

### 2. اطلاعات کامل برون‌سپاری
- **تاریخ خروج**: با تاریخ شمسی
- **مقصد**: محل برون‌سپاری
- **توضیحات خروج**: شرح هنگام ارسال
- **تاریخ بازگشت**: با تاریخ شمسی
- **توضیحات بازگشت**: شرح هنگام دریافت
- **مبلغ فاکتور**: با ارقام فارسی

### 3. مدیریت حالت‌های مختلف
- نمایش "هنوز بازگشت داده نشده است" برای موارد خروج بدون بازگشت
- مدیریت فیلدهای خالی با نمایش مناسب
- پشتیبانی از متن‌های چندخطی

### 4. طراحی کاربرپسند
- بخش برون‌سپاری در قاب جداگانه با پس‌زمینه متفاوت
- آیکون و رنگ‌بندی مناسب برای تشخیص آسان
- ساختار منظم و خوانا

## نتیجه

### قبل از تغییر:
- ❌ عدم نمایش اطلاعات برون‌سپاری در سوابق تعمیرات
- ❌ اطلاعات ناقص برای تعمیرات برون‌سپاری شده

### بعد از تغییر:
- ✅ نمایش کامل اطلاعات برون‌سپاری
- ✅ نشانگر بصری برای تعمیرات شامل برون‌سپاری
- ✅ اطلاعات تاریخ، مقصد، توضیحات و مبلغ
- ✅ مدیریت حالت‌های مختلف برون‌سپاری
- ✅ طراحی یکپارچه و حرفه‌ای
- ✅ تجربه کاربری بهبود یافته
