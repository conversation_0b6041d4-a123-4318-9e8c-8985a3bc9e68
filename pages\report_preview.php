<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'report_builder', 'view')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'عدم دسترسی']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'متد نامعتبر']);
    exit;
}

$config = json_decode($_POST['config'] ?? '{}', true);

if (!$config || !isset($config['tables']) || !isset($config['fields'])) {
    echo json_encode(['success' => false, 'message' => 'تنظیمات نامعتبر']);
    exit;
}

class ReportGenerator {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function generateReport($config) {
        try {
            $sql = $this->buildQuery($config);
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'data' => $data,
                'query' => $sql // برای دیباگ
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطا در تولید گزارش: ' . $e->getMessage()
            ];
        }
    }
    
    private function buildQuery($config) {
        $tables = $config['tables'];
        $fields = $config['fields'];
        $joins = $config['joins'] ?? [];
        $filters = $config['filters'] ?? [];
        $groupBy = $config['group_by'] ?? [];
        $orderBy = $config['order_by'] ?? [];
        
        // ساخت بخش SELECT
        $selectFields = [];
        foreach ($fields as $field) {
            if (strpos($field, '(') !== false) {
                // فیلد تجمیعی مثل COUNT(*)
                $selectFields[] = $field;
            } else {
                // فیلد عادی
                $selectFields[] = $this->sanitizeField($field);
            }
        }
        
        $sql = "SELECT " . implode(', ', $selectFields);
        
        // ساخت بخش FROM
        $mainTable = $tables[0];
        $sql .= " FROM " . $this->sanitizeTableName($mainTable);
        
        // ساخت بخش JOIN
        foreach ($joins as $join) {
            $table1 = $this->sanitizeTableName($join['table1']);
            $field1 = $this->sanitizeField($join['field1']);
            $table2 = $this->sanitizeTableName($join['table2']);
            $field2 = $this->sanitizeField($join['field2']);
            
            $sql .= " LEFT JOIN {$table2} ON {$table1}.{$field1} = {$table2}.{$field2}";
        }
        
        // ساخت بخش WHERE
        if (!empty($filters)) {
            $whereConditions = [];
            foreach ($filters as $filter) {
                $field = $this->sanitizeField($filter['field']);
                $operator = $this->sanitizeOperator($filter['operator']);
                $value = $filter['value'];
                
                if ($operator === 'LIKE') {
                    $whereConditions[] = "{$field} LIKE '%{$value}%'";
                } else {
                    $whereConditions[] = "{$field} {$operator} '{$value}'";
                }
            }
            
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }
        }
        
        // ساخت بخش GROUP BY
        if (!empty($groupBy)) {
            $groupFields = array_map([$this, 'sanitizeField'], $groupBy);
            $sql .= " GROUP BY " . implode(', ', $groupFields);
        }
        
        // ساخت بخش ORDER BY
        if (!empty($orderBy)) {
            $orderFields = [];
            foreach ($orderBy as $order) {
                $field = $this->sanitizeField($order['field']);
                $direction = strtoupper($order['direction']) === 'DESC' ? 'DESC' : 'ASC';
                $orderFields[] = "{$field} {$direction}";
            }
            $sql .= " ORDER BY " . implode(', ', $orderFields);
        }
        
        // محدود کردن تعداد نتایج برای پیش‌نمایش
        $sql .= " LIMIT 100";
        
        return $sql;
    }
    
    private function sanitizeTableName($tableName) {
        // لیست جداول مجاز
        $allowedTables = [
            'devices', 'work_orders', 'breakdown_reports', 'activities', 
            'users', 'categories', 'locations', 'work_order_assignees',
            'work_order_execution', 'work_order_parts', 'work_order_labor'
        ];
        
        if (!in_array($tableName, $allowedTables)) {
            throw new Exception("جدول غیرمجاز: {$tableName}");
        }
        
        return "`{$tableName}`";
    }
    
    private function sanitizeField($field) {
        // حذف کاراکترهای خطرناک
        $field = preg_replace('/[^a-zA-Z0-9_\.\(\)\*\s,]/', '', $field);
        
        // اگر فیلد شامل نقطه است (table.field)
        if (strpos($field, '.') !== false) {
            $parts = explode('.', $field);
            if (count($parts) === 2) {
                return "`{$parts[0]}`.`{$parts[1]}`";
            }
        }
        
        // اگر فیلد تجمیعی است
        if (preg_match('/^(COUNT|SUM|AVG|MIN|MAX)\s*\(/i', $field)) {
            return $field;
        }
        
        return "`{$field}`";
    }
    
    private function sanitizeOperator($operator) {
        $allowedOperators = ['=', '!=', '<>', '<', '>', '<=', '>=', 'LIKE', 'NOT LIKE', 'IN', 'NOT IN'];
        
        if (!in_array(strtoupper($operator), $allowedOperators)) {
            throw new Exception("عملگر غیرمجاز: {$operator}");
        }
        
        return strtoupper($operator);
    }
}

// تولید گزارش
$generator = new ReportGenerator($pdo);
$result = $generator->generateReport($config);

echo json_encode($result, JSON_UNESCAPED_UNICODE);
?>
