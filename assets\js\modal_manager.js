/**
 * مدیریت عمومی مودال‌ها
 * شامل جلوگیری از اسکرول صفحه پشت و مدیریت کلید Escape
 */

class ModalManager {
    constructor() {
        this.openModals = [];
        this.bindGlobalEvents();
    }
    
    /**
     * باز کردن مودال
     * @param {string|HTMLElement} modal - سلکتور یا عنصر مودال
     */
    openModal(modal) {
        const modalElement = typeof modal === 'string' ? document.querySelector(modal) : modal;
        if (!modalElement) return;
        
        // ذخیره موقعیت اسکرول فعلی
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        document.body.style.top = `-${scrollTop}px`;
        document.body.classList.add('modal-open');
        
        // نمایش مودال
        modalElement.style.display = 'block';
        
        // اضافه کردن به لیست مودال‌های باز
        this.openModals.push({
            element: modalElement,
            scrollTop: scrollTop
        });
    }
    
    /**
     * بستن مودال
     * @param {string|HTMLElement} modal - سلکتور یا عنصر مودال
     */
    closeModal(modal) {
        const modalElement = typeof modal === 'string' ? document.querySelector(modal) : modal;
        if (!modalElement) return;
        
        // پنهان کردن مودال
        modalElement.style.display = 'none';
        
        // حذف از لیست مودال‌های باز
        const modalIndex = this.openModals.findIndex(m => m.element === modalElement);
        if (modalIndex > -1) {
            const modalData = this.openModals[modalIndex];
            this.openModals.splice(modalIndex, 1);
            
            // اگر هیچ مودال دیگری باز نیست، اسکرول را بازگردان
            if (this.openModals.length === 0) {
                document.body.classList.remove('modal-open');
                document.body.style.top = '';
                window.scrollTo(0, modalData.scrollTop);
            }
        }
    }
    
    /**
     * بستن آخرین مودال باز شده
     */
    closeLastModal() {
        if (this.openModals.length > 0) {
            const lastModal = this.openModals[this.openModals.length - 1];
            this.closeModal(lastModal.element);
        }
    }
    
    /**
     * بستن تمام مودال‌ها
     */
    closeAllModals() {
        while (this.openModals.length > 0) {
            this.closeLastModal();
        }
    }
    
    /**
     * اتصال رویدادهای عمومی
     */
    bindGlobalEvents() {
        // بستن مودال با کلید Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.openModals.length > 0) {
                this.closeLastModal();
            }
        });
        
        // بستن مودال با کلیک خارج از آن
        document.addEventListener('click', (e) => {
            if (this.openModals.length > 0) {
                const lastModal = this.openModals[this.openModals.length - 1];
                if (e.target === lastModal.element) {
                    this.closeModal(lastModal.element);
                }
            }
        });
    }
    
    /**
     * بررسی اینکه آیا مودالی باز است
     */
    hasOpenModal() {
        return this.openModals.length > 0;
    }
    
    /**
     * دریافت تعداد مودال‌های باز
     */
    getOpenModalCount() {
        return this.openModals.length;
    }
}

// ایجاد instance عمومی
window.modalManager = new ModalManager();

// توابع کمکی برای سازگاری با کد موجود
window.openModal = function(modal) {
    window.modalManager.openModal(modal);
};

window.closeModal = function(modal) {
    window.modalManager.closeModal(modal);
};

// Export برای استفاده در ماژول‌ها
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModalManager;
}
