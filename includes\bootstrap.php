<?php
// includes/bootstrap.php

// 1. تنظیم منطقه زمانی
date_default_timezone_set('Asia/Tehran');

// 2. فعال‌سازی تبدیل خودکار تاریخ‌ها
ob_start(function($buffer) {
    // شناسایی تاریخ‌های میلادی و تبدیل به شمسی
    $patterns = [
        '/\b(\d{4})-(\d{2})-(\d{2})\b/' => 'Y-m-d',
        '/\b(\d{2})\/(\d{2})\/(\d{4})\b/' => 'm/d/Y'
    ];
    
    foreach ($patterns as $pattern => $format) {
        $buffer = preg_replace_callback($pattern, function($matches) use ($format) {
            try {
                $date = new DateTime($matches[0]);
                require_once __DIR__ . '/../vendor/autoload.php';
                $jDate = \Jalali\Jalalian::fromDateTime($date);
                return $jDate->format($format);
            } catch (Exception $e) {
                return $matches[0];
            }
        }, $buffer);
    }
    
    return $buffer;
});

// 3. تابع کمکی برای تبدیل تاریخ‌ها
function toJalali($date, $format = 'Y-m-d') {
    if (!$date) return null;
    require_once __DIR__ . '/../vendor/autoload.php';
    return \Jalali\Jalalian::fromDateTime($date)->format($format);
}

// 4. تابع تبدیل تاریخ شمسی به میلادی
function toGregorian($jalaliDate) {
    if (!$jalaliDate) return null;
    require_once __DIR__ . '/../vendor/autoload.php';
    return \Jalali\Jalalian::fromFormat('Y-m-d', $jalaliDate)->toCarbon();
}