<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== اضافه کردن صفحه تایید گزارش تعمیر به منو ===\n\n";
    
    // اضافه کردن صفحه به system_pages
    $insert_page = $pdo->prepare("
        INSERT INTO system_pages (name, display_name, file_path, icon, is_active)
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            display_name = VALUES(display_name),
            file_path = VALUES(file_path),
            icon = VALUES(icon)
    ");

    $insert_page->execute([
        'repair_approvals',
        'تایید گزارش تعمیر',
        'repair_approvals.php',
        'fas fa-check-circle',
        1
    ]);
    
    echo "✅ صفحه repair_approvals به جدول system_pages اضافه شد\n";
    
    // دریافت ID صفحه
    $page_id = $pdo->query("SELECT id FROM system_pages WHERE name = 'repair_approvals'")->fetchColumn();
    
    // اضافه کردن مجوزهای پایه
    $permissions = [
        ['view', 'مشاهده'],
        ['approve', 'تایید'],
        ['reject', 'رد']
    ];

    foreach ($permissions as $perm) {
        $insert_perm = $pdo->prepare("
            INSERT INTO permissions (page_id, name, display_name)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
                display_name = VALUES(display_name)
        ");

        $insert_perm->execute([$page_id, $perm[0], $perm[1]]);
        echo "✅ مجوز '{$perm[0]}' ({$perm[1]}) اضافه شد\n";
    }
    
    // اضافه کردن مجوزها به نقش admin
    $admin_role_id = $pdo->query("SELECT id FROM roles WHERE name = 'admin'")->fetchColumn();
    
    if ($admin_role_id) {
        $perm_ids = $pdo->prepare("SELECT id FROM permissions WHERE page_id = ?");
        $perm_ids->execute([$page_id]);
        
        while ($perm_id = $perm_ids->fetchColumn()) {
            $check_existing = $pdo->prepare("SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission_id = ?");
            $check_existing->execute([$admin_role_id, $perm_id]);
            
            if ($check_existing->fetchColumn() == 0) {
                $insert_role_perm = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                $insert_role_perm->execute([$admin_role_id, $perm_id]);
            }
        }
        echo "✅ مجوزها به نقش admin اضافه شد\n";
    }
    
    // اضافه کردن مجوز view به نقش user (اگر نیاز باشد)
    $user_role_id = $pdo->query("SELECT id FROM roles WHERE name = 'user'")->fetchColumn();
    
    if ($user_role_id) {
        $view_perm_id = $pdo->prepare("SELECT id FROM permissions WHERE page_id = ? AND name = 'view'");
        $view_perm_id->execute([$page_id]);
        $view_perm_id = $view_perm_id->fetchColumn();
        
        if ($view_perm_id) {
            $check_user_perm = $pdo->prepare("SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission_id = ?");
            $check_user_perm->execute([$user_role_id, $view_perm_id]);
            
            if ($check_user_perm->fetchColumn() == 0) {
                $insert_user_perm = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                $insert_user_perm->execute([$user_role_id, $view_perm_id]);
                echo "✅ مجوز view به نقش user اضافه شد\n";
            }
        }
    }
    
    echo "\n=== نتیجه نهایی ===\n";
    echo "صفحه 'تایید گزارش تعمیر' با موفقیت به منو اضافه شد\n";
    echo "مجوزهای تعریف شده:\n";
    echo "- view: مشاهده صفحه\n";
    echo "- approve: تایید گزارش‌ها\n";
    echo "- reject: رد گزارش‌ها\n";
    echo "\nنقش‌های دارای دسترسی:\n";
    echo "- admin: همه مجوزها\n";
    echo "- user: فقط مشاهده\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
