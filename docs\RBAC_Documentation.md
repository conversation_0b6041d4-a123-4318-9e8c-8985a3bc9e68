# مستندات سیستم RBAC - Net Dombaz

## مقدمه

سیستم کنترل دسترسی مبتنی بر نقش (RBAC) برای پروژه Net Dombaz طراحی شده است تا امنیت و مدیریت کاربران را بهبود بخشد. این سیستم از معماری دو مرحله‌ای استفاده می‌کند: ابتدا دسترسی به صفحه و سپس مجوزهای عملیاتی.

## ویژگی‌های کلیدی

- **کنترل دسترسی دو مرحله‌ای**: ابتدا بررسی دسترسی به صفحه، سپس مجوزهای عملیاتی
- **مدیریت کاربران جامع**: رابط کاربری تب‌دار برای مدیریت کاربران، نقش‌ها و مجوزها
- **منوی دینامیک**: تولید خودکار منو بر اساس مجوزهای کاربر
- **معماری مقیاس‌پذیر**: امکان افزودن صفحات جدید بدون تغییر کد
- **امنیت بالا**: رمزگذاری bcrypt، بررسی وضعیت فعال بودن کاربر
- **سازگاری با سیستم قدیمی**: حفظ عملکرد کدهای موجود

## ساختار دیتابیس

### جداول اصلی

#### 1. جدول `roles` (نقش‌ها)
```sql
- id: شناسه یکتا
- name: نام نقش (انگلیسی)
- display_name: نام نمایشی (فارسی)
- description: توضیحات نقش
- is_active: وضعیت فعال/غیرفعال
- created_at: تاریخ ایجاد
```

#### 2. جدول `system_pages` (صفحات سیستم)
```sql
- id: شناسه یکتا
- name: نام صفحه (انگلیسی)
- display_name: نام نمایشی (فارسی)
- file_path: مسیر فایل
- icon: آیکون صفحه
- is_active: وضعیت فعال/غیرفعال
- created_at: تاریخ ایجاد
```

#### 3. جدول `permissions` (مجوزها)
```sql
- id: شناسه یکتا
- page_id: شناسه صفحه (Foreign Key)
- name: نام مجوز (view, create, edit, delete, etc.)
- display_name: نام نمایشی
- description: توضیحات
- created_at: تاریخ ایجاد
```

#### 4. جدول `role_permissions` (مجوزهای نقش)
```sql
- id: شناسه یکتا
- role_id: شناسه نقش (Foreign Key)
- permission_id: شناسه مجوز (Foreign Key)
- created_at: تاریخ ایجاد
```

#### 5. جدول `users` (به‌روزرسانی شده)
```sql
- role_id: شناسه نقش (Foreign Key) - جدید
- is_active: وضعیت فعال/غیرفعال - جدید
- role: نقش قدیمی (برای سازگاری) - موجود
```

## کلاس PermissionSystem

### متدهای اصلی

#### `has_permission($page_name, $permission_name, $user_id = null)`
بررسی داشتن مجوز خاص برای کاربر

```php
// مثال استفاده
if ($permission_system->has_permission('devices', 'create')) {
    // کاربر مجوز ایجاد دستگاه دارد
}
```

#### `require_page_access($page_name, $permission_name = 'view')`
اجباری کردن دسترسی به صفحه (در صورت عدم دسترسی، کاربر هدایت می‌شود)

```php
// در ابتدای هر صفحه
require_page_access('devices', 'view');
```

#### `get_accessible_pages($user_id = null)`
دریافت لیست صفحات قابل دسترس برای کاربر

```php
$pages = get_accessible_pages();
// برای تولید منوی دینامیک
```

#### `get_role_permissions($role_id)`
دریافت تمام مجوزهای یک نقش

#### `get_active_roles()` و `get_active_pages()`
دریافت نقش‌ها و صفحات فعال

## نحوه استفاده

### 1. بررسی دسترسی در صفحات

```php
<?php
require_once '../includes/auth.php';

// بررسی دسترسی به صفحه
require_page_access('devices', 'view');

// بررسی مجوز برای نمایش دکمه‌ها
if (has_permission('devices', 'create')): ?>
    <button>افزودن دستگاه</button>
<?php endif; ?>

// بررسی مجوز در عملیات POST
if ($_POST['action'] === 'create_device') {
    require_page_access('devices', 'create');
    // انجام عملیات
}
?>
```

### 2. مدیریت کاربران

صفحه `user_management.php` شامل سه تب اصلی:

- **مدیریت کاربران**: ایجاد، ویرایش، حذف کاربران
- **مدیریت نقش‌ها**: ایجاد و ویرایش نقش‌ها
- **تنظیم مجوزها**: اختصاص مجوزها به نقش‌ها

### 3. نقش‌های پیش‌فرض

#### Admin (مدیر سیستم)
- دسترسی کامل به تمام بخش‌ها
- مدیریت کاربران و نقش‌ها
- تنظیم مجوزها

#### User (کاربر عادی)
- دسترسی به عملیات پایه
- مشاهده و ویرایش محدود
- عدم دسترسی به مدیریت کاربران

#### Outsource (پیمانکار)
- دسترسی محدود
- فقط مشاهده اطلاعات مربوطه

## نصب و راه‌اندازی

### 1. اجرای اسکریپت دیتابیس
```bash
# اجرای فایل database.sql در MySQL
mysql -u username -p database_name < database.sql
```

### 2. اجرای مایگریشن
```bash
# دسترسی به فایل migration/migrate_to_rbac.php از مرورگر
http://your-domain/net_dombaz/migration/migrate_to_rbac.php
```

### 3. تست سیستم
```bash
# اجرای تست‌ها
http://your-domain/net_dombaz/tests/rbac_test.php
```

## امنیت

### نکات امنیتی مهم

1. **رمزگذاری**: استفاده از `password_hash()` و `password_verify()`
2. **بررسی وضعیت**: کنترل `is_active` برای کاربران
3. **اعتبارسنجی ورودی**: Prepared Statements برای جلوگیری از SQL Injection
4. **کنترل دسترسی**: بررسی مجوزها در هر درخواست
5. **Session Security**: مدیریت امن session ها

### توصیه‌های امنیتی

- فایل‌های تست و مایگریشن را پس از استفاده حذف کنید
- رمزهای عبور قوی استفاده کنید
- دسترسی‌ها را به حداقل ضروری محدود کنید
- لاگ‌های سیستم را مرتب بررسی کنید

## عیب‌یابی

### مشکلات رایج

#### 1. خطای "Permission denied"
- بررسی وجود مجوز در جدول `role_permissions`
- کنترل فعال بودن کاربر (`is_active = 1`)
- بررسی صحت `role_id` کاربر

#### 2. منوی دینامیک نمایش داده نمی‌شود
- بررسی وجود صفحات در جدول `system_pages`
- کنترل مجوز `view` برای صفحات
- بررسی مسیر فایل‌ها

#### 3. مشکل در ورود کاربران
- بررسی تکمیل مایگریشن
- کنترل `role_id` کاربران
- بررسی فعال بودن نقش‌ها

### لاگ‌ها و دیباگ

برای دیباگ، می‌توانید از کدهای زیر استفاده کنید:

```php
// نمایش اطلاعات کاربر جاری
var_dump($_SESSION['user']);

// بررسی مجوزهای کاربر
$permissions = $permission_system->get_user_permissions($_SESSION['user']['id']);
var_dump($permissions);

// بررسی صفحات قابل دسترس
$pages = get_accessible_pages();
var_dump($pages);
```

## توسعه و سفارشی‌سازی

### افزودن صفحه جدید

1. **اضافه کردن به دیتابیس**:
```sql
INSERT INTO system_pages (name, display_name, file_path, icon, is_active) 
VALUES ('new_page', 'صفحه جدید', 'new_page.php', 'fas fa-new', 1);
```

2. **ایجاد مجوزها**:
```sql
INSERT INTO permissions (page_id, name, display_name) 
VALUES 
(LAST_INSERT_ID(), 'view', 'مشاهده'),
(LAST_INSERT_ID(), 'create', 'ایجاد'),
(LAST_INSERT_ID(), 'edit', 'ویرایش'),
(LAST_INSERT_ID(), 'delete', 'حذف');
```

3. **اعمال در کد**:
```php
<?php
require_once '../includes/auth.php';
require_page_access('new_page', 'view');
?>
```

### افزودن نقش جدید

از صفحه مدیریت کاربران یا مستقیماً در دیتابیس:

```sql
INSERT INTO roles (name, display_name, description, is_active) 
VALUES ('manager', 'مدیر بخش', 'مدیریت بخش خاص', 1);
```

## پشتیبانی و به‌روزرسانی

### نسخه‌گیری

قبل از هر تغییر، از دیتابیس نسخه‌گیری کنید:

```bash
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql
```

### به‌روزرسانی

برای به‌روزرسانی سیستم:

1. نسخه‌گیری از دیتابیس
2. اعمال تغییرات جدید
3. اجرای تست‌ها
4. بررسی عملکرد

## نتیجه‌گیری

سیستم RBAC Net Dombaz یک راه‌حل جامع و امن برای مدیریت دسترسی کاربران است. با معماری مقیاس‌پذیر و رابط کاربری ساده، امکان مدیریت آسان و امن کاربران را فراهم می‌کند.

برای سوالات بیشتر یا گزارش مشکلات، با تیم توسعه تماس بگیرید.
