<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'report_builder', 'delete')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'عدم دسترسی']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'متد نامعتبر']);
    exit;
}

$templateId = $_POST['id'] ?? null;

if (!$templateId) {
    echo json_encode(['success' => false, 'message' => 'شناسه قالب الزامی است']);
    exit;
}

try {
    $pdo->beginTransaction();
    
    // بررسی مالکیت قالب و اینکه پیش‌فرض نباشد
    $stmt = $pdo->prepare("
        SELECT id, is_default FROM report_templates 
        WHERE id = ? AND (created_by = ? OR ? IN (SELECT id FROM users WHERE role = 'admin'))
    ");
    $stmt->execute([$templateId, $_SESSION['user_id'], $_SESSION['user_id']]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        throw new Exception('قالب یافت نشد یا شما مجوز حذف آن را ندارید');
    }
    
    if ($template['is_default']) {
        throw new Exception('قالب‌های پیش‌فرض قابل حذف نیستند');
    }
    
    // حذف گزارش‌های مرتبط
    $stmt = $pdo->prepare("SELECT id FROM saved_reports WHERE template_id = ?");
    $stmt->execute([$templateId]);
    $reports = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($reports)) {
        // حذف ویجت‌های مرتبط
        $placeholders = str_repeat('?,', count($reports) - 1) . '?';
        $stmt = $pdo->prepare("DELETE FROM dashboard_widgets WHERE report_id IN ($placeholders)");
        $stmt->execute($reports);
        
        // حذف گزارش‌ها
        $stmt = $pdo->prepare("DELETE FROM saved_reports WHERE template_id = ?");
        $stmt->execute([$templateId]);
    }
    
    // حذف قالب
    $stmt = $pdo->prepare("DELETE FROM report_templates WHERE id = ?");
    $stmt->execute([$templateId]);
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'قالب و گزارش‌های مرتبط با موفقیت حذف شدند'
    ]);
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo json_encode([
        'success' => false,
        'message' => 'خطا در حذف قالب: ' . $e->getMessage()
    ]);
}
?>
