# مدیریت کاربران و مخفی کردن کاربر "برون سپاری"

## مسئله

در نرم‌افزار یک کاربر پیش‌فرض به نام "برون سپاری" تعریف شده است که برای شناسایی تعمیرات برون‌سپاری شده استفاده می‌شود. این کاربر نباید در فیلدهای انتخاب کاربر به کاربران عادی نمایش داده شود.

## راه‌حل

### 1. فایل Helper

فایل `includes/user_helper.php` ایجاد شده که شامل توابع زیر است:

#### `getUsersForSelect($pdo, $include_outsource = false, $order_by = 'name')`
- دریافت لیست کاربران برای نمایش در فیلدهای select
- به طور پیش‌فرض کاربر "برون سپاری" را حذف می‌کند
- پارامتر `$include_outsource` برای شامل کردن کاربر برون سپاری

#### `getUsersForSelectWithSelected($pdo, $selected_user_ids = null, $order_by = 'name')`
- دریافت کاربران با در نظر گیری کاربران انتخاب شده
- اگر کاربر برون سپاری از قبل انتخاب شده باشد، آن را شامل می‌کند

#### `isOutsourceUser($user_id)`
- بررسی اینکه آیا یک کاربر، کاربر برون سپاری است (ID = 99999)

#### `getUserName($pdo, $user_id)`
- دریافت نام کاربر با مدیریت کاربر برون سپاری

#### `getOutsourceUser($pdo)`
- دریافت اطلاعات کاربر برون سپاری

#### `canSeeOutsourceUser()`
- بررسی اینکه آیا کاربر فعلی مجاز به دیدن کاربر برون سپاری است

### 2. فایل‌های به‌روزرسانی شده

فایل‌های زیر برای استفاده از helper به‌روزرسانی شده‌اند:

- `pages/work_order.php`
- `pages/work_order_edit.php`
- `pages/action_report.php`
- `pages/my_tasks.php`
- `pages/users.php`

### 3. نحوه استفاده

#### برای فیلدهای انتخاب عادی:
```php
require_once '../includes/user_helper.php';
$users = getUsersForSelect($pdo);
```

#### برای فیلدهای ویرایش (با کاربران انتخاب شده):
```php
require_once '../includes/user_helper.php';
$selected_user_ids = [1, 2, 99999]; // شامل کاربر برون سپاری
$users = getUsersForSelectWithSelected($pdo, $selected_user_ids);
```

#### برای مدیران (شامل کاربر برون سپاری):
```php
require_once '../includes/user_helper.php';
$users = getUsersForSelect($pdo, true); // include_outsource = true
```

## مزایا

1. **مدیریت متمرکز**: تمام منطق مربوط به مخفی کردن کاربر برون سپاری در یک فایل
2. **سازگاری با کد موجود**: تغییرات کمینه در فایل‌های موجود
3. **انعطاف‌پذیری**: امکان شامل کردن کاربر برون سپاری در موارد خاص
4. **قابلیت نگهداری**: آسان برای به‌روزرسانی و تغییر

## نکات مهم

- کاربر "برون سپاری" دارای ID = 99999 است
- در صفحه مدیریت کاربران، این کاربر به عنوان "کاربر سیستمی" مشخص می‌شود
- اگر کاربر برون سپاری از قبل انتخاب شده باشد، همچنان نمایش داده می‌شود
- فقط مدیران سیستم مجاز به دیدن و مدیریت کاربر برون سپاری هستند
