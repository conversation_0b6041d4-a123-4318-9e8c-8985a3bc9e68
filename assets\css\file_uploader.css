/* کامپوننت یکپارچه آپلود فایل */

.file-uploader-container {
    margin-bottom: 1rem;
    width: 100%;
    box-sizing: border-box;
}

.file-uploader-label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

/* بخش فایل‌های موجود */
.existing-files-section {
    margin-bottom: 1rem;
}

.existing-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

/* منطقه کشیدن و رها کردن */
.file-drop-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background-color: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 1rem;
}

.file-drop-area:hover {
    border-color: #007bff;
    background-color: #f0f8ff;
}

.file-drop-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
    transform: scale(1.02);
}

.drop-area-content {
    pointer-events: none;
}

.drop-icon {
    font-size: 2rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.drop-text {
    margin: 0.5rem 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.btn-select-files {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
    pointer-events: all;
}

.btn-select-files:hover {
    background-color: #0056b3;
}

/* پیش‌نمایش فایل‌های جدید */
.new-files-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

/* آیتم فایل */
.file-item {
    position: relative;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.file-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.file-item.existing-file {
    border-color: #28a745;
}

.file-item.new-file {
    border-color: #17a2b8;
}

/* تصویر بندانگشتی */
.file-thumbnail {
    width: 100%;
    height: 80px;
    margin-bottom: 0.5rem;
    border-radius: 4px;
    overflow: hidden;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.file-icon {
    font-size: 2rem;
    color: #dc3545;
}

/* اطلاعات فایل */
.file-info {
    text-align: center;
}

.file-name {
    font-size: 0.8rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.file-size {
    font-size: 0.7rem;
    color: #6c757d;
    font-weight: 400;
}

/* دکمه حذف */
.btn-remove-file {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #dc3545;
    color: white;
    border: 2px solid white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-remove-file:hover {
    background-color: #c82333;
    transform: scale(1.1);
}

/* اطلاعات آپلودر */
.file-uploader-info {
    margin-top: 0.5rem;
}

.file-uploader-info small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* حالت خطا */
.file-item.error {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.file-item.error .file-name {
    color: #721c24;
}

/* حالت در حال آپلود */
.file-item.uploading {
    opacity: 0.7;
}

.file-item.uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,123,255,0.1);
    border-radius: 8px;
}

/* انیمیشن لودینگ */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ریسپانسیو */
@media (max-width: 768px) {
    .existing-files-grid,
    .new-files-preview {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
    }

    .file-drop-area {
        padding: 1.5rem 1rem;
    }

    .drop-icon {
        font-size: 1.5rem;
    }

    .file-thumbnail {
        height: 60px;
    }

    .drop-text {
        font-size: 0.8rem;
    }

    .btn-select-files {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .existing-files-grid,
    .new-files-preview {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    }

    .file-thumbnail {
        height: 50px;
    }

    .file-name {
        font-size: 0.7rem;
    }

    .file-size {
        font-size: 0.6rem;
    }
}

/* مودال نمایش تصویر */
.image-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    overflow: hidden;
}

/* جلوگیری از اسکرول صفحه پشت مودال */
body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

.image-modal-content {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 700px;
    max-height: 80%;
    object-fit: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.image-modal-close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10000;
}

.image-modal-close:hover,
.image-modal-close:focus {
    color: #bbb;
    text-decoration: none;
}

/* واکنشگرایی مودال تصویر */
@media (max-width: 768px) {
    .image-modal-content {
        width: 95%;
        max-width: none;
        max-height: 70%;
    }

    .image-modal-close {
        top: 10px;
        right: 15px;
        font-size: 30px;
    }
}
