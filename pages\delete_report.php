<?php
require_once '../includes/auth.php';
require_once '../config/db.php';
require_once '../includes/PermissionSystem.php';

// بررسی مجوز دسترسی
$permissionSystem = new PermissionSystem($pdo);
if (!$permissionSystem->hasPermission($_SESSION['user_id'], 'report_builder', 'delete')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'عدم دسترسی']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'متد نامعتبر']);
    exit;
}

$reportId = $_POST['id'] ?? null;

if (!$reportId) {
    echo json_encode(['success' => false, 'message' => 'شناسه گزارش الزامی است']);
    exit;
}

try {
    $pdo->beginTransaction();
    
    // بررسی مالکیت گزارش
    $stmt = $pdo->prepare("
        SELECT id FROM saved_reports 
        WHERE id = ? AND (created_by = ? OR ? IN (SELECT id FROM users WHERE role = 'admin'))
    ");
    $stmt->execute([$reportId, $_SESSION['user_id'], $_SESSION['user_id']]);
    if (!$stmt->fetch()) {
        throw new Exception('گزارش یافت نشد یا شما مجوز حذف آن را ندارید');
    }
    
    // حذف ویجت‌های مرتبط از داشبورد
    $stmt = $pdo->prepare("DELETE FROM dashboard_widgets WHERE report_id = ?");
    $stmt->execute([$reportId]);
    
    // حذف گزارش
    $stmt = $pdo->prepare("DELETE FROM saved_reports WHERE id = ?");
    $stmt->execute([$reportId]);
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'گزارش با موفقیت حذف شد'
    ]);
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo json_encode([
        'success' => false,
        'message' => 'خطا در حذف گزارش: ' . $e->getMessage()
    ]);
}
?>
