// مدیریت گزارش‌ها - JavaScript

class ReportsManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // مشاهده گزارش
        $('.view-report').on('click', (e) => {
            const reportId = $(e.target).closest('.view-report').data('report-id');
            this.viewReport(reportId);
        });

        // افزودن به داشبورد
        $('.add-to-dashboard').on('click', (e) => {
            const reportId = $(e.target).closest('.add-to-dashboard').data('report-id');
            this.showDashboardModal(reportId);
        });

        // ویرایش گزارش
        $('.edit-report').on('click', (e) => {
            const reportId = $(e.target).closest('.edit-report').data('report-id');
            this.editReport(reportId);
        });

        // حذف گزارش
        $('.delete-report').on('click', (e) => {
            const reportId = $(e.target).closest('.delete-report').data('report-id');
            this.deleteReport(reportId);
        });

        // استفاده از قالب
        $('.use-template').on('click', (e) => {
            const templateId = $(e.target).closest('.use-template').data('template-id');
            this.useTemplate(templateId);
        });

        // حذف قالب
        $('.delete-template').on('click', (e) => {
            const templateId = $(e.target).closest('.delete-template').data('template-id');
            this.deleteTemplate(templateId);
        });

        // ذخیره در داشبورد
        $('#saveToDashboard').on('click', () => {
            this.saveToDashboard();
        });
    }

    viewReport(reportId) {
        $.ajax({
            url: 'get_report.php',
            method: 'GET',
            data: { id: reportId },
            success: (response) => {
                if (response.success) {
                    this.displayReportInModal(response.data, response.config);
                } else {
                    this.showToast('خطا در بارگذاری گزارش: ' + response.message, 'danger');
                }
            },
            error: () => {
                this.showToast('خطا در ارتباط با سرور', 'danger');
            }
        });
    }

    displayReportInModal(data, config) {
        const modalContent = $('#modalReportContent');
        modalContent.empty();

        if (!data || data.length === 0) {
            modalContent.html('<p class="text-center">داده‌ای یافت نشد</p>');
            $('#reportModal').modal('show');
            return;
        }

        switch (config.display_type) {
            case 'table':
                this.displayTableInModal(data);
                break;
            case 'chart':
                this.displayChartInModal(data, config);
                break;
            case 'card':
                this.displayCardsInModal(data);
                break;
            case 'kpi':
                this.displayKPIInModal(data);
                break;
        }

        $('#reportModal').modal('show');
    }

    displayTableInModal(data) {
        const headers = Object.keys(data[0]);
        let tableHtml = '<div class="table-responsive"><table class="table table-striped table-hover">';
        
        // سرتیتر
        tableHtml += '<thead class="thead-dark"><tr>';
        headers.forEach(header => {
            tableHtml += `<th>${header}</th>`;
        });
        tableHtml += '</tr></thead>';
        
        // داده‌ها
        tableHtml += '<tbody>';
        data.forEach(row => {
            tableHtml += '<tr>';
            headers.forEach(header => {
                tableHtml += `<td>${row[header] || ''}</td>`;
            });
            tableHtml += '</tr>';
        });
        tableHtml += '</tbody></table></div>';
        
        $('#modalReportContent').html(tableHtml);
    }

    displayChartInModal(data, config) {
        const canvas = $('<canvas id="modalChart" width="400" height="200"></canvas>');
        $('#modalReportContent').html(canvas);
        
        const chartData = this.prepareChartData(data);
        const chartType = config.chart_type || 'bar';
        
        new Chart(document.getElementById('modalChart'), {
            type: chartType,
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });
    }

    displayCardsInModal(data) {
        let cardsHtml = '<div class="row">';
        
        data.forEach(row => {
            const headers = Object.keys(row);
            cardsHtml += `
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">${row[headers[0]]}</h5>
                            ${headers.slice(1).map(header => 
                                `<p class="card-text"><strong>${header}:</strong> ${row[header]}</p>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            `;
        });
        
        cardsHtml += '</div>';
        $('#modalReportContent').html(cardsHtml);
    }

    displayKPIInModal(data) {
        let kpiHtml = '<div class="row">';
        
        if (data && data.length > 0) {
            const row = data[0];
            Object.entries(row).forEach(([key, value]) => {
                kpiHtml += `
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h2 class="text-primary">${value}</h2>
                                <p class="card-text">${key}</p>
                            </div>
                        </div>
                    </div>
                `;
            });
        }
        
        kpiHtml += '</div>';
        $('#modalReportContent').html(kpiHtml);
    }

    prepareChartData(data) {
        if (!data || data.length === 0) return { labels: [], datasets: [] };
        
        const headers = Object.keys(data[0]);
        const labelField = headers[0];
        const valueField = headers[1];
        
        return {
            labels: data.map(row => row[labelField]),
            datasets: [{
                label: valueField,
                data: data.map(row => row[valueField]),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        };
    }

    showDashboardModal(reportId) {
        $('#dashboardReportId').val(reportId);
        $('#dashboardModal').modal('show');
    }

    saveToDashboard() {
        const reportId = $('#dashboardReportId').val();
        const widgetType = $('#widgetType').val();
        const width = $('#widgetWidth').val();
        const height = $('#widgetHeight').val();

        $.ajax({
            url: 'add_to_dashboard.php',
            method: 'POST',
            data: {
                report_id: reportId,
                widget_type: widgetType,
                width: width,
                height: height
            },
            success: (response) => {
                if (response.success) {
                    this.showToast('گزارش با موفقیت به داشبورد اضافه شد', 'success');
                    $('#dashboardModal').modal('hide');
                } else {
                    this.showToast('خطا در افزودن به داشبورد: ' + response.message, 'danger');
                }
            },
            error: () => {
                this.showToast('خطا در ارتباط با سرور', 'danger');
            }
        });
    }

    editReport(reportId) {
        // انتقال به صفحه سازنده گزارش با پارامتر ویرایش
        window.location.href = `report_builder.php?edit=${reportId}`;
    }

    deleteReport(reportId) {
        if (!confirm('آیا از حذف این گزارش اطمینان دارید؟')) {
            return;
        }

        $.ajax({
            url: 'delete_report.php',
            method: 'POST',
            data: { id: reportId },
            success: (response) => {
                if (response.success) {
                    this.showToast('گزارش با موفقیت حذف شد', 'success');
                    location.reload();
                } else {
                    this.showToast('خطا در حذف گزارش: ' + response.message, 'danger');
                }
            },
            error: () => {
                this.showToast('خطا در ارتباط با سرور', 'danger');
            }
        });
    }

    useTemplate(templateId) {
        // انتقال به صفحه سازنده گزارش با قالب انتخاب شده
        window.location.href = `report_builder.php?template=${templateId}`;
    }

    deleteTemplate(templateId) {
        if (!confirm('آیا از حذف این قالب اطمینان دارید؟')) {
            return;
        }

        $.ajax({
            url: 'delete_template.php',
            method: 'POST',
            data: { id: templateId },
            success: (response) => {
                if (response.success) {
                    this.showToast('قالب با موفقیت حذف شد', 'success');
                    location.reload();
                } else {
                    this.showToast('خطا در حذف قالب: ' + response.message, 'danger');
                }
            },
            error: () => {
                this.showToast('خطا در ارتباط با سرور', 'danger');
            }
        });
    }

    showToast(message, type) {
        // نمایش پیام toast
        const toast = $(`
            <div class="toast toast-${type}">
                ${message}
            </div>
        `);
        
        if (!$('.toast-container').length) {
            $('body').append('<div class="toast-container"></div>');
        }
        
        $('.toast-container').append(toast);
        
        setTimeout(() => {
            toast.addClass('show');
        }, 100);
        
        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
}

// راه‌اندازی مدیریت گزارش‌ها
$(document).ready(() => {
    new ReportsManager();
});
