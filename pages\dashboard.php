<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
$pdo = db_connect();

// بررسی دسترسی به صفحه داشبورد
require_page_access('dashboard', 'view');

include '../includes/header.php';

$current_user_id = current_user_id();

// بررسی مجوز مشاهده آمار
$show_statistics = has_permission('dashboard', 'view_statistics');

if ($show_statistics) {
    // گرفتن تعداد کل دستگاه‌ها
    $totalDevices = $pdo->query("SELECT COUNT(*) FROM devices")->fetchColumn();

    // گرفتن تعداد کل فعالیت‌ها
    $totalActivities = $pdo->query("SELECT COUNT(*) FROM activities")->fetchColumn();

    // گرفتن تعداد کل کاربران
    $totalUsers = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
} else {
    $totalDevices = 0;
    $totalActivities = 0;
    $totalUsers = 0;
}

// بررسی مجوز مشاهده فعالیت‌ها
$recentLogs = [];
if (has_permission('dashboard', 'view_all_activities')) {
    // مشاهده همه فعالیت‌ها
    $recentLogsStmt = $pdo->prepare("
        SELECT l.*, d.name AS device_name, a.activity_name AS activity_title
        FROM logs l
        JOIN devices d ON l.device_id = d.id
        JOIN activities a ON l.activity_id = a.id
        ORDER BY l.activity_date DESC
        LIMIT 5
    ");
    $recentLogsStmt->execute();
    $recentLogs = $recentLogsStmt->fetchAll(PDO::FETCH_ASSOC);
} elseif (has_permission('dashboard', 'view_own_activities')) {
    // مشاهده فعالیت‌های خود
    $recentLogsStmt = $pdo->prepare("
        SELECT l.*, d.name AS device_name, a.activity_name AS activity_title
        FROM logs l
        JOIN devices d ON l.device_id = d.id
        JOIN activities a ON l.activity_id = a.id
        WHERE l.user_id = ?
        ORDER BY l.activity_date DESC
        LIMIT 5
    ");
    $recentLogsStmt->execute([$current_user_id]);
    $recentLogs = $recentLogsStmt->fetchAll(PDO::FETCH_ASSOC);
}

// هشدار برای دستگاه‌ها - اگر گزارشی بیش از 7 روز نداشته باشند هشدار بده
$deviceAlertsStmt = $pdo->prepare("
    SELECT d.id, d.name AS device_name, MAX(l.activity_date) AS last_report
    FROM devices d
    LEFT JOIN logs l ON d.id = l.device_id
    GROUP BY d.id
");
$deviceAlertsStmt->execute();
$devicesWithLastReport = $deviceAlertsStmt->fetchAll(PDO::FETCH_ASSOC);

$deviceAlerts = [];
$now = new DateTime();
foreach ($devicesWithLastReport as $device) {
    if (empty($device['last_report'])) {
        $deviceAlerts[] = [
            'device_name' => $device['device_name'],
            'message' => 'هیچ گزارشی ثبت نشده است.',
            'created_at' => '—'
        ];
    } else {
        $lastReportDate = new DateTime($device['last_report']);
        $interval = $now->diff($lastReportDate)->days;
        if ($interval > 7) {
            $deviceAlerts[] = [
                'device_name' => $device['device_name'],
                'message' => "گزارشی از این دستگاه بیش از {$interval} روز است ثبت نشده.",
                'created_at' => $device['last_report']
            ];
        }
    }
}
?>

<h2>داشبورد مدیریت</h2>

<div class="dashboard-container">

    <?php if ($show_statistics): ?>
    <div class="dashboard-cards">
        <div class="card card-devices" tabindex="0" aria-label="تعداد دستگاه‌ها">
            <div class="card-icon" aria-hidden="true">🖥️</div>
            <div class="card-info">
                <h3>تعداد دستگاه‌ها</h3>
                <p><?= htmlspecialchars($totalDevices) ?></p>
            </div>
        </div>

        <div class="card card-activities" tabindex="0" aria-label="تعداد فعالیت‌ها">
            <div class="card-icon" aria-hidden="true">⚙️</div>
            <div class="card-info">
                <h3>تعداد فعالیت‌ها</h3>
                <p><?= htmlspecialchars($totalActivities) ?></p>
            </div>
        </div>

        <div class="card card-users" tabindex="0" aria-label="تعداد کاربران">
            <div class="card-icon" aria-hidden="true">👥</div>
            <div class="card-info">
                <h3>تعداد کاربران</h3>
                <p><?= htmlspecialchars($totalUsers) ?></p>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="alert alert-info">
        <strong>توجه:</strong> شما مجوز مشاهده آمار سیستم را ندارید.
    </div>
    <?php endif; ?>
    </div>

    <div class="dashboard-lower-row">
        <?php if (has_permission('dashboard', 'view_all_activities') || has_permission('dashboard', 'view_own_activities')): ?>
        <section class="box recent-logs-box" aria-labelledby="recent-logs-title">
            <h3 id="recent-logs-title">آخرین گزارش‌ها</h3>
            <?php if (empty($recentLogs)): ?>
                <p>گزارشی ثبت نشده است.</p>
            <?php else: ?>
                <ul>
                    <?php foreach ($recentLogs as $log): ?>
                        <li>
                            <strong><?= htmlspecialchars($log['device_name']) ?></strong> - <?= htmlspecialchars($log['activity_title']) ?>
                            <br>
                            <small><?= htmlspecialchars($log['activity_date']) ?></small>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </section>
        <?php else: ?>
        <section class="box recent-logs-box" aria-labelledby="recent-logs-title">
            <h3 id="recent-logs-title">آخرین گزارش‌ها</h3>
            <div class="alert alert-warning">
                <strong>توجه:</strong> شما مجوز مشاهده فعالیت‌ها را ندارید.
            </div>
        </section>
        <?php endif; ?>

        <section class="box alerts-box" aria-labelledby="alerts-title">
            <h3 id="alerts-title">هشدار دستگاه‌ها</h3>
            <?php if (empty($deviceAlerts)): ?>
                <p>هشدار فعالی وجود ندارد.</p>
            <?php else: ?>
                <ul>
                    <?php foreach ($deviceAlerts as $alert): ?>
                        <li>
                            <strong><?= htmlspecialchars($alert['device_name']) ?></strong>: <?= htmlspecialchars($alert['message']) ?>
                            <br>
                            <small><?= htmlspecialchars($alert['created_at']) ?></small>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </section>
    </div>

</div>

<?php include '../includes/footer.php'; ?>
