<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== تحلیل مجوزهای مشاهده محدود ===\n\n";
    
    // یافتن مجوزهای view_all و view_own
    $view_permissions = $pdo->query("
        SELECT sp.name as page_name, p.name as permission_name, p.display_name
        FROM permissions p
        JOIN system_pages sp ON p.page_id = sp.id
        WHERE p.name LIKE '%view_%' AND p.name != 'view'
        ORDER BY sp.name, p.name
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "مجوزهای مشاهده محدود موجود:\n";
    echo "================================\n";
    
    foreach ($view_permissions as $perm) {
        echo "{$perm['page_name']}: {$perm['permission_name']} ({$perm['display_name']})\n";
    }
    
    echo "\n=== بررسی پیاده‌سازی در صفحات ===\n\n";
    
    // بررسی work_order.php
    echo "1. صفحه work_order.php:\n";
    echo "مجوزهای موجود: view_all, view_own\n";
    
    $work_order_content = file_get_contents('pages/work_order.php');
    $has_view_all_check = strpos($work_order_content, 'view_all') !== false;
    $has_view_own_check = strpos($work_order_content, 'view_own') !== false;
    $has_user_filter = strpos($work_order_content, 'requester_id') !== false || 
                       strpos($work_order_content, 'assignees') !== false;
    
    echo "✓ بررسی مجوز view_all: " . ($has_view_all_check ? "موجود" : "❌ موجود نیست") . "\n";
    echo "✓ بررسی مجوز view_own: " . ($has_view_own_check ? "موجود" : "❌ موجود نیست") . "\n";
    echo "✓ فیلتر کاربر: " . ($has_user_filter ? "موجود" : "❌ موجود نیست") . "\n";
    echo "نتیجه: ❌ مجوزهای محدود پیاده‌سازی نشده‌اند\n\n";
    
    // بررسی reports_list.php
    echo "2. صفحه reports_list.php:\n";
    echo "مجوزهای موجود: view_all, view_own\n";
    
    $reports_content = file_get_contents('pages/reports_list.php');
    $has_view_all_check = strpos($reports_content, 'view_all') !== false;
    $has_view_own_check = strpos($reports_content, 'view_own') !== false;
    $has_reporter_filter = strpos($reports_content, 'reported_by_id') !== false;
    
    echo "✓ بررسی مجوز view_all: " . ($has_view_all_check ? "موجود" : "❌ موجود نیست") . "\n";
    echo "✓ بررسی مجوز view_own: " . ($has_view_own_check ? "موجود" : "❌ موجود نیست") . "\n";
    echo "✓ فیلتر گزارش‌دهنده: " . ($has_reporter_filter ? "موجود اما استفاده نشده" : "❌ موجود نیست") . "\n";
    echo "نتیجه: ❌ مجوزهای محدود پیاده‌سازی نشده‌اند\n\n";
    
    // بررسی my_tasks.php
    echo "3. صفحه my_tasks.php:\n";
    echo "مجوزهای موجود: view\n";
    
    $my_tasks_content = file_get_contents('pages/my_tasks.php');
    $has_user_filter = strpos($my_tasks_content, 'woa.user_id = ?') !== false;
    
    echo "✓ فیلتر کاربر: " . ($has_user_filter ? "✅ موجود و کار می‌کند" : "❌ موجود نیست") . "\n";
    echo "نتیجه: ✅ فقط وظایف کاربر نمایش داده می‌شود\n\n";
    
    // بررسی dashboard.php
    echo "4. صفحه dashboard.php:\n";
    echo "مجوزهای موجود: view_all_activities, view_own_activities, view_statistics\n";
    
    $dashboard_content = file_get_contents('pages/dashboard.php');
    $has_view_all_activities = strpos($dashboard_content, 'view_all_activities') !== false;
    $has_view_own_activities = strpos($dashboard_content, 'view_own_activities') !== false;
    $has_view_statistics = strpos($dashboard_content, 'view_statistics') !== false;
    
    echo "✓ بررسی مجوز view_all_activities: " . ($has_view_all_activities ? "موجود" : "❌ موجود نیست") . "\n";
    echo "✓ بررسی مجوز view_own_activities: " . ($has_view_own_activities ? "موجود" : "❌ موجود نیست") . "\n";
    echo "✓ بررسی مجوز view_statistics: " . ($has_view_statistics ? "موجود" : "❌ موجود نیست") . "\n";
    echo "نتیجه: ❌ مجوزهای محدود پیاده‌سازی نشده‌اند\n\n";
    
    // بررسی user_management.php
    echo "5. صفحه user_management.php:\n";
    echo "مجوزهای موجود: view_all_profiles, edit_all_profiles, edit_own_profile\n";
    
    $user_mgmt_content = file_get_contents('pages/user_management.php');
    $has_view_all_profiles = strpos($user_mgmt_content, 'view_all_profiles') !== false;
    $has_edit_all_profiles = strpos($user_mgmt_content, 'edit_all_profiles') !== false;
    $has_edit_own_profile = strpos($user_mgmt_content, 'edit_own_profile') !== false;
    
    echo "✓ بررسی مجوز view_all_profiles: " . ($has_view_all_profiles ? "موجود" : "❌ موجود نیست") . "\n";
    echo "✓ بررسی مجوز edit_all_profiles: " . ($has_edit_all_profiles ? "موجود" : "❌ موجود نیست") . "\n";
    echo "✓ بررسی مجوز edit_own_profile: " . ($has_edit_own_profile ? "موجود" : "❌ موجود نیست") . "\n";
    echo "نتیجه: ❌ مجوزهای محدود پیاده‌سازی نشده‌اند\n\n";
    
    echo "=== خلاصه مشکلات ===\n";
    echo "1. صفحه work_order.php همه دستورکارها را برای همه کاربران نشان می‌دهد\n";
    echo "2. صفحه reports_list.php همه گزارش‌ها را برای همه کاربران نشان می‌دهد\n";
    echo "3. صفحه dashboard.php مجوزهای محدود را بررسی نمی‌کند\n";
    echo "4. صفحه user_management.php مجوزهای محدود را بررسی نمی‌کند\n";
    echo "5. فقط صفحه my_tasks.php درست کار می‌کند\n\n";
    
    echo "=== راه‌حل پیشنهادی ===\n";
    echo "نیاز به اصلاح صفحات برای پیاده‌سازی مجوزهای محدود\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
