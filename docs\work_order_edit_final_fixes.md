# اصلاحات نهایی صفحه ویرایش دستور کار

## مشکلات اصلاح شده

### 1. امکان حذف فایل در قسمت ویرایش

**مشکل**: امکان حذف فایل‌های ضمیمه موجود وجود نداشت

**راه‌حل**:
- اضافه کردن دکمه "حذف" برای هر فایل موجود
- پیاده‌سازی AJAX برای حذف فایل
- حذف فایل از دیتابیس و سرور
- نمایش پیام تأیید قبل از حذف

### 2. حذف آیکون از دکمه ذخیره در حالت عادی

**مشکل**: دکمه ذخیره در حالت عادی آیکون spinner داشت

**راه‌حل**:
- تغییر ترتیب المنت‌ها در HTML
- نمایش فقط متن در حالت عادی
- نمایش spinner فقط هنگام loading

### 3. مشکل نمایش پیام موفقیت

**مشکل**: 
- پیام موفقیت بعد از ذخیره نمایش داده نمی‌شد
- هنگام ورود مجدد به صفحه، پیام دو بار نمایش داده می‌شد

**راه‌حل**:
- اضافه کردن فلگ `skipFooterToast` برای جلوگیری از نمایش دوباره
- مدیریت صحیح نمایش پیام در footer و صفحه ویرایش

## تغییرات انجام شده

### فایل `pages/work_order_edit.php`

#### 1. اضافه کردن مدیریت AJAX برای حذف فایل:
```php
// مدیریت درخواست AJAX برای حذف فایل
if (isset($_POST['action']) && $_POST['action'] === 'delete_attachment') {
    header('Content-Type: application/json');
    
    try {
        $attachment_id = filter_input(INPUT_POST, 'attachment_id', FILTER_VALIDATE_INT);
        if (!$attachment_id) {
            throw new Exception('شناسه فایل نامعتبر است.');
        }
        
        // دریافت اطلاعات فایل
        $stmt = $pdo->prepare("SELECT file_path FROM work_order_attachments WHERE id = ?");
        $stmt->execute([$attachment_id]);
        $attachment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$attachment) {
            throw new Exception('فایل یافت نشد.');
        }
        
        // حذف فایل از دیتابیس
        $stmt = $pdo->prepare("DELETE FROM work_order_attachments WHERE id = ?");
        $stmt->execute([$attachment_id]);
        
        // حذف فایل از سرور
        if (file_exists($attachment['file_path'])) {
            unlink($attachment['file_path']);
        }
        
        echo json_encode(['success' => true, 'message' => 'فایل با موفقیت حذف شد.']);
        exit;
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}
```

#### 2. اصلاح HTML دکمه ذخیره:
```html
<!-- قبل -->
<button type="submit" id="save-changes-btn" class="btn btn-primary">
    <span id="spinner" class="spinner d-none me-2"></span>
    <span id="button-text">ذخیره تغییرات</span>
</button>

<!-- بعد -->
<button type="submit" id="save-changes-btn" class="btn btn-primary">
    <span id="button-text">ذخیره تغییرات</span>
    <span id="spinner" class="spinner d-none ms-2"></span>
</button>
```

#### 3. اضافه کردن دکمه حذف برای فایل‌ها:
```html
<a href="<?= htmlspecialchars($attachment['file_path']) ?>" target="_blank" class="btn btn-sm btn-info me-2">مشاهده</a>
<button type="button" class="btn btn-sm btn-danger" onclick="deleteAttachment(<?= $attachment['id'] ?>)">حذف</button>
```

#### 4. JavaScript برای حذف فایل:
```javascript
function deleteAttachment(attachmentId) {
    if (!confirm('آیا از حذف این فایل مطمئن هستید؟')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'delete_attachment');
    formData.append('attachment_id', attachmentId);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // حذف المنت از DOM
            const attachmentElement = document.querySelector(`[data-id="${attachmentId}"]`);
            if (attachmentElement) {
                attachmentElement.remove();
            }
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('خطا در حذف فایل', 'danger');
    });
}
```

#### 5. مدیریت نمایش پیام:
```javascript
// جلوگیری از نمایش دوباره پیام در footer
window.skipFooterToast = true;
```

### فایل `includes/footer.php`

#### اصلاح مدیریت toast:
```javascript
// Show toast on page load if message exists in session
document.addEventListener('DOMContentLoaded', function() {
    // بررسی اینکه آیا صفحه خودش پیام را مدیریت می‌کند
    if (window.skipFooterToast) {
        return;
    }
    
    // Check if there's a toast message from PHP session
    const toastMessage = document.querySelector('meta[name="toast-message"]');
    const toastType = document.querySelector('meta[name="toast-type"]');

    if (toastMessage && toastMessage.content) {
        showToast(toastMessage.content, toastType ? toastType.content : 'info', 5000);
    }
});
```

## نتیجه

### قبل از اصلاح:
- ❌ امکان حذف فایل‌های موجود وجود نداشت
- ❌ دکمه ذخیره همیشه آیکون spinner داشت
- ❌ پیام موفقیت نمایش داده نمی‌شد یا دو بار نمایش داده می‌شد

### بعد از اصلاح:
- ✅ امکان حذف فایل‌های موجود با تأیید کاربر
- ✅ دکمه ذخیره در حالت عادی بدون آیکون
- ✅ نمایش صحیح پیام‌های موفقیت و خطا
- ✅ مدیریت بهتر حالت‌های loading
- ✅ تجربه کاربری بهبود یافته

## ویژگی‌های جدید

1. **حذف فایل با AJAX**: بدون نیاز به refresh صفحه
2. **تأیید حذف**: نمایش پیام تأیید قبل از حذف فایل
3. **مدیریت خطا**: نمایش پیام‌های خطای مناسب
4. **UI بهبود یافته**: دکمه‌های واضح‌تر و حالت‌های مناسب
5. **جلوگیری از نمایش دوباره پیام**: مدیریت صحیح toast notifications
