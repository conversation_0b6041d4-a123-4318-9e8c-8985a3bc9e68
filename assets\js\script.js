document.addEventListener('DOMContentLoaded', function() {
    const outsourcingButton = document.getElementById('outsourcingButton');
    const modal = document.getElementById('outsourcingModal');
    const modalContent = document.getElementById('modalContent');

    outsourcingButton.addEventListener('click', function() {
        fetch(`/pages/work_order_details.php?id=${workOrderId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const outsourcingDetails = data.outsourcing_details;
                    modalContent.innerHTML = '';
                    outsourcingDetails.forEach(detail => {
                        const detailElement = document.createElement('p');
                        detailElement.textContent = `شرکت: ${detail.company_name}, هزینه: ${detail.cost}`;
                        modalContent.appendChild(detailElement);
                    });
                    modal.style.display = 'block';
                } else {
                    alert('خطا در واکشی اطلاعات برون سپاری');
                }
            });
    });

    document.getElementById('closeModal').addEventListener('click', function() {
        modal.style.display = 'none';
    });
});