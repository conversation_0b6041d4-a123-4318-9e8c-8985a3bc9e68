<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== بررسی وضعیت صفحه repair_approvals در منو ===\n\n";
    
    // بررسی وجود صفحه در system_pages
    $page_check = $pdo->query("SELECT * FROM system_pages WHERE name = 'repair_approvals' OR file_path = 'repair_approvals.php'")->fetch(PDO::FETCH_ASSOC);
    
    if ($page_check) {
        echo "✅ صفحه repair_approvals در جدول system_pages موجود است:\n";
        echo "   - نام: {$page_check['name']}\n";
        echo "   - نام نمایشی: {$page_check['display_name']}\n";
        echo "   - مسیر فایل: {$page_check['file_path']}\n";
        echo "   - آیکون: {$page_check['icon']}\n";
        echo "   - فعال: " . ($page_check['is_active'] ? 'بله' : 'خیر') . "\n";
    } else {
        echo "❌ صفحه repair_approvals در جدول system_pages موجود نیست\n";
        echo "نیاز به اضافه کردن آن به جدول system_pages\n";
    }
    
    // بررسی مجوزهای موجود برای این صفحه
    if ($page_check) {
        $permissions = $pdo->prepare("SELECT * FROM permissions WHERE page_id = ?");
        $permissions->execute([$page_check['id']]);
        $perms = $permissions->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\nمجوزهای موجود:\n";
        if ($perms) {
            foreach ($perms as $perm) {
                echo "   - {$perm['name']} ({$perm['display_name']})\n";
            }
        } else {
            echo "   هیچ مجوزی تعریف نشده\n";
        }
    }
    
    // بررسی کاربرانی که دسترسی دارند
    if ($page_check) {
        $users_with_access = $pdo->prepare("
            SELECT DISTINCT u.username, r.display_name as role_name
            FROM users u
            JOIN roles r ON u.role_id = r.id
            JOIN role_permissions rp ON r.id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE p.page_id = ? AND p.name = 'view'
        ");
        $users_with_access->execute([$page_check['id']]);
        $users = $users_with_access->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\nکاربران با دسترسی:\n";
        if ($users) {
            foreach ($users as $user) {
                echo "   - {$user['username']} (نقش: {$user['role_name']})\n";
            }
        } else {
            echo "   هیچ کاربری دسترسی ندارد\n";
        }
    }
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
