<?php
/**
 * کامپوننت یکپارچه آپلود فایل
 * برای استفاده در تمام صفحات سیستم
 */

function render_file_uploader($config = []) {
    // تنظیمات پیش‌فرض
    $defaults = [
        'id' => 'fileUploader',
        'name' => 'files[]',
        'accept' => 'image/*,.pdf',
        'max_size' => 2, // مگابایت
        'max_files' => 10,
        'existing_files' => [],
        'show_existing' => true,
        'label' => 'فایل‌ها',
        'description' => 'فایل‌های عکس و PDF - حداکثر 2 مگابایت',
        'drag_text' => 'فایل‌ها را اینجا بکشید و رها کنید یا کلیک کنید',
        'button_text' => 'انتخاب فایل‌ها'
    ];
    
    $config = array_merge($defaults, $config);
    $uploader_id = $config['id'];
    
    ob_start();
    ?>
    
    <div class="file-uploader-container" id="<?= $uploader_id ?>Container">
        <label class="file-uploader-label"><?= htmlspecialchars($config['label']) ?>:</label>
        
        <?php if ($config['show_existing'] && !empty($config['existing_files'])): ?>
        <div class="existing-files-section">
            <div class="existing-files-grid" id="<?= $uploader_id ?>ExistingFiles">
                <?php foreach ($config['existing_files'] as $file): ?>
                    <?php render_file_item($file, $uploader_id, true); ?>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="file-drop-area" id="<?= $uploader_id ?>DropArea">
            <div class="drop-area-content">
                <i class="fas fa-cloud-upload-alt drop-icon"></i>
                <p class="drop-text"><?= htmlspecialchars($config['drag_text']) ?></p>
                <button type="button" class="btn-select-files" id="<?= $uploader_id ?>SelectBtn">
                    <?= htmlspecialchars($config['button_text']) ?>
                </button>
            </div>
            <input type="file" 
                   id="<?= $uploader_id ?>Input" 
                   name="<?= htmlspecialchars($config['name']) ?>" 
                   accept="<?= htmlspecialchars($config['accept']) ?>" 
                   multiple 
                   style="display: none;">
        </div>
        
        <div class="new-files-preview" id="<?= $uploader_id ?>Preview"></div>
        
        <div class="file-uploader-info">
            <small><?= htmlspecialchars($config['description']) ?></small>
        </div>
    </div>
    
    <script>
    // راه‌اندازی آپلودر پس از بارگذاری کامل صفحه
    document.addEventListener('DOMContentLoaded', function() {
        // تاخیر کوتاه برای اطمینان از بارگذاری کلاس FileUploader
        setTimeout(function() {
            const uploaderId = '<?= $uploader_id ?>';
            const config = <?= json_encode($config, JSON_HEX_APOS | JSON_HEX_QUOT) ?>;

            console.log('Initializing FileUploader for:', uploaderId);

            // ایجاد instance آپلودر
            if (typeof FileUploader !== 'undefined') {
                try {
                    window.fileUploaders = window.fileUploaders || {};
                    window.fileUploaders[uploaderId] = new FileUploader(uploaderId, config);
                    console.log('FileUploader initialized successfully for:', uploaderId);
                } catch (error) {
                    console.error('Error initializing FileUploader:', error);
                }
            } else {
                console.error('FileUploader class not loaded for: ' + uploaderId);
                // تلاش مجدد پس از 500 میلی‌ثانیه
                setTimeout(function() {
                    if (typeof FileUploader !== 'undefined') {
                        try {
                            window.fileUploaders = window.fileUploaders || {};
                            window.fileUploaders[uploaderId] = new FileUploader(uploaderId, config);
                            console.log('FileUploader initialized successfully (retry) for:', uploaderId);
                        } catch (error) {
                            console.error('Error initializing FileUploader (retry):', error);
                        }
                    } else {
                        console.error('FileUploader class still not available after retry');
                    }
                }, 500);
            }
        }, 100);
    });
    </script>
    
    <?php
    return ob_get_clean();
}

function render_file_item($file, $uploader_id, $is_existing = false) {
    $file_path = is_array($file) ? $file['path'] : $file;
    $file_name = is_array($file) ? $file['name'] : basename($file_path);
    $file_size = is_array($file) ? $file['size'] : (file_exists($file_path) ? filesize($file_path) : 0);
    $file_id = is_array($file) ? ($file['id'] ?? '') : '';

    $is_image = preg_match('/\.(jpg|jpeg|png|gif)$/i', $file_name);

    // مسیر برای نمایش فایل‌های موجود
    $display_path = $is_existing ? $file_path : '';

    ?>
    <div class="file-item <?= $is_existing ? 'existing-file' : 'new-file' ?>"
         data-file-id="<?= htmlspecialchars($file_id) ?>"
         data-file-name="<?= htmlspecialchars($file_name) ?>">

        <div class="file-thumbnail">
            <?php if ($is_image): ?>
                <img src="<?= htmlspecialchars($display_path) ?>" alt="<?= htmlspecialchars($file_name) ?>" class="thumbnail-image">
            <?php else: ?>
                <div class="file-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
            <?php endif; ?>
        </div>

        <div class="file-info">
            <div class="file-name" title="<?= htmlspecialchars($file_name) ?>">
                <?= htmlspecialchars(strlen($file_name) > 15 ? substr($file_name, 0, 12) . '...' : $file_name) ?>
            </div>
            <div class="file-size"><?= format_file_size($file_size) ?></div>
        </div>

        <button type="button" class="btn-remove-file" title="حذف فایل">
            <i class="fas fa-times"></i>
        </button>

        <?php if ($is_existing): ?>
            <input type="hidden" name="existing_files[]" value="<?= htmlspecialchars($file_path) ?>">
            <?php if ($file_id): ?>
                <input type="hidden" name="existing_file_ids[]" value="<?= htmlspecialchars($file_id) ?>">
            <?php endif; ?>
        <?php endif; ?>
    </div>
    <?php
}

function format_file_size($bytes) {
    if ($bytes == 0) return '0 B';
    
    $units = ['B', 'KB', 'MB', 'GB'];
    $factor = floor(log($bytes, 1024));
    
    return sprintf("%.1f %s", $bytes / pow(1024, $factor), $units[$factor]);
}
?>
