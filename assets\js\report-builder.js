// سازنده گزارش - JavaScript

class ReportBuilder {
    constructor() {
        this.selectedTables = [];
        this.selectedFields = [];
        this.currentConfig = {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateAvailableFields();
    }

    bindEvents() {
        // انتخاب قالب
        $('#templateSelect').on('change', (e) => {
            this.loadTemplate(e.target.value);
        });

        // انتخاب جداول
        $('.table-checkbox').on('change', (e) => {
            this.updateSelectedTables();
            this.updateAvailableFields();
        });

        // تغییر نوع نمایش
        $('#displayType').on('change', (e) => {
            this.toggleChartOptions(e.target.value);
        });

        // دکمه‌های عملیات
        $('#previewReport').on('click', () => this.previewReport());
        $('#saveReport').on('click', () => this.saveReport());
        $('#saveTemplate').on('click', () => this.saveTemplate());

        // Drag and Drop برای فیلدها
        this.initDragAndDrop();
    }

    loadTemplate(templateId) {
        if (!templateId) {
            this.resetForm();
            return;
        }

        const option = $(`#templateSelect option[value="${templateId}"]`);
        const config = JSON.parse(option.data('config') || '{}');
        
        this.currentConfig = config;
        this.applyConfig(config);
    }

    applyConfig(config) {
        // انتخاب جداول
        $('.table-checkbox').prop('checked', false);
        if (config.tables) {
            config.tables.forEach(table => {
                $(`#table_${table}`).prop('checked', true);
            });
        }

        // به‌روزرسانی فیلدهای موجود
        this.updateSelectedTables();
        this.updateAvailableFields();

        // انتخاب فیلدها
        if (config.fields) {
            setTimeout(() => {
                config.fields.forEach(field => {
                    this.addFieldToSelected(field);
                });
            }, 100);
        }

        // نوع نمایش
        if (config.display_type) {
            $('#displayType').val(config.display_type);
            this.toggleChartOptions(config.display_type);
        }

        // نوع نمودار
        if (config.chart_type) {
            $('#chartType').val(config.chart_type);
        }
    }

    updateSelectedTables() {
        this.selectedTables = [];
        $('.table-checkbox:checked').each((i, el) => {
            this.selectedTables.push($(el).val());
        });
    }

    updateAvailableFields() {
        const container = $('#availableFields');
        container.empty();

        this.selectedTables.forEach(tableName => {
            if (tableStructure[tableName]) {
                const tableDiv = $(`
                    <div class="table-fields">
                        <h6 class="table-name">${tableStructure[tableName].name}</h6>
                        <div class="fields-container"></div>
                    </div>
                `);

                const fieldsContainer = tableDiv.find('.fields-container');
                
                Object.entries(tableStructure[tableName].fields).forEach(([fieldName, fieldLabel]) => {
                    const fieldElement = $(`
                        <div class="field-item" draggable="true" data-table="${tableName}" data-field="${fieldName}">
                            <i class="fas fa-grip-vertical"></i>
                            <span>${fieldLabel}</span>
                            <small class="text-muted">(${tableName}.${fieldName})</small>
                        </div>
                    `);
                    fieldsContainer.append(fieldElement);
                });

                container.append(tableDiv);
            }
        });

        this.initDragAndDrop();
    }

    initDragAndDrop() {
        // Drag start
        $(document).off('dragstart', '.field-item').on('dragstart', '.field-item', function(e) {
            const table = $(this).data('table');
            const field = $(this).data('field');
            e.originalEvent.dataTransfer.setData('text/plain', `${table}.${field}`);
        });

        // Drop zone
        $('#selectedFields').off('dragover drop').on('dragover', function(e) {
            e.preventDefault();
        }).on('drop', (e) => {
            e.preventDefault();
            const fieldData = e.originalEvent.dataTransfer.getData('text/plain');
            this.addFieldToSelected(fieldData);
        });

        // Click to add
        $(document).off('click', '.field-item').on('click', '.field-item', (e) => {
            const table = $(e.currentTarget).data('table');
            const field = $(e.currentTarget).data('field');
            this.addFieldToSelected(`${table}.${field}`);
        });
    }

    addFieldToSelected(fieldData) {
        const [table, field] = fieldData.split('.');
        
        // بررسی تکراری نبودن
        if ($(`#selectedFields .selected-field[data-field="${fieldData}"]`).length > 0) {
            return;
        }

        const fieldLabel = tableStructure[table]?.fields[field] || field;
        const fieldElement = $(`
            <div class="selected-field" data-field="${fieldData}">
                <span>${fieldLabel}</span>
                <small class="text-muted">(${fieldData})</small>
                <button type="button" class="btn btn-sm btn-danger remove-field">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `);

        $('#selectedFields').append(fieldElement);
        this.updateSelectedFields();
    }

    updateSelectedFields() {
        this.selectedFields = [];
        $('#selectedFields .selected-field').each((i, el) => {
            this.selectedFields.push($(el).data('field'));
        });

        // حذف فیلد
        $('.remove-field').off('click').on('click', (e) => {
            $(e.target).closest('.selected-field').remove();
            this.updateSelectedFields();
        });
    }

    toggleChartOptions(displayType) {
        if (displayType === 'chart') {
            $('#chartTypeContainer').show();
        } else {
            $('#chartTypeContainer').hide();
        }
    }

    previewReport() {
        if (this.selectedFields.length === 0) {
            this.showToast('لطفاً حداقل یک فیلد انتخاب کنید', 'warning');
            return;
        }

        const config = this.buildConfig();
        
        // ارسال درخواست پیش‌نمایش
        $.ajax({
            url: 'report_preview.php',
            method: 'POST',
            data: {
                config: JSON.stringify(config)
            },
            success: (response) => {
                this.displayResults(response, config.display_type);
            },
            error: (xhr, status, error) => {
                this.showToast('خطا در تولید گزارش: ' + error, 'danger');
            }
        });
    }

    buildConfig() {
        return {
            tables: this.selectedTables,
            fields: this.selectedFields,
            display_type: $('#displayType').val(),
            chart_type: $('#chartType').val(),
            joins: this.generateJoins()
        };
    }

    generateJoins() {
        const joins = [];
        
        // تولید خودکار JOIN ها بر اساس روابط شناخته شده
        const relationships = {
            'work_orders.device_id': 'devices.id',
            'work_orders.requester_id': 'users.id',
            'breakdown_reports.device_id': 'devices.id',
            'breakdown_reports.reported_by_id': 'users.id',
            'activities.device_id': 'devices.id',
            'work_order_assignees.work_order_id': 'work_orders.id',
            'work_order_assignees.user_id': 'users.id'
        };

        // پیدا کردن JOIN های مورد نیاز
        for (let i = 0; i < this.selectedTables.length; i++) {
            for (let j = i + 1; j < this.selectedTables.length; j++) {
                const table1 = this.selectedTables[i];
                const table2 = this.selectedTables[j];
                
                // بررسی رابطه مستقیم
                Object.entries(relationships).forEach(([key, value]) => {
                    const [keyTable, keyField] = key.split('.');
                    const [valueTable, valueField] = value.split('.');
                    
                    if ((keyTable === table1 && valueTable === table2) || 
                        (keyTable === table2 && valueTable === table1)) {
                        joins.push({
                            table1: keyTable,
                            field1: keyField,
                            table2: valueTable,
                            field2: valueField
                        });
                    }
                });
            }
        }

        return joins;
    }

    displayResults(response, displayType) {
        const resultsContainer = $('#reportResults');
        const resultsCard = $('#resultsCard');
        
        resultsContainer.empty();
        
        if (response.success) {
            switch (displayType) {
                case 'table':
                    this.displayTable(response.data);
                    break;
                case 'chart':
                    this.displayChart(response.data);
                    break;
                case 'card':
                    this.displayCards(response.data);
                    break;
                case 'kpi':
                    this.displayKPI(response.data);
                    break;
            }
            resultsCard.show();
        } else {
            this.showToast('خطا: ' + response.message, 'danger');
        }
    }

    displayTable(data) {
        if (!data || data.length === 0) {
            $('#reportResults').html('<p class="text-center">داده‌ای یافت نشد</p>');
            return;
        }

        const headers = Object.keys(data[0]);
        let tableHtml = '<div class="table-responsive"><table class="table table-striped table-hover">';
        
        // سرتیتر
        tableHtml += '<thead class="thead-dark"><tr>';
        headers.forEach(header => {
            tableHtml += `<th>${header}</th>`;
        });
        tableHtml += '</tr></thead>';
        
        // داده‌ها
        tableHtml += '<tbody>';
        data.forEach(row => {
            tableHtml += '<tr>';
            headers.forEach(header => {
                tableHtml += `<td>${row[header] || ''}</td>`;
            });
            tableHtml += '</tr>';
        });
        tableHtml += '</tbody></table></div>';
        
        $('#reportResults').html(tableHtml);
    }

    displayChart(data) {
        // پیاده‌سازی نمودار با Chart.js
        const canvas = $('<canvas id="reportChart"></canvas>');
        $('#reportResults').html(canvas);
        
        // تبدیل داده‌ها برای نمودار
        const chartData = this.prepareChartData(data);
        const chartType = $('#chartType').val();
        
        new Chart(document.getElementById('reportChart'), {
            type: chartType,
            data: chartData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: $('#reportName').val() || 'گزارش'
                    }
                }
            }
        });
    }

    prepareChartData(data) {
        if (!data || data.length === 0) return { labels: [], datasets: [] };
        
        const headers = Object.keys(data[0]);
        const labelField = headers[0];
        const valueField = headers[1];
        
        return {
            labels: data.map(row => row[labelField]),
            datasets: [{
                label: valueField,
                data: data.map(row => row[valueField]),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        };
    }

    displayCards(data) {
        let cardsHtml = '<div class="row">';
        
        data.forEach(row => {
            const headers = Object.keys(row);
            cardsHtml += `
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">${row[headers[0]]}</h5>
                            ${headers.slice(1).map(header => 
                                `<p class="card-text"><strong>${header}:</strong> ${row[header]}</p>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            `;
        });
        
        cardsHtml += '</div>';
        $('#reportResults').html(cardsHtml);
    }

    displayKPI(data) {
        let kpiHtml = '<div class="row">';
        
        if (data && data.length > 0) {
            const row = data[0];
            Object.entries(row).forEach(([key, value]) => {
                kpiHtml += `
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h2 class="text-primary">${value}</h2>
                                <p class="card-text">${key}</p>
                            </div>
                        </div>
                    </div>
                `;
            });
        }
        
        kpiHtml += '</div>';
        $('#reportResults').html(kpiHtml);
    }

    saveReport() {
        const name = $('#reportName').val();
        if (!name) {
            this.showToast('لطفاً نام گزارش را وارد کنید', 'warning');
            return;
        }

        const config = this.buildConfig();
        
        $.ajax({
            url: 'save_report.php',
            method: 'POST',
            data: {
                name: name,
                config: JSON.stringify(config),
                template_id: $('#templateSelect').val() || null
            },
            success: (response) => {
                if (response.success) {
                    this.showToast('گزارش با موفقیت ذخیره شد', 'success');
                } else {
                    this.showToast('خطا در ذخیره گزارش: ' + response.message, 'danger');
                }
            },
            error: () => {
                this.showToast('خطا در ارتباط با سرور', 'danger');
            }
        });
    }

    saveTemplate() {
        const name = $('#reportName').val();
        if (!name) {
            this.showToast('لطفاً نام قالب را وارد کنید', 'warning');
            return;
        }

        const config = this.buildConfig();
        
        $.ajax({
            url: 'save_template.php',
            method: 'POST',
            data: {
                name: name,
                config: JSON.stringify(config)
            },
            success: (response) => {
                if (response.success) {
                    this.showToast('قالب با موفقیت ذخیره شد', 'success');
                    location.reload(); // بارگذاری مجدد برای نمایش قالب جدید
                } else {
                    this.showToast('خطا در ذخیره قالب: ' + response.message, 'danger');
                }
            },
            error: () => {
                this.showToast('خطا در ارتباط با سرور', 'danger');
            }
        });
    }

    resetForm() {
        $('#reportName').val('');
        $('.table-checkbox').prop('checked', false);
        $('#selectedFields').empty();
        $('#displayType').val('table');
        $('#chartType').val('bar');
        this.toggleChartOptions('table');
        this.updateSelectedTables();
        this.updateAvailableFields();
        $('#resultsCard').hide();
    }

    showToast(message, type) {
        // نمایش پیام toast
        const toast = $(`
            <div class="toast toast-${type}">
                ${message}
            </div>
        `);
        
        $('.toast-container').append(toast);
        
        setTimeout(() => {
            toast.addClass('show');
        }, 100);
        
        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
}

// راه‌اندازی سازنده گزارش
$(document).ready(() => {
    new ReportBuilder();
});
