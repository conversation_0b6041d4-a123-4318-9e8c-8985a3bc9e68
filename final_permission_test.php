<?php
require_once 'db_connection.php';

try {
    $pdo = db_connect();
    
    echo "=== تست نهایی سیستم مجوزها ===\n\n";
    
    // 1. بررسی تعداد کل مجوزها
    $total_permissions = $pdo->query("SELECT COUNT(*) FROM permissions")->fetchColumn();
    echo "1. تعداد کل مجوزها: $total_permissions\n\n";
    
    // 2. نمایش مجوزهای هر صفحه
    echo "2. مجوزهای هر صفحه:\n";
    echo "========================\n";
    
    $pages_stmt = $pdo->query("
        SELECT sp.name as page_name, sp.display_name, 
               COUNT(p.id) as permission_count
        FROM system_pages sp
        LEFT JOIN permissions p ON sp.id = p.page_id
        GROUP BY sp.id, sp.name, sp.display_name
        ORDER BY sp.name
    ");
    
    while ($page = $pages_stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "{$page['page_name']} ({$page['display_name']}): {$page['permission_count']} مجوز\n";
        
        // نمایش مجوزهای هر صفحه
        $perms_stmt = $pdo->prepare("
            SELECT name, display_name 
            FROM permissions 
            WHERE page_id = (SELECT id FROM system_pages WHERE name = ?)
            ORDER BY name
        ");
        $perms_stmt->execute([$page['page_name']]);
        
        while ($perm = $perms_stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "  - {$perm['name']} ({$perm['display_name']})\n";
        }
        echo "\n";
    }
    
    // 3. بررسی نقش‌ها و مجوزهایشان
    echo "3. مجوزهای نقش‌ها:\n";
    echo "==================\n";
    
    $roles_stmt = $pdo->query("SELECT id, name, display_name FROM roles WHERE is_active = 1 ORDER BY name");
    
    while ($role = $roles_stmt->fetch(PDO::FETCH_ASSOC)) {
        $role_perms_stmt = $pdo->prepare("
            SELECT sp.name as page_name, p.name as permission_name, p.display_name
            FROM role_permissions rp
            JOIN permissions p ON rp.permission_id = p.id
            JOIN system_pages sp ON p.page_id = sp.id
            WHERE rp.role_id = ?
            ORDER BY sp.name, p.name
        ");
        $role_perms_stmt->execute([$role['id']]);
        $role_permissions = $role_perms_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "{$role['name']} ({$role['display_name']}): " . count($role_permissions) . " مجوز\n";
        
        $current_page = '';
        foreach ($role_permissions as $perm) {
            if ($current_page !== $perm['page_name']) {
                $current_page = $perm['page_name'];
                echo "  $current_page:\n";
            }
            echo "    - {$perm['permission_name']} ({$perm['display_name']})\n";
        }
        echo "\n";
    }
    
    // 4. بررسی کاربران و نقش‌هایشان
    echo "4. کاربران و نقش‌هایشان:\n";
    echo "========================\n";
    
    $users_stmt = $pdo->query("
        SELECT u.username, u.name, r.name as role_name, r.display_name as role_display_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        WHERE u.is_active = 1
        ORDER BY u.username
    ");
    
    while ($user = $users_stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "{$user['username']} ({$user['name']}) - نقش: {$user['role_name']} ({$user['role_display_name']})\n";
    }
    
    echo "\n=== تست با موفقیت انجام شد! ===\n";
    
} catch (Exception $e) {
    echo "خطا: " . $e->getMessage() . "\n";
}
?>
